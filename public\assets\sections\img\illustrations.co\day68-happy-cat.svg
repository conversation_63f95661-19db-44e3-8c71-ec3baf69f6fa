<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Generator: Adobe Illustrator 23.1.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   id="Layer_1"
   x="0px"
   y="0px"
   viewBox="0 0 1000 1000"
   style="enable-background:new 0 0 1000 1000;"
   xml:space="preserve"
   sodipodi:docname="day68-happy-cat.svg"
   inkscape:version="0.92.5 (2060ec1f9f, 2020-04-08)"><metadata
   id="metadata300"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /></cc:Work></rdf:RDF></metadata><defs
   id="defs298" /><sodipodi:namedview
   pagecolor="#ffffff"
   bordercolor="#666666"
   borderopacity="1"
   objecttolerance="10"
   gridtolerance="10"
   guidetolerance="10"
   inkscape:pageopacity="0"
   inkscape:pageshadow="2"
   inkscape:window-width="2560"
   inkscape:window-height="1379"
   id="namedview296"
   showgrid="false"
   inkscape:zoom="1.3350176"
   inkscape:cx="437.25407"
   inkscape:cy="88.186875"
   inkscape:window-x="0"
   inkscape:window-y="0"
   inkscape:window-maximized="1"
   inkscape:current-layer="Layer_1" />
<style
   type="text/css"
   id="style2">
	.st0{fill:#F4F5F8;}
	.st1{fill:none;stroke:#E5E5E5;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st2{fill:#E5E5E5;}
	.st3{fill:none;stroke:#E5E5E5;stroke-width:4;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st4{fill:none;stroke:#E5E5E5;stroke-width:8;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st5{fill:none;stroke:#A83727;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st6{fill:#6DCCA3;}
	.st7{fill:none;stroke:#465172;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st8{fill:#2359B2;}
	.st9{opacity:0.67;fill:none;stroke:#1E2A54;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st10{fill:#D6D6D6;}
	.st11{fill:#F78A21;}
	.st12{opacity:0.26;fill:#9B463D;}
	.st13{fill:#A83727;}
	.st14{opacity:0.24;}
	.st15{fill:none;stroke:#A83727;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
</style>

<g
   id="XMLID_278_">
	<polyline
   id="XMLID_285_"
   class="st1"
   points="243.9,634.7 243.9,665.6 182,665.6  " />
	<polyline
   id="XMLID_284_"
   class="st1"
   points="305.7,665.6 243.9,665.6 243.9,634.7  " />
	<rect
   id="XMLID_283_"
   x="212.9"
   y="665.6"
   class="st1"
   width="61.9"
   height="30.9" />
	<polyline
   id="XMLID_282_"
   class="st1"
   points="261.4,727.5 243.9,727.5 243.9,696.5 305.7,696.5  " />
	<polyline
   id="XMLID_281_"
   class="st1"
   points="182,696.5 243.9,696.5 243.9,727.5 193.7,727.5  " />
	<polyline
   id="XMLID_280_"
   class="st1"
   points="160.9,665.6 212.9,665.6 212.9,696.5 182,696.5  " />
	<polyline
   id="XMLID_279_"
   class="st1"
   points="319.4,696.5 274.8,696.5 274.8,665.6 296.2,665.6  " />
</g>
<g
   id="XMLID_2_">
	<polyline
   id="XMLID_49_"
   class="st1"
   points="817.6,441.4 817.6,472.3 879.5,472.3  " />
	<polyline
   id="XMLID_38_"
   class="st1"
   points="755.8,472.3 817.6,472.3 817.6,441.4  " />
	
		<rect
   id="XMLID_37_"
   x="786.7"
   y="472.3"
   transform="matrix(-1 -4.487045e-11 4.487045e-11 -1 1635.2858 975.629)"
   class="st1"
   width="61.9"
   height="30.9" />
	<polyline
   id="XMLID_26_"
   class="st1"
   points="800.1,534.2 817.6,534.2 817.6,503.3 755.8,503.3  " />
	<polyline
   id="XMLID_8_"
   class="st1"
   points="879.5,503.3 817.6,503.3 817.6,534.2 867.8,534.2  " />
	<polyline
   id="XMLID_4_"
   class="st1"
   points="900.6,472.3 848.6,472.3 848.6,503.3 879.5,503.3  " />
	<polyline
   id="XMLID_3_"
   class="st1"
   points="742.1,503.3 786.7,503.3 786.7,472.3 765.3,472.3  " />
</g>
<g
   id="XMLID_288_">
	<circle
   id="XMLID_175_"
   class="st1"
   cx="587"
   cy="251.9"
   r="56" />
	<line
   id="XMLID_178_"
   class="st1"
   x1="587"
   y1="201.6"
   x2="587"
   y2="207" />
	<line
   id="XMLID_179_"
   class="st1"
   x1="587"
   y1="296.7"
   x2="587"
   y2="302.1" />
	<line
   id="XMLID_180_"
   class="st1"
   x1="637.3"
   y1="251.9"
   x2="631.9"
   y2="251.9" />
	<line
   id="XMLID_177_"
   class="st1"
   x1="542.2"
   y1="251.9"
   x2="536.7"
   y2="251.9" />
	<circle
   id="XMLID_181_"
   class="st2"
   cx="587"
   cy="251.9"
   r="4.5" />
	<line
   id="XMLID_183_"
   class="st3"
   x1="587"
   y1="251.9"
   x2="611.2"
   y2="222" />
	<line
   id="XMLID_184_"
   class="st1"
   x1="571.8"
   y1="236.9"
   x2="587"
   y2="251.9" />
</g>
<g
   id="XMLID_23_">
	<rect
   id="XMLID_22_"
   x="135.8"
   y="252.9"
   class="st4"
   width="123.3"
   height="125.3" />
	
		<rect
   id="XMLID_114_"
   x="154.3"
   y="271.2"
   transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 513.0942 118.1157)"
   class="st3"
   width="86.5"
   height="88.9" />
	<rect
   id="XMLID_18_"
   x="289.2"
   y="213"
   class="st4"
   width="108.6"
   height="77.8" />
	
		<rect
   id="XMLID_122_"
   x="320.6"
   y="213.5"
   transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 595.3908 -91.6892)"
   class="st3"
   width="45.9"
   height="76.7" />
	
		<rect
   id="XMLID_115_"
   x="292.7"
   y="352.6"
   transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 705.4474 47.4194)"
   class="st3"
   width="72.6"
   height="47.7" />
	<rect
   id="XMLID_17_"
   x="289.2"
   y="324.2"
   class="st4"
   width="79.6"
   height="104.5" />
</g>
<line
   id="XMLID_59_"
   class="st5"
   x1="833.8"
   y1="784.2"
   x2="841.7"
   y2="784.2" />
<line
   id="XMLID_56_"
   class="st5"
   x1="802.3"
   y1="784.2"
   x2="820.8"
   y2="784.2" />
<line
   id="XMLID_54_"
   class="st5"
   x1="473"
   y1="784.2"
   x2="788.3"
   y2="784.2" />
<line
   id="XMLID_53_"
   class="st5"
   x1="200"
   y1="784.2"
   x2="452"
   y2="784.2" />
<line
   id="XMLID_52_"
   class="st5"
   x1="156.5"
   y1="784.2"
   x2="178.5"
   y2="784.2" />
<line
   id="XMLID_57_"
   class="st5"
   x1="126.5"
   y1="784.2"
   x2="138.5"
   y2="784.2" />
<g
   id="g49">
	<path
   id="XMLID_28_"
   class="st6"
   d="M330.9,674.4c-18.2-11.6-50.3-32-64.2-74.4c-9.7-29.6-9.9-66.8,0.5-70.3   c12.1-4,33.2,38.8,40.9,35.1c6.1-2.9-6.6-29.4-8.3-74.3c-1.2-30.6,3.2-56.9,17-80.3c6.3-10.7,10.9-18.6,16-18   c20.9,2.4,15,144,37.3,146.7c10.5,1.3,18.9-29.2,30.7-26.7c10,2.1,16.7,26.8,18.5,42.8c0.1,0.8,0.2,1.4,0.2,1.9   c5.2,51.9-25.1,89.3-35.3,102c-15.3,18.8-25.6,22.6-33.6,22.7C342.2,681.5,335.8,677.6,330.9,674.4z" />
	<path
   id="XMLID_41_"
   class="st7"
   d="M325.5,477c3.5,37,24.5,93.3,27.6,141c2,31.1,0.1,58.5-3,81" />
	<path
   id="XMLID_25_"
   class="st8"
   d="M422,784.2H267l0,0c-15.6-27.6-15-61.5,1.5-88.5l22.8-37.3h106.3l22.1,34.5   c16.4,25.5,18.3,57.8,5.2,85.1L422,784.2z" />
	<path
   id="XMLID_44_"
   class="st7"
   d="M375,561.9c0,0-0.4,17-23.8,39.1" />
	<path
   id="XMLID_47_"
   class="st7"
   d="M301.1,601c0,0,15.7,32,52.8,32" />
	<path
   id="XMLID_48_"
   class="st9"
   d="M382.3,684.8c0,0,23.6,15.4,23.6,52.3" />
</g>

<g
   id="g293">
	<path
   id="XMLID_14_"
   class="st10"
   d="M523.6,794c0,29.2,49.9,53,111.4,53c48.2,0,89.2-14.5,104.7-34.9l-116.5,8.4L523.6,794z" />
	<path
   id="XMLID_15_"
   class="st11"
   d="M723.7,791.1c-2.9-0.6-6-0.3-8.7,0.8c-17.6,6.9-48.4,11.5-83.4,11.5c-54.7,0-99-11.2-99-25   c0-12.2,34.7-22.4,80.6-24.6l3.2-16.7c-62.9,3.9-110.7,23.4-110.7,46.8c0,26.2,60,47.5,134,47.5c32.4,0,62.1-4.1,85.3-10.9   C740,816,739.1,794.3,723.7,791.1L723.7,791.1z" />
	<path
   id="XMLID_51_"
   class="st12"
   d="M671.1,801.3l48-10.5c0,0,12.6,1.3,14.9,8c4,11.4-4.2,18.4-4.2,18.4S726.7,790.8,671.1,801.3z   " />
	<path
   id="XMLID_21_"
   class="st5"
   d="M723.7,791.1c-2.9-0.6-6-0.3-8.7,0.8c-17.6,6.9-48.4,11.5-83.4,11.5c-54.7,0-99-11.2-99-25   c0-12.2,34.7-22.4,80.6-24.6l3.2-16.7c-62.9,3.9-110.7,23.4-110.7,46.8c0,26.2,60,47.5,134,47.5c32.4,0,62.1-4.1,85.3-10.9   C740,816,739.1,794.3,723.7,791.1L723.7,791.1z" />
	<path
   id="XMLID_27_"
   class="st11"
   d="M714.3,439.9c-16-5.1-33.7-8-52.2-8c-18.4,0-35.9,2.8-51.9,7.8c-9.7-22.7-22.9-41.2-36.8-52.7   c-2.2-1.8-5.5-1.2-6.9,1.3c-27.7,50.1-40.1,107.4-34.9,169.9c4.8,69.8,31.7,146.5,57.2,225.8h146.8c25.6-79.2,52.2-155.1,57.2-224   c5.5-62.9-7.1-120.4-35.6-171c-1.4-2.5-4.7-3.1-6.9-1.3C736.7,399.3,723.8,417.5,714.3,439.9z" />
	<path
   id="XMLID_55_"
   class="st12"
   d="M674.4,533.9c0,3.7-5.5,6.7-12.4,6.7c-6.8,0-12.4-3-12.4-6.7l12.5-2.8L674.4,533.9z" />
	<path
   id="XMLID_40_"
   class="st12"
   d="M713.3,782.6c0,0,0-7.9,1.5-14.5c13.2-58.7,4.5-76.8,4.5-76.8l-27.8,91.5L713.3,782.6z" />
	<path
   id="XMLID_42_"
   class="st12"
   d="M608.8,782.6c0,0,0-7.9-1.5-14.5c-13.2-58.7-4.5-76.8-4.5-76.8l27.8,91.5L608.8,782.6z" />
	<path
   id="XMLID_16_"
   class="st5"
   d="M714.3,439.9c-16-5.1-33.7-8-52.2-8c-18.4,0-35.9,2.8-51.9,7.8c-9.7-22.7-22.9-41.2-36.8-52.7   c-2.2-1.8-5.5-1.2-6.9,1.3c-27.7,50.1-40.1,107.4-34.9,169.9c4.8,69.8,31.7,146.5,57.2,225.8h146.8c25.6-79.2,52.2-155.1,57.2-224   c5.5-62.9-7.1-120.4-35.6-171c-1.4-2.5-4.7-3.1-6.9-1.3C736.7,399.3,723.8,417.5,714.3,439.9z" />
	<circle
   id="XMLID_6_"
   class="st13"
   cx="620.6"
   cy="479.8"
   r="8" />
	<circle
   id="XMLID_12_"
   class="st13"
   cx="703.6"
   cy="479.8"
   r="8" />
	<path
   id="XMLID_13_"
   class="st13"
   d="M668.8,506.7c-4.4-0.4-8.8-0.4-13.2,0c-2.1,0.2-2.8,2.3-1.2,3.4c1.4,0.9,2.8,1.8,4.2,2.8   c2,1.4,5.3,1.4,7.3,0c1.4-1,2.8-1.9,4.2-2.8C671.6,509,670.8,506.9,668.8,506.7z" />
	<g
   id="XMLID_32_">
		<path
   id="XMLID_39_"
   class="st11"
   d="M719.1,694.2c-1.7,29.5-15.4,60.5-24.8,90c-9.7,0-32.2,0-32.2,0l0.2-83.1" />
		<path
   id="XMLID_34_"
   class="st11"
   d="M605,694.2c1.7,29.5,18.4,60.5,27.8,90c9.7,0,29.2,0,29.2,0l-0.2-83.1" />
	</g>
	<g
   id="XMLID_5_">
		<path
   id="XMLID_10_"
   class="st5"
   d="M680.5,533c-1.8,1-4,1.6-6.2,1.6c-6.7,0-12.1-4.9-12.1-10.9" />
		<path
   id="XMLID_7_"
   class="st5"
   d="M643.7,533c1.8,1,4,1.6,6.2,1.6c6.7,0,12.1-4.9,12.1-10.9" />
	</g>
	<g
   id="XMLID_29_">
		<path
   id="XMLID_11_"
   class="st5"
   d="M719.1,694.2c-1.7,29.5-18.4,60.5-27.8,90c-9.7,0-29.2,0-29.2,0l0.2-83.1" />
		<path
   id="XMLID_30_"
   class="st5"
   d="M605,694.2c1.7,29.5,15.4,60.5,24.8,90c9.7,0,32.2,0,32.2,0l-0.2-83.1" />
	</g>
	<path
   id="XMLID_31_"
   class="st12"
   d="M729.1,451.4c7.4-22.7,18.2-28.5,18.2-28.5s7.8,2.9,7.8,33.8" />
	<path
   id="XMLID_33_"
   class="st12"
   d="M595.1,451.4c-7.4-22.7-18.2-28.5-18.2-28.5s-7.8,2.9-7.8,33.8" />
	<line
   id="XMLID_45_"
   class="st5"
   x1="644.3"
   y1="772.2"
   x2="646.8"
   y2="783.5" />
	<line
   id="XMLID_46_"
   class="st5"
   x1="679.9"
   y1="772.2"
   x2="677.4"
   y2="783.5" />
	<g
   id="XMLID_1_"
   class="st14">
		<path
   id="XMLID_19_"
   class="st15"
   d="M707,528.7c12.1,3.2,24.6,3.8,36.6,2" />
		<path
   id="XMLID_20_"
   class="st15"
   d="M703.6,536.3c11,7,22.4,11.8,33.4,14.3" />
		<path
   id="XMLID_35_"
   class="st15"
   d="M707,517.9c9.9-0.7,20.1-3.4,30-8.1" />
		<path
   id="XMLID_50_"
   class="st15"
   d="M617.2,528.7c-12.1,3.2-24.6,3.8-36.6,2" />
		<path
   id="XMLID_43_"
   class="st15"
   d="M620.6,536.3c-11,7-22.4,11.8-33.4,14.3" />
		<path
   id="XMLID_36_"
   class="st15"
   d="M617.2,517.9c-9.9-0.7-20.1-3.4-30-8.1" />
	</g>
</g>
</svg>