(()=>{var e,t={261:()=>{function e(e){$("#submit").prop("disabled",!0).prepend('<i class="fa fa-spinner fa-pulse"></i> '),$.post(e.action,$(e).serialize()).done((function(e){location.reload()}))}$((function(){"use strict";init_ajax_search("contact","#contactid.ajax-search",{contact_userid:userid}),appValidateForm($("#assign_plan_to_client_create_tenant"),{contactid:"required",tenant_plan:"required",tenants_name:{required:!0,remote:{url:admin_url+"saas/superadmin/validateTenantsName",type:"post",data:{tenants_name:function(){return $('input[name="tenants_name"]').val()},userid:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(){return userid}))}}}},e,{tenants_name:{remote:"Tenant name already exist! Please try another name"}}),$("#tenants_name").trigger("keyup")})),$(document).on("keyup","#tenants_name",(function(e){value=$(this).val().replace(/[^a-zA-Z0-9 ]/g,"").toLowerCase(),$(this).val(value),value=value.replace(/ /g,""),$("#tenants_name").val(value),$("#display_subdomain").html(value)})),$("#settings").on("change",(function(){var e=$(this).val();$.ajax({url:"".concat(admin_url,"saas/superadmin/get_settings_view_page"),type:"POST",data:{userid,view_path:$(this).val()},dataType:"json"}).done((function(t){if($("#settings_view").html(t.view),init_selectpicker(),appColorPicker(),"admin/settings/includes/tags"==e&&$("#settings_view a").each((function(){setting_link=$(this).attr("href"),superadmin_link=setting_link.replace("/settings","/saas/superadmin"),superadmin_link+="/"+userid,$(this).attr("href",superadmin_link)})),"admin/settings/includes/email"==e&&$("#settings_view #email_queue a").each((function(){setting_link=$(this).attr("href"),superadmin_link=setting_link.replace("/emails","/saas/superadmin"),superadmin_link+="/"+userid,$(this).attr("href",superadmin_link)})),"admin/settings/includes/google"==e||"admin/settings/includes/tickets"==e||"admin/settings/includes/cronjob"==e){var n=new URL(site_url),r=n.protocol,i=n.hostname,o=$("#cron_command .alert-info span.text-info").text();o=o.replace(site_url,"".concat(r,"//").concat(tenants_name,".").concat(i,"/")),$("#cron_command .alert-info span.text-info").text(o);var a=$("#settings_view textarea").text();a=a.replace(site_url,"".concat(r,"//").concat(tenants_name,".").concat(i,"/")),$("#settings_view textarea").text(a),$("#settings_view a").not("[role='tab']").each((function(){setting_link=$(this).attr("href");var e=setting_link.replace(site_url,"".concat(r,"//").concat(tenants_name,".").concat(i,"/"));$(this).attr("href",e),setting_text=$(this).text();var t=setting_text.replace(site_url,"".concat(r,"//").concat(tenants_name,".").concat(i,"/"));$(this).text(t)}))}"admin/settings/includes/general"==e&&$("#settings_view a").each((function(){setting_link=$(this).attr("href"),superadmin_link=setting_link.replace("/settings","/saas/superadmin"),superadmin_link+="/"+userid,$(this).attr("href",superadmin_link)})),$('input[name="settings[mail_engine]"]').on("change",(function(){"codeigniter"==$(this).val()?($(".protocol-microsoft").addClass("hide"),$(".protocol-google").addClass("hide"),"microsoft"==$('input[name="settings[email_protocol]"]:checked').val()&&($("#smtp").prop("checked",!0),$("#microsoft").trigger("change")),"google"==$('input[name="settings[email_protocol]"]:checked').val()&&($("#smtp").prop("checked",!0),$("#google").trigger("change"))):($(".protocol-microsoft").removeClass("hide"),$(".protocol-google").removeClass("hide"))})),$('input[name="settings[email_protocol]"]').on("change",(function(){var e=$('input[name="settings[smtp_host]"]'),t=$('input[name="settings[smtp_port]"]'),n=$('select[name="settings[smtp_encryption]"]'),r=function(){n.hasClass("_modified")&&(n.selectpicker("val",""),n.removeClass("_modified")),t.hasClass("_modified")&&(t.val(""),t.removeClass("_modified")),e.hasClass("_modified")&&(e.val(""),e.removeClass("_modified"))};"mail"==$(this).val()?($(".xoauth-hide").addClass("hide"),$(".smtp-fields").addClass("hide"),$(".xoauth-microsoft-show").addClass("hide"),$(".xoauth-google-show").addClass("hide"),r()):"microsoft"===$(this).val()||"google"===$(this).val()?($(".smtp-fields").removeClass("hide"),$(".xoauth-hide").addClass("hide"),$(".xoauth-microsoft-show").addClass("hide"),$(".xoauth-google-show").addClass("hide"),"microsoft"===$(this).val()&&($(".xoauth-microsoft-show").removeClass("hide"),""==e.val()&&(e.val("smtp.office365.com"),e.addClass("_modified"))),"google"===$(this).val()&&($(".xoauth-google-show").removeClass("hide"),""==e.val()&&(e.val("smtp.gmail.com"),e.addClass("_modified"))),""==t.val()&&(t.val("587"),t.addClass("_modified"),""==n.selectpicker("val")&&(n.selectpicker("val","tls"),n.addClass("_modified")))):($(".smtp-fields").removeClass("hide"),$(".xoauth-hide").removeClass("hide"),$(".xoauth-microsoft-show").addClass("hide"),$(".xoauth-google-show").addClass("hide"),r())})),$(".sms_gateway_active input").on("change",(function(){"1"==$(this).val()&&$("body .sms_gateway_active").not($(this).parents(".sms_gateway_active")[0]).find('input[value="0"]').prop("checked",!0)})),$('input[name="settings[pusher_realtime_notifications]"]').on("change",(function(){"1"==$(this).val()?$('input[name="settings[desktop_notifications]"]').prop("disabled",!1):($('input[name="settings[desktop_notifications]"]').prop("disabled",!0),$('input[name="settings[desktop_notifications]"][value="0"]').prop("checked",!0))})),$(".test_email").on("click",(function(){var e=$('input[name="test_email"]').val();""!=e&&($(this).attr("disabled",!0),$.post(admin_url+"saas/superadmin/sent_smtp_test_email/"+userid,{test_email:e}).done((function(e){})))})),$('input[name="settings[reminder_for_completed_but_not_billed_tasks]"]').on("change",(function(){"1"==$(this).val()?$(".staff_notify_completed_but_not_billed_tasks_fields").removeClass("hide"):$(".staff_notify_completed_but_not_billed_tasks_fields").addClass("hide")}))}))}))}},n={};function r(e){var i=n[e];if(void 0!==i){if(void 0!==i.error)throw i.error;return i.exports}var o=n[e]={exports:{}};try{var a={id:e,module:o,factory:t[e],require:r};r.i.forEach((function(e){e(a)})),o=a.module,a.factory.call(o.exports,o,o.exports,a.require)}catch(e){throw o.error=e,e}return o.exports}r.m=t,r.c=n,r.i=[],r.hu=e=>e+"."+r.h()+".hot-update.js",r.hmrF=()=>"saas_client."+r.h()+".hot-update.json",r.h=()=>"2c0ba3deee11a72b7ac2",r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},r.l=(t,n,i,o)=>{if(e[t])e[t].push(n);else{var a,s;if(void 0!==i)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var l=c[d];if(l.getAttribute("src")==t){a=l;break}}a||(s=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,r.nc&&a.setAttribute("nonce",r.nc),a.src=t),e[t]=[n];var u=(n,r)=>{a.onerror=a.onload=null,clearTimeout(p);var i=e[t];if(delete e[t],a.parentNode&&a.parentNode.removeChild(a),i&&i.forEach((e=>e(r))),n)return n(r)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=u.bind(null,a.onerror),a.onload=u.bind(null,a.onload),s&&document.head.appendChild(a)}},(()=>{var e,t,n,i={},o=r.c,a=[],s=[],c="idle",d=0,l=[];function u(e){c=e;for(var t=[],n=0;n<s.length;n++)t[n]=s[n].call(null,e);return Promise.all(t).then((function(){}))}function p(){0==--d&&u("ready").then((function(){if(0===d){var e=l;l=[];for(var t=0;t<e.length;t++)e[t]()}}))}function f(e){if("idle"!==c)throw new Error("check() is only allowed in idle status");return u("check").then(r.hmrM).then((function(n){return n?u("prepare").then((function(){var i=[];return t=[],Promise.all(Object.keys(r.hmrC).reduce((function(e,o){return r.hmrC[o](n.c,n.r,n.m,e,t,i),e}),[])).then((function(){return t=function(){return e?m(e):u("ready").then((function(){return i}))},0===d?t():new Promise((function(e){l.push((function(){e(t())}))}));var t}))})):u(v()?"ready":"idle").then((function(){return null}))}))}function h(e){return"ready"!==c?Promise.resolve().then((function(){throw new Error("apply() is only allowed in ready status (state: "+c+")")})):m(e)}function m(e){e=e||{},v();var r=t.map((function(t){return t(e)}));t=void 0;var i=r.map((function(e){return e.error})).filter(Boolean);if(i.length>0)return u("abort").then((function(){throw i[0]}));var o=u("dispose");r.forEach((function(e){e.dispose&&e.dispose()}));var a,s=u("apply"),c=function(e){a||(a=e)},d=[];return r.forEach((function(e){if(e.apply){var t=e.apply(c);if(t)for(var n=0;n<t.length;n++)d.push(t[n])}})),Promise.all([o,s]).then((function(){return a?u("fail").then((function(){throw a})):n?m(e).then((function(e){return d.forEach((function(t){e.indexOf(t)<0&&e.push(t)})),e})):u("idle").then((function(){return d}))}))}function v(){if(n)return t||(t=[]),Object.keys(r.hmrI).forEach((function(e){n.forEach((function(n){r.hmrI[e](n,t)}))})),n=void 0,!0}r.hmrD=i,r.i.push((function(l){var m,v,_,g,$=l.module,y=function(t,n){var r=o[n];if(!r)return t;var i=function(i){if(r.hot.active){if(o[i]){var s=o[i].parents;-1===s.indexOf(n)&&s.push(n)}else a=[n],e=i;-1===r.children.indexOf(i)&&r.children.push(i)}else console.warn("[HMR] unexpected require("+i+") from disposed module "+n),a=[];return t(i)},s=function(e){return{configurable:!0,enumerable:!0,get:function(){return t[e]},set:function(n){t[e]=n}}};for(var l in t)Object.prototype.hasOwnProperty.call(t,l)&&"e"!==l&&Object.defineProperty(i,l,s(l));return i.e=function(e,n){return function(e){switch(c){case"ready":u("prepare");case"prepare":return d++,e.then(p,p),e;default:return e}}(t.e(e,n))},i}(l.require,l.id);$.hot=(m=l.id,v=$,g={_acceptedDependencies:{},_acceptedErrorHandlers:{},_declinedDependencies:{},_selfAccepted:!1,_selfDeclined:!1,_selfInvalidated:!1,_disposeHandlers:[],_main:_=e!==m,_requireSelf:function(){a=v.parents.slice(),e=_?void 0:m,r(m)},active:!0,accept:function(e,t,n){if(void 0===e)g._selfAccepted=!0;else if("function"==typeof e)g._selfAccepted=e;else if("object"==typeof e&&null!==e)for(var r=0;r<e.length;r++)g._acceptedDependencies[e[r]]=t||function(){},g._acceptedErrorHandlers[e[r]]=n;else g._acceptedDependencies[e]=t||function(){},g._acceptedErrorHandlers[e]=n},decline:function(e){if(void 0===e)g._selfDeclined=!0;else if("object"==typeof e&&null!==e)for(var t=0;t<e.length;t++)g._declinedDependencies[e[t]]=!0;else g._declinedDependencies[e]=!0},dispose:function(e){g._disposeHandlers.push(e)},addDisposeHandler:function(e){g._disposeHandlers.push(e)},removeDisposeHandler:function(e){var t=g._disposeHandlers.indexOf(e);t>=0&&g._disposeHandlers.splice(t,1)},invalidate:function(){switch(this._selfInvalidated=!0,c){case"idle":t=[],Object.keys(r.hmrI).forEach((function(e){r.hmrI[e](m,t)})),u("ready");break;case"ready":Object.keys(r.hmrI).forEach((function(e){r.hmrI[e](m,t)}));break;case"prepare":case"check":case"dispose":case"apply":(n=n||[]).push(m)}},check:f,apply:h,status:function(e){if(!e)return c;s.push(e)},addStatusHandler:function(e){s.push(e)},removeStatusHandler:function(e){var t=s.indexOf(e);t>=0&&s.splice(t,1)},data:i[m]},e=void 0,g),$.parents=a,$.children=[],a=[],l.require=y})),r.hmrC={},r.hmrI={}})(),(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var i=n.length-1;i>-1&&(!e||!/^http(s?):/.test(e));)e=n[i--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e})(),(()=>{var e,t,n,i,o,a=r.hmrS_jsonp=r.hmrS_jsonp||{235:0},s={};function c(t,n){return e=n,new Promise(((e,n)=>{s[t]=e;var i=r.p+r.hu(t),o=new Error;r.l(i,(e=>{if(s[t]){s[t]=void 0;var r=e&&("load"===e.type?"missing":e.type),i=e&&e.target&&e.target.src;o.message="Loading hot update chunk "+t+" failed.\n("+r+": "+i+")",o.name="ChunkLoadError",o.type=r,o.request=i,n(o)}}))}))}function d(e){function s(e){for(var t=[e],n={},i=t.map((function(e){return{chain:[e],id:e}}));i.length>0;){var o=i.pop(),a=o.id,s=o.chain,d=r.c[a];if(d&&(!d.hot._selfAccepted||d.hot._selfInvalidated)){if(d.hot._selfDeclined)return{type:"self-declined",chain:s,moduleId:a};if(d.hot._main)return{type:"unaccepted",chain:s,moduleId:a};for(var l=0;l<d.parents.length;l++){var u=d.parents[l],p=r.c[u];if(p){if(p.hot._declinedDependencies[a])return{type:"declined",chain:s.concat([u]),moduleId:a,parentId:u};-1===t.indexOf(u)&&(p.hot._acceptedDependencies[a]?(n[u]||(n[u]=[]),c(n[u],[a])):(delete n[u],t.push(u),i.push({chain:s.concat([u]),id:u})))}}}}return{type:"accepted",moduleId:e,outdatedModules:t,outdatedDependencies:n}}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];-1===e.indexOf(r)&&e.push(r)}}r.f&&delete r.f.jsonpHmr,t=void 0;var d={},l=[],u={},p=function(e){console.warn("[HMR] unexpected require("+e.id+") to disposed module")};for(var f in n)if(r.o(n,f)){var h=n[f],m=h?s(f):{type:"disposed",moduleId:f},v=!1,_=!1,g=!1,$="";switch(m.chain&&($="\nUpdate propagation: "+m.chain.join(" -> ")),m.type){case"self-declined":e.onDeclined&&e.onDeclined(m),e.ignoreDeclined||(v=new Error("Aborted because of self decline: "+m.moduleId+$));break;case"declined":e.onDeclined&&e.onDeclined(m),e.ignoreDeclined||(v=new Error("Aborted because of declined dependency: "+m.moduleId+" in "+m.parentId+$));break;case"unaccepted":e.onUnaccepted&&e.onUnaccepted(m),e.ignoreUnaccepted||(v=new Error("Aborted because "+f+" is not accepted"+$));break;case"accepted":e.onAccepted&&e.onAccepted(m),_=!0;break;case"disposed":e.onDisposed&&e.onDisposed(m),g=!0;break;default:throw new Error("Unexception type "+m.type)}if(v)return{error:v};if(_)for(f in u[f]=h,c(l,m.outdatedModules),m.outdatedDependencies)r.o(m.outdatedDependencies,f)&&(d[f]||(d[f]=[]),c(d[f],m.outdatedDependencies[f]));g&&(c(l,[m.moduleId]),u[f]=p)}n=void 0;for(var y,w=[],k=0;k<l.length;k++){var b=l[k],x=r.c[b];x&&(x.hot._selfAccepted||x.hot._main)&&u[b]!==p&&!x.hot._selfInvalidated&&w.push({module:b,require:x.hot._requireSelf,errorHandler:x.hot._selfAccepted})}return{dispose:function(){var e;i.forEach((function(e){delete a[e]})),i=void 0;for(var t,n=l.slice();n.length>0;){var o=n.pop(),s=r.c[o];if(s){var c={},u=s.hot._disposeHandlers;for(k=0;k<u.length;k++)u[k].call(null,c);for(r.hmrD[o]=c,s.hot.active=!1,delete r.c[o],delete d[o],k=0;k<s.children.length;k++){var p=r.c[s.children[k]];p&&(e=p.parents.indexOf(o))>=0&&p.parents.splice(e,1)}}}for(var f in d)if(r.o(d,f)&&(s=r.c[f]))for(y=d[f],k=0;k<y.length;k++)t=y[k],(e=s.children.indexOf(t))>=0&&s.children.splice(e,1)},apply:function(t){for(var n in u)r.o(u,n)&&(r.m[n]=u[n]);for(var i=0;i<o.length;i++)o[i](r);for(var a in d)if(r.o(d,a)){var s=r.c[a];if(s){y=d[a];for(var c=[],p=[],f=[],h=0;h<y.length;h++){var m=y[h],v=s.hot._acceptedDependencies[m],_=s.hot._acceptedErrorHandlers[m];if(v){if(-1!==c.indexOf(v))continue;c.push(v),p.push(_),f.push(m)}}for(var g=0;g<c.length;g++)try{c[g].call(null,y)}catch(n){if("function"==typeof p[g])try{p[g](n,{moduleId:a,dependencyId:f[g]})}catch(r){e.onErrored&&e.onErrored({type:"accept-error-handler-errored",moduleId:a,dependencyId:f[g],error:r,originalError:n}),e.ignoreErrored||(t(r),t(n))}else e.onErrored&&e.onErrored({type:"accept-errored",moduleId:a,dependencyId:f[g],error:n}),e.ignoreErrored||t(n)}}}for(var $=0;$<w.length;$++){var k=w[$],b=k.module;try{k.require(b)}catch(n){if("function"==typeof k.errorHandler)try{k.errorHandler(n,{moduleId:b,module:r.c[b]})}catch(r){e.onErrored&&e.onErrored({type:"self-accept-error-handler-errored",moduleId:b,error:r,originalError:n}),e.ignoreErrored||(t(r),t(n))}else e.onErrored&&e.onErrored({type:"self-accept-errored",moduleId:b,error:n}),e.ignoreErrored||t(n)}}return l}}}self.webpackHotUpdate=(t,i,a)=>{for(var c in i)r.o(i,c)&&(n[c]=i[c],e&&e.push(c));a&&o.push(a),s[t]&&(s[t](),s[t]=void 0)},r.hmrI.jsonp=function(e,t){n||(n={},o=[],i=[],t.push(d)),r.o(n,e)||(n[e]=r.m[e])},r.hmrC.jsonp=function(e,s,l,u,p,f){p.push(d),t={},i=s,n=l.reduce((function(e,t){return e[t]=!1,e}),{}),o=[],e.forEach((function(e){r.o(a,e)&&void 0!==a[e]?(u.push(c(e,f)),t[e]=!0):t[e]=!1})),r.f&&(r.f.jsonpHmr=function(e,n){t&&r.o(t,e)&&!t[e]&&(n.push(c(e)),t[e]=!0)})},r.hmrM=()=>{if("undefined"==typeof fetch)throw new Error("No browser support: need fetch API");return fetch(r.p+r.hmrF()).then((e=>{if(404!==e.status){if(!e.ok)throw new Error("Failed to fetch update manifest "+e.statusText);return e.json()}}))}})(),r(261)})();