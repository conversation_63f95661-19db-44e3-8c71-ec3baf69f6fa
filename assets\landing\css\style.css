/**
* Importing necessary  Styles.
**/
@import url('https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,700italic,300,400,700');
@import url('https://fonts.googleapis.com/css?family=Inter');
/*----bootstrap css ----- */
@import url('../vendor/bootstrap/css/bootstrap.min.css');
/*------- Framework css -----------*/
@import url('framework.css');
/*----------- Slick Slider -------*/
@import url('../vendor/slick/slick.css');
/*----Animate css----*/
@import url('../vendor/animate.min.css');
/*-------- AOS css ------*/
@import url('../vendor/aos-next/dist/aos.css');
/*----------- Fancybox css -------*/
@import url('../vendor/fancybox/dist/jquery.fancybox.min.css');
/*---------------- Custom Animation -------------*/
@import url('custom-animation.css');


/*^^^^^^^^^^ Fonts ^^^^^^^^^^^^^^^^*/
@font-face {
  font-family: 'Inter', serif;
  font-style: normal;
  font-weight: 400;
}

/*========================================================================================

*************************** Start Styling Your theme from here ***************************

==========================================================================================*/
/*** 

====================================================================
  Loading Transition
====================================================================

 ***/
.ctn-preloader {
  align-items: center;
  -webkit-align-items: center;
  display: flex;
  display: -ms-flexbox;
  height: 100%;
  justify-content: center;
  -webkit-justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 999999;
  background: #fff;
}
.ctn-preloader .animation-preloader {
  position: absolute;
  z-index: 100;
}
/* Spinner cargando */
.ctn-preloader .animation-preloader .spinner {
  animation: spinner 1.5s infinite linear;
  border-radius: 50%;
  border: 3px solid rgb(101 92 151 / 6%);
  border-top-color: #FFC107;
  border-bottom-color: #655C97;
  height: 50px;
  margin: 0 auto 45px auto;
  width: 50px;
}
/* Texto cargando */
.ctn-preloader .animation-preloader .txt-loading {
  text-align: center;
  user-select: none;
}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:before {
  animation: letters-loading 4s infinite;
  color: #572FF6;
  content: attr(data-text-preloader);
  left: 0;
  opacity: 0;
  top:0;
  line-height: 70px;
  position: absolute;
}
.ctn-preloader .animation-preloader .txt-loading .letters-loading {
  font-family: 'Inter', serif;
  font-weight: 800;
  letter-spacing: -2px;
  display: inline-block;
  color: rgb(101 92 151 / 14%);
  position: relative;
  font-size: 70px;
  line-height: 70px;
}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(2):before {animation-delay: 0.2s;}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(3):before {animation-delay: 0.4s;}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(4):before {animation-delay: 0.6s;}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(5):before {animation-delay: 0.8s;}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(6):before { animation-delay: 1s;}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(7):before { animation-delay: 1.2s;}
.ctn-preloader .animation-preloader .txt-loading .letters-loading:nth-child(8):before { animation-delay: 1.4s;}
.ctn-preloader .loader-section {
  background-color: #ffffff;
  height: 100%;
  position: fixed;
  top: 0;
  width: calc(50% + 1px);
}
.loaded .animation-preloader {
  opacity: 0;
  transition: 0.3s ease-out;
}
/* Animación del preloader */
@keyframes spinner {
  to {
    transform: rotateZ(360deg);
  }
}
@keyframes letters-loading {
  0%,
  75%,
  100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }

  25%,
  50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}

@media screen and (max-width: 767px) {
  .ctn-preloader .animation-preloader .spinner {
    height: 8em;
    width: 8em;
  }
}
@media screen and (max-width: 500px) {
  .ctn-preloader .animation-preloader .spinner {
    height: 7em;
    width: 7em;
  }
  .ctn-preloader .animation-preloader .txt-loading .letters-loading {font-size: 40px; letter-spacing: 10px;}
}
/*==================== Click Top ====================*/
.scroll-top {
  width:35px;
  height:35px;
  line-height: 35px;
  position: fixed;
  bottom: 20px;
  right: 5px;
  z-index: 99;
  text-align: center;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  border-radius: 50%;
  background: #000;
  display: none;
  transition: all 0.3s ease-in-out;
}
.scroll-top:after {
  position: absolute;
  z-index: -1;
  content: '';
  top: 100%;
  left: 5%;
  height: 10px;
  width: 90%;
  opacity: 1;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}
/*------------- Global Prefix -------------------*/
::-webkit-scrollbar{ width: 15px; }
::-webkit-scrollbar-track { background: #ffffff !important; }
::-webkit-scrollbar-thumb { background: var(--blue-dark);border-radius: 15px !important; }
:root {
  --text-color: #73737B;
  --text-light:#888888;
  --heading :#101621;
  --blue-dark:#6F55FF;
  --red-light:#FD6A5E;
  --yellow-deep:#FFB840;
  --blue-light:#3BB0D7;
  --purple-blue:#655C97;
  --vin-red:#973C56;
}
::selection {
  background-color: #6F55FF;
  color: #fff;
}
body {
  font-family: 'Inter', serif !important;
  font-weight: normal;
  font-size: 18px;
  position: relative;
  line-height: 1.7em;
  color: var(--text-color);
}
/*______________________ Global style ___________________*/
.main-page-wrapper {overflow-x:hidden; padding-top: 150px;}
.h1,h1,.h2,h2,.h3,h3,.h4,h4,.h5,h5,.h6,h6 {color: var(--heading); font-weight: normal;}
.h1,h1 {font-size: 80px; line-height: 1.1em;}
.h2,h2 {font-size: 52px; line-height: 1.1em;}
.h3,h3 {font-size: 36px;}
.h4,h4 {font-size: 22px;}
.h5,h5 {font-size: 20px;}
.h6,h6 {font-size: 15px;}
.font-rubik {font-family: 'Inter', serif !important;}
.font-gilroy-bold {font-family: 'Inter' !important;}
.font-slab {font-family: 'Inter', serif !important;}
.font-gordita {font-family: 'Inter' !important;}
.font-recoleta {font-family: 'Inter' !important;}
::-webkit-input-placeholder { /* Edge */color: #ACAEB3;}
:-ms-input-placeholder { /* Internet Explorer 10-11 */color: #ACAEB3;}
::placeholder {color: #ACAEB3;}
.cs-screen {position: absolute;}
.illustration-holder {position: relative;}
.shapes {position: absolute;z-index: -1;}
.dark-style {background: #0F1123; color: #AFAFAF;}
.dark-style .h1,.dark-style h1,.dark-style .h2,.dark-style h2,.dark-style .h3,.dark-style h3,.dark-style .h4,.dark-style h4,.dark-style .h5,.dark-style h5,.dark-style .h6,.dark-style h6 {color: #fff;}
.gr-bg-one {background: linear-gradient(90.6deg, #F548A0 2.93%, #F57C35 99.47%);}
.hover-reverse-gr-bg-one {position: relative; z-index: 1;}
.hover-reverse-gr-bg-one:before{
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  background: linear-gradient(270.01deg, #F548A0 0.01%, #F57C35 100%);
  border-radius: 6px;
  transition: all 0.6s ease-in-out;
  opacity: 0;
  z-index: -1;
}
.hover-reverse-gr-bg-one:hover:before {opacity: 1;}
.top-border {border-top: 1px solid #efefef;}
.bottom-border {border-bottom: 1px solid #efefef;}
/*-------------- Prefix Classes ----------------*/
.tran3s   {
  transition: all 0.3s ease-in-out;
}
.tran4s {
  transition: all 0.4s ease-in-out;
}
.tran5s {
  transition: all 0.5s ease-in-out;
}
.tran6s {
  transition: all 0.6s ease-in-out;
}
/*^^^^^^^^^^^^^^^^^^^^^ Section Title ^^^^^^^^^^^^^^^^^^^^^^^^^*/
.title-style-one h2 {
  font-family: 'gilroy-black';
  font-size: 58px;
  line-height: 1.15em;
}
.title-style-one h6 {
  font-size: 20px;
  color: #CACACA;
  padding-bottom: 15px;
}
.title-style-one p {
  font-size: 22px;
  line-height: 1.58em;
  color: #2A2A2A;
  padding-top: 25px;
}
.title-style-two p {
  text-transform: uppercase;
  font-size: 16px;
  color: #ADB1B5;
  letter-spacing: 1.12px;
  padding-bottom: 7px;
}
.title-style-two h2 {
  font-family: 'gilroy-black';
  font-size: 58px;
  line-height: 1.15em;
}
.title-style-two span {position: relative;}
.title-style-two span img {
  position: absolute;
  bottom: -30px;
  left: 0;
  z-index: -1;
}
.title-style-two .sub-text {
  font-size: 24px;
  line-height: 1.5em;
  color: #6D6D6D;
  padding-top: 15px;
}
.title-style-three p {
  text-transform: uppercase;
  font-size: 20px;
  color: #ADB1B5;
  letter-spacing: 1.12px;
  padding-bottom: 17px;
}
.title-style-three h6 {
  font-family: 'Roboto', sans-serif;
  font-size: 18px;
  color: #ADB1B5;
  padding-bottom: 15px;
}
.title-style-three h2 {
  font-family: 'gilroy-black';
  font-size: 54px;
  line-height: 1.18em;
}
.title-style-three span {position: relative;}
.title-style-three span img {
  position: absolute;
  bottom: -21px;
  left: 0;
  z-index: -1;
}
.title-style-four h6 {
  font-family: 'Roboto', sans-serif;
  font-size: 20px;
  color: rgba(0,0,0,0.35);
  padding-bottom: 15px;
}
.title-style-four h2 {
  font-family: 'gilroy-black';
  font-size: 58px;
  line-height: 1.15em;
  color: #2a2a2a;
}
.title-style-four span {position: relative;z-index: 1;}
.title-style-four span img {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.title-style-four .sub-text {
  font-size: 22px;
  line-height: 1.59em;
  color: #2A2A2A;
}
.title-style-five h2 {
  font-family: 'Inter', serif;
  font-size: 54px;
  line-height: 1.25em;
}
.title-style-five h2 span {position: relative; z-index: 1;}
.title-style-five h2 span:before {
  content: '';
  width: 100%;
  height: 15px;
  position: absolute;
  background: rgba(241, 193, 83, 0.45);
  left: 0;
  bottom: 12px;
  z-index: -1;
}
.title-style-five h6 {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.35);
  text-transform: uppercase;
  letter-spacing: 1px;
  padding-bottom: 12px;
}
.title-style-six h2 {
  font-family: 'Inter';
  font-weight: normal;
  font-size: 68px;
  line-height: 1.2em;
  color: #000;
}
.dark-style .title-style-six h2 {color: #fff;}
.title-style-six h2 span {
  color: #FF2759;
  text-decoration: underline;
  text-decoration-thickness: 4px;
}
.title-style-six h6 {
  font-size: 18px;
  text-transform: uppercase;
  color: #bfbfbf;
  padding-bottom: 20px;
  letter-spacing: 1.8px;
}
.title-style-six .text-xs {font-size: 22px; line-height: 1.65em; padding-top: 30px;}
.dark-style .title-style-six .text-xs {color: rgba(255, 255, 255, 0.4);}
.title-style-seven h2 {
  font-size: 64px;
  font-weight: 500;
  line-height: 1.32em;
}
.title-style-seven h2 span {position: relative; display: inline-block;}
.title-style-seven h2 span:before {
  content: '';
  width: 100%;
  height: 13px;
  background: #FFEAA0;
  position: absolute;
  left: 0;
  bottom: 10px;
  z-index: -1;
}
.title-style-seven p {font-size: 22px; color: #000; padding-top: 22px;}
.title-style-eight h2 {
  font-size: 68px;
  font-weight: 700;
  line-height: 1.26em;
}
.title-style-eight p {font-size: 22px; color: #000; padding-top: 40px;}
.title-style-eight h6 {
  font-size: 16px;
  letter-spacing: 2px;
  color: rgba(0, 0, 0, 0.3);
  padding-bottom: 20px;
}
.title-style-eight h2 span {display: inline-block; position: relative;}
.title-style-eight h2 span img {
  position: absolute;
  max-height: 100%;
  z-index: -1;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.title-style-nine h6 {
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #C8C8C8;
  padding-bottom: 25px;
}
.title-style-nine h2 {
  font-size: 52px;
  font-weight: 700;
  line-height: 1.34em;
}
.title-style-nine h2 span {display: inline-block; position: relative;}
.title-style-nine h2 span img {
  position: absolute;
  max-height: 100%;
  z-index: -1;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.title-style-nine p {font-size: 20px; color: #6A6B72; padding-top: 28px;}
.title-style-ten h6 {
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: rgba(0, 0, 0, 0.25);
  padding-bottom: 20px;
}
.title-style-ten h2 {
  font-family: 'Inter';
  font-size: 68px;
  font-weight: 500;
  line-height: 1.27em;
}
.title-style-ten p {font-size: 24px; line-height: 1.91em; color: #000;}
.title-style-eleven .upper-title {
  font-size: 14px;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 3px;
  padding-bottom: 10px;
}
.title-style-eleven h2 {
  font-family: 'Inter';
  font-size: 58px;
  font-weight: 500;
  line-height: 1.29em;
  letter-spacing: -2px;
}
.title-style-eleven p {font-size: 20px;}
.title-style-twelve .upper-title {
  text-transform: uppercase;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
  letter-spacing: 3px;
  padding-bottom: 8px;
}
.title-style-twelve h2 {
  font-weight: 700;
  font-size: 58px;
  line-height: 1.29em;
}
.title-style-twelve span {position: relative;}
.title-style-twelve span img {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.title-style-twelve p {
  font-size: 24px;
  line-height: 1.75em;
}
.title-style-thirteen .upper-title {
  text-transform: uppercase;
  font-size: 15px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.2);
  letter-spacing: 3px;
  padding-bottom: 16px;
}
.title-style-thirteen h2 {
  font-family: 'Inter';
  font-weight: 700;
  font-size: 72px;
  line-height: 1.25em;
  letter-spacing: -1px;
}
.title-style-thirteen span {position: relative;}
.title-style-thirteen span img {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.title-style-fourteen h2 {
  font-size: 52px;
  font-weight: 500;
  line-height: 1.25em;
  letter-spacing: -1px;
}
.title-style-fourteen .upper-title {
  text-transform: uppercase;
  font-size: 14px;
  color: #000;
  letter-spacing: 2px;
  padding-bottom: 10px;
}
.title-style-fifteen h2 {
  font-size: 64px;
  font-weight: 500;
  line-height: 1.40em;
  text-transform: uppercase;
}
.title-style-fifteen .upper-title {
  text-transform: uppercase;
  font-size: 18px;
  color: #B6B6B6;
  letter-spacing: 3px;
  padding-bottom: 18px;
}
.title-style-sixteen h2 {
  font-size: 58px;
  font-weight: 500;
  line-height: 1.29em;
  letter-spacing: -1px;
  text-transform: uppercase;
}
.title-style-sixteen .upper-title {
  font-size: 20px;
  color: #BABABA;
  padding-bottom: 25px;
}
.nav-item:hover {
    font-weight: bold;
}
.theme-menu-six.fixed .right-widget .signup-btn:hover {
    background: #FFC107;
    color: black;
}
.icon-bar {
    border-bottom: 2px solid #9d9d9d !important;
    display: block;
    width: 22px;
    height: 5px;
}
/*^^^^^^^^^^^^^^^^^^^^^ Theme Button ^^^^^^^^^^^^^^^^^^^^^^^^^*/
body .theme-btn-one {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  line-height: 50px;
  background: var(--blue-dark);
  padding: 0 42px;
  border-radius: 4px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-one.btn-lg {
  padding: 0 50px; 
  line-height: 55px; 
  text-transform: uppercase;
  letter-spacing: 1.14px;
}
body .theme-btn-one:hover {background: #FFBA12;}
body .theme-btn-two {
  font-family: 'gilroy-semibold';
  font-size: 18px;
  color: #fff;
  line-height: 48px;
  border:2px solid var(--red-light);
  background: var(--red-light);
  padding: 0 42px;
  border-radius: 30px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-two:hover {background: transparent; color:var(--red-light); }
body .theme-btn-three {
  font-family: 'gilroy-semibold';
  font-size: 18px;
  color: var(--red-light);
  line-height: 51px;
  border-radius: 30px;
  border:2px solid var(--red-light);
  padding: 0 32px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-three:hover {
  background: var(--red-light);
  color: #fff;
}
body .theme-btn-four {
  font-size: 17px;
  color: var(--blue-dark);
  border:2px solid var(--blue-dark);
  border-radius: 5px;
  line-height: 46px;
  padding: 0 35px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-four:hover {background: var(--blue-dark); color: #fff;}
body .theme-btn-five {
  font-family: 'Inter', serif;
  line-height: 56px;
  padding: 0 35px;
  text-align: center;
  color: #fff;
  background: var(--purple-blue);
  position: relative;
  z-index: 1;
}
body .theme-btn-five:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top:0;
  left:0;
  background: var(--vin-red);
  z-index: -1;
  transition: all 0.3s ease-in-out;
  transform: scale(0,1);
  transform-origin: 0 100%;
}
body .theme-btn-five:hover:before {
  transform: scale(1,1);
}
body .theme-btn-six {
  font-size: 16px;
  font-weight: 500;
  line-height: 50px;
  color: #000;
  transition: all 0.3s ease-in-out;
  background: #000;
  padding: 0 30px;
  color: #fff;
}
body .theme-btn-six.lg {padding: 0 45px;}
body .theme-btn-six:hover {
  background: var(--blue-light);
  color: #fff;
}
body .theme-btn-seven {
  font-weight: 500;
  text-align: center;
  line-height: 60px;
  color: #fff;
  border-radius: 6px;
  padding: 0 45px;
  background: #000;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-seven:hover {background: #FF2759;}
body .theme-btn-eight {
  font-weight: 500;
  font-size: 16px;
  line-height: 58px;
  padding: 0 40px;
  border-radius: 5px;
  background: #2D2D2D;
  color: #fff;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-eight:hover {
  background: #FFEAA0;
  color: #212121;
}
body .theme-btn-nine {
  font-family: 'Inter';
  line-height: 58px;
  border-radius: 50px;
  color: #fff;
  padding: 0 34px;
  min-width: 180px;
  text-align: center;
  background: #262626;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-nine:hover {background: #EB5E2A;}
body .theme-btn-ten {
  font-weight: 500;
  font-size: 18px;
  letter-spacing: -0.5px;
  color: #000;
  position: relative;
  padding-bottom: 5px;
}
body .theme-btn-ten .fa {font-size: 14px; margin-left: 5px;}
body .theme-btn-ten:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #000;
  left: 0;
  bottom: 0;
}
body .theme-btn-eleven {
  font-family: 'Inter';
  font-weight: 500;
  font-size: 18px;
  line-height: 48px;
  border: 2px solid #000;
  border-radius: 30px;
  color: #000;
  padding: 0 40px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-eleven:hover {background: #000; color: #fff;}
body .theme-btn-twelve {
  font-family: 'Inter';
  font-size: 17px;
  line-height: 48px;
  background: #FF006B;
  border: 2px solid #FF006B;
  border-radius: 30px;
  color: #fff;
  padding: 0 32px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-twelve:hover {
  background: transparent;
  color: #FF006B;
}
body .theme-btn-thirteen {
  font-family: 'Inter';
  font-size: 17px;
  line-height: 50px;
  background: #232323;
  border-radius: 30px;
  color: #fff;
  padding: 0 45px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-thirteen:hover {background: #FF006B;}
body .theme-btn-fourteen {
  font-family: 'Inter';
  font-size: 17px;
  line-height: 54px;
  background: #000;
  border-radius: 7px;
  color: #fff;
  letter-spacing: -0.3px;
  padding: 0 33px;
  transition: all 0.3s ease-in-out;
}
body .theme-btn-fourteen:hover {background: #6F6CFF;}
body .shop-btn-one {
  width: 70px;
  height: 70px;
  background: #000;
}
body .shop-btn-one:hover {background: var(--purple-blue);}
body .shop-btn-two {
  font-size: 15px;
  line-height: 50px;
  text-transform: uppercase;
  color: #fff;
  background: #161616;
  padding: 0 36px;
}
body .shop-btn-two:hover {background: var(--purple-blue);}
/*-------------------- Pagination ------------------*/
.page-pagination-one li a {
  font-family: 'Inter', serif !important;
  background: #fff;
  width: 40px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  font-size: 17px;
  color: #515151;
  box-shadow: 0 2px 4px rgba(0,0,0,0.07);
  transition: all 0.3s ease-in-out;
}
.page-pagination-one li a .fa {font-size: 22px;}
.page-pagination-one li:first-child a {border-radius: 5px 0 0 5px;}
.page-pagination-one li:last-child a {border-radius: 0 5px 5px 0;}
.page-pagination-one li a:hover,
.page-pagination-one li a.active {background: var(--blue-dark); color: #fff;}
.blog-v3 .page-pagination-one li a:hover,
.blog-v3 .page-pagination-one li a.active {background: var(--red-light); color: #fff;}
.page-pagination-two ul li {
  font-family: 'Inter', serif;
  font-size: 22px;
  color: #BABABA;
  margin: 0 11px;
}
.page-pagination-two ul li:first-child a {transform: rotate(180deg);}
.page-pagination-two ul li:hover a,
.page-pagination-two ul li.active a {color: #000;}
.page-pagination-two ul li.arrow {font-size: 35px; color: #000;}
.page-pagination-two ul li:nth-child(7) a {color: #000;}
/*^^^^^^^^^^^^^^^^^^^^^ Theme Menu ^^^^^^^^^^^^^^^^^^^^^^^^^*/
.theme-main-menu {
  position: fixed;
  z-index: 99;
  top:0;
  left:0;
  right: 0;
  background: #fff;
  padding: 30px 70px;
  transition: all 0.4s ease-out;
}
.theme-main-menu.bg-none {background: transparent;}
.theme-main-menu.sticky-menu.fixed {
  z-index: 9999;
  padding-top: 5px;
  padding-bottom: 5px;
  background: #fff;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}
.theme-main-menu .logo a {display: block;}
.theme-menu-one .logo img {max-width: 120px;}
.theme-menu-one .right-button-group a {
  border-radius: 5px;
  font-weight: 500;
  font-size: 13px;
  line-height: 45px;
  text-transform: uppercase;
  text-align: center;
  color: var(--heading);
  transition: all 0.3s ease-in-out;
}
.theme-menu-one .right-button-group .signIn-action:hover {color: var(--blue-dark);}
.theme-menu-one .right-button-group .signUp-action {
  width: 155px;
  color: #fff;
  background: var(--blue-dark);
  box-shadow: 0px 15px 40px rgba(161,146,250,0.35);
  margin-left: 35px;
}
.theme-menu-one .right-button-group .signUp-action:hover {background: #FFBA12; box-shadow: 0px 15px 40px rgba(255,186,18,0.3);}
.theme-menu-one .right-button-group {margin-left: 100px;}
/*---------------------Theme Hero Banner/One ---------------*/
.hero-banner-one {position: relative;}
.hero-banner-one .hero-upper-container {
  position: relative; 
  padding: 60px 0 165px;
  text-align: center;
  z-index: 5;
}
.hero-banner-one .hero-heading span {position: relative; color: var(--blue-dark); display: inline-block;}
.hero-banner-one .hero-heading span:before {
  content: '';
  width: 96%;
  height: 12px;
  border-radius: 6px;
  background: var(--blue-dark);
  opacity: 0.16;
  position: absolute;
  bottom: 12px;
  left: 8px;
  z-index: -1;
}
.hero-banner-one .hero-sub-heading {font-size: 20px; padding: 20px 0 50px;}
.hero-banner-one .subscription-form {
  max-width: 610px;
  margin: 0 auto;
  position: relative;
}
.hero-banner-one .subscription-form input {
  width: 100%;
  height: 58px;
  border:1px solid #E2E2E2;
  border-radius: 30px;
  padding: 0 170px 0 30px;
  font-size: 16px;
}
.hero-banner-one .subscription-form button {
  position: absolute;
  top:0;
  right: 0;
  bottom: 0;
  background: var(--blue-dark);
  border-radius: 0 30px 30px 0;
  color: #fff;
  font-size: 17px;
  width: 165px;
  transition: all 0.3s ease-in-out;
}
.hero-banner-one .subscription-form button:hover {background: #FFBA12;}
.hero-banner-one .sing-in-call {font-size: 16px; padding-top: 18px;}
.hero-banner-one .sing-in-call a {color: var(--blue-dark); transition: all 0.25s ease-in-out;}
.hero-banner-one .sing-in-call a:hover {text-decoration: underline;}
.hero-banner-one [class*="icon-box"] {position: absolute; background: #fff; border-radius: 50%;}
.hero-banner-one [class*="icon-box"] img {
  margin: 0 auto;
  position: relative;
  top:50%;
  transform: translateY(-50%);
}
.hero-banner-one .icon-box-one {
  width: 58px;
  height: 58px;
  box-shadow: 0px 20px 50px rgba(31,36,44,0.07);
  top:9%;
  left: 17%;
  animation: jumpTwo 3.5s infinite linear;
}
.hero-banner-one .icon-box-one img {width: 29px;}
.hero-banner-one .icon-box-two {
  width: 90px;
  height: 90px;
  box-shadow: 10px 20px 50px rgba(31,36,44,0.07);
  top:32%;
  left: 5%;
  animation: jumpTwo 4s infinite linear;
}
.hero-banner-one .icon-box-two img {width: 42px;}
.hero-banner-one .icon-box-three {
  width: 70px;
  height: 70px;
  box-shadow: 10px 25px 70px rgba(31,36,44,0.07);
  top:45%;
  left: 20%;
  animation: jumpThree 3.5s infinite linear;
}
.hero-banner-one .icon-box-three img {width: 30px;}
.hero-banner-one .icon-box-four {
  width: 75px;
  height: 75px;
  box-shadow: 10px 30px 60px rgba(31,36,44,0.07);
  bottom:12%;
  left: 10%;
  animation: jumpThree 3s infinite linear;
}
.hero-banner-one .icon-box-four img {width: 40px;}
.hero-banner-one .icon-box-five {
  width: 58px;
  height: 58px;
  box-shadow: 0px 20px 50px rgba(31,36,44,0.07);
  top:9%;
  right: 17%;
  animation: jumpTwo 3.5s infinite linear;
}
.hero-banner-one .icon-box-five img {width: 35px;}
.hero-banner-one .icon-box-six {
  width: 90px;
  height: 90px;
  box-shadow: 10px 20px 50px rgba(31,36,44,0.07);
  top:32%;
  right: 5%;
  animation: jumpTwo 4s infinite linear;
}
.hero-banner-one .icon-box-six img {width: 54px;}
.hero-banner-one .icon-box-seven {
  width: 70px;
  height: 70px;
  box-shadow: 10px 25px 70px rgba(31,36,44,0.07);
  top:45%;
  right: 20%;
  animation: jumpThree 3.5s infinite linear;
}
.hero-banner-one .icon-box-seven img {width: 26px;}
.hero-banner-one .icon-box-eight {
  width: 75px;
  height: 75px;
  box-shadow: 10px 30px 60px rgba(31,36,44,0.07);
  bottom:12%;
  right: 10%;
  animation: jumpThree 3s infinite linear;
}
.hero-banner-one .icon-box-eight img {width: 30px;}
.hero-banner-one [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.hero-banner-one [class*="bubble"]:before {
  content: '';
  border-radius: 50%;
  position: absolute;
}
.hero-banner-one .bubble-one {
  width: 120px;
  height: 120px;
  left: 4%;
  top:15%;
  animation: rotated 12s infinite linear;
}
.hero-banner-one .bubble-one:before {
  width: 6px;
  height: 6px;
  background: #FAC3FF;
  top:0;
  left: 50%;
}
.hero-banner-one .bubble-two {
  width: 160px;
  height: 160px;
  top:30%;
  left: 15%;
  animation: rotated 17s infinite linear;
}
.hero-banner-one .bubble-two:before {
  width: 10px;
  height: 10px;
  background: #8FE9E0;
  top:50%;
  right: 100%;
}
.hero-banner-one .bubble-three {
  width: 120px;
  height: 120px;
  bottom: 33%;
  left: 4%;
  animation: rotated 12s infinite linear;
}
.hero-banner-one .bubble-three:before {
  width: 6px;
  height: 6px;
  background: #FFD5AD;
  bottom:0;
  left: 50%;
}

.hero-banner-one .bubble-four {
  width: 120px;
  height: 120px;
  right: 4%;
  top:15%;
  animation: rotated 12s infinite linear;
}
.hero-banner-one .bubble-four:before {
  width: 6px;
  height: 6px;
  background: #8FE9E1;
  top:0;
  left: 50%;
}
.hero-banner-one .bubble-five {
  width: 160px;
  height: 160px;
  top:30%;
  right: 15%;
  animation: rotated 17s infinite linear;
}
.hero-banner-one .bubble-five:before {
  width: 10px;
  height: 10px;
  background: #FFD5AD;
  top:50%;
  right: 100%;
}
.hero-banner-one .bubble-six {
  width: 120px;
  height: 120px;
  bottom: 33%;
  right: 4%;
  animation: rotated 12s infinite linear;
}
.hero-banner-one .bubble-six:before {
  width: 6px;
  height: 6px;
  background: #FAC3FF;
  bottom:0;
  left: 50%;
}
/*------------------ Fancy Feature One --------------*/
.fancy-feature-one {
  background-image: url(../images/assets/dot-bg-01.svg);
  background-repeat: no-repeat;
  background-position: center 230px;
  background-size: cover;
  position: relative;
  z-index: 5;
}
.fancy-feature-one .feature-img-area {
  display: inline-block;
  position: relative;
  z-index: 5;
  padding-right: 125px;
}
.fancy-feature-one .feature-img-area:before {
  content: '';
  position: absolute;
  left: 35px;
  right: 160px;
  background: #fff;
  height: 200px;
  top:-25px;
  border:1px solid #F1F1F1;
  border-radius: 10px;
  box-shadow: 0px -3px 21px rgba(0,0,0,0.04);
  z-index: -1;
}
.fancy-feature-one .feature-img-area:after {
  content: '';
  position: absolute;
  left: 80px;
  right: 205px;
  background: #fff;
  height: 200px;
  top:-50px;
  border:1px solid #F1F1F1;
  border-radius: 10px;
  box-shadow: 0px -3px 21px rgba(0,0,0,0.04);
  z-index: -2;
}
.fancy-feature-one .feature-img-area .screen-one {
  width: 51%;
  right: -13%;
  bottom: -46%;
  z-index: 1;
  animation: jumpTwo 3s infinite linear;
}
.fancy-feature-one .feature-img-area .screen-two {
  width: 38%;
  right: 0;
  top: 33%;
}
.fancy-feature-one .feature-img-area .screen-three {
  width: 32%;
  left: 21.5%;
  top: 47%;
}
.fancy-feature-one .feature-img-area .screen-four {
  width: 32%;
  left: 35%;
  top: 61%;
}
.fancy-feature-one .feature-img-area .screen-five {
  width: 25%;
  right: 19.5%;
  bottom: 16%;
  z-index: 0;
}
.fancy-feature-one .block-style-one {margin-top: 240px;}
.block-style-one .inner-container {position: relative;}
.block-style-one .icon-box {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  position: relative;
  border-width: 5px;
  border-style: solid;
  margin: 0 auto;
  cursor: pointer;
}
.block-style-one .icon-box:after {
  content: "\f10a";
  font-family: "Flaticon";
  position: absolute;
  top:-50px;
  left: 59%;
  transform: translateX(-50%);
  font-size: 26px;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-one .icon-box:before {
  content: '';
  position: absolute;
  top:-5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border-width: 10px;
  border-style: solid;
  z-index: -1;
}
.block-style-one .block-meta-data:hover .icon-box:before {animation: hvr-ripple-out 2.75s infinite linear;}
.block-style-one .block-meta-data:nth-child(1) .icon-box {border-color:  #FF6080;color: #FF6080;}
.block-style-one .block-meta-data:nth-child(1) .icon-box:before {border-color: #FFD8E2;}
.block-style-one .block-meta-data:nth-child(2) .icon-box {border-color: #02E7E8; color: #02E7E8;}
.block-style-one .block-meta-data:nth-child(2) .icon-box:before {border-color: #C6FFF6;}
.block-style-one .block-meta-data:nth-child(3) .icon-box {border-color:  #936DFF;color: #936DFF;}
.block-style-one .block-meta-data:nth-child(3) .icon-box:before {border-color: #E3DDF4;}
.block-style-one .block-meta-data:nth-child(4) .icon-box {border-color:  #FFBA12;color: #FFBA12;}
.block-style-one .block-meta-data:nth-child(4) .icon-box:before {border-color: #FFE0B8;}
.block-style-one .icon-box img {
  margin:0 auto; 
  position: relative; 
  top:50%;
  transform: translateY(-50%);
}
.block-style-one .block-meta-data p {font-size: 20px; padding: 25px 0 80px; position: relative;}
.block-style-one .block-meta-data p:before {
  content: '';
  position: absolute;
  width: 25px;
  height: 25px;
  border-top:1px solid #E9E9E9;
  border-left:1px solid #E9E9E9;
  background: #fff;
  transform: rotate(45deg);
  left: 45%;
  bottom: 25px;
  z-index: 1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-one .block-meta-data .hover-content {
  font-size: 15px;
  border:1px solid #E9E9E9;
  border-radius: 20px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: #fff;
  width: 100%;
  text-align: left;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}
.block-style-one .block-meta-data .hover-content span {position: relative; z-index: 5; display: block; padding: 3px 20px;}
.block-style-one .block-meta-data:hover .hover-content,
.block-style-one .block-meta-data:hover p:before,
.block-style-one .block-meta-data:hover .icon-box:after {opacity: 1; visibility: visible;}
.block-style-one .block-meta-data .line-dot-container {position: relative;}
.block-style-one .block-meta-data .line-dot-container:before {
  content: '';
  position: absolute;
  right: -187px;
  top:42px;
  width: 160px;
  height: 1px;
  border:1px dashed rgba(151,151,151,0.35);
}
.block-style-one .block-meta-data:last-child .line-dot-container:before {display: none;}
/*------------------ Fancy Feature Two --------------*/
.block-style-two .img-holder  {position: relative;}
.block-style-two {margin: 0 -60px}
.block-style-two> [class*="col-"] {padding: 0 60px;}
.block-style-two .text-wrapper h6 {
  font-size: 16px; 
  text-transform: uppercase;
  letter-spacing: 1.6px;
  padding-bottom: 15px;
}
.block-style-two .text-wrapper h3 {font-size: 36px; line-height: 1.25em; padding-bottom: 26px;}
.block-style-two .text-wrapper a {
  font-size: 16px;
  color: var(--blue-dark);
  margin-top: 18px;
}
.block-style-two .text-wrapper a:hover {text-decoration: underline;}
.block-style-two .text-wrapper .quote {
  border-top:1px solid #ECECEC;
  margin-top: 22px;
  padding-top: 20px;
}
.block-style-two .text-wrapper .quote blockquote {
  font-size: 15px;
  padding-bottom: 22px;
  line-height: 25px;
}
.block-style-two .text-wrapper .quote img {
  width: 42px;
  height: 42px;
  border-radius: 50%;
}
.block-style-two .text-wrapper .quote .info-meta {padding-left: 15px;}
.block-style-two .text-wrapper .quote .info-meta h5 {
  font-size: 16px;
  font-weight: 500;
}
.block-style-two .text-wrapper .quote .info-meta span {font-size: 16px;color: #7034FF; display: block;}
.block-style-two .img-holder-one .screen-one {
  z-index: -5;
  top:-19%;
  right: 15%;
  max-width: inherit;
}
.block-style-two .img-holder-one .dot-shape {
  z-index: -1;
  top:32%;
  right: 37%;
  width: 60%;
}
.block-style-two [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.block-style-two [class*="bubble"]:before {
  content: '';
  border-radius: 50%;
  position: absolute;
}
.block-style-two .img-holder-one .bubble-one {
  width: 120px;
  height: 120px;
  left: 23%;
  top:-3%;
  animation: rotated 14s infinite linear;
}
.block-style-two .img-holder-one .bubble-one:before {
  width: 8px;
  height: 8px;
  background: #AE78FF;
  top:0;
  left: 50%;
}
.block-style-two .img-holder-one .bubble-two {
  width: 160px;
  height: 160px;
  top:28%;
  left: -21%;
  animation: rotatedTwo 25s infinite linear;
}
.block-style-two .img-holder-one .bubble-two:before {
  width: 17px;
  height: 17px;
  background: #FFB15F;
  top:50%;
  right: 100%;
}
.block-style-two .img-holder-one .bubble-three {
  width: 120px;
  height: 120px;
  bottom: -7%;
  left: -10%;
  animation: rotated 15s infinite linear;
}
.block-style-two .img-holder-one .bubble-three:before {
  width: 7px;
  height: 7px;
  background: #FF53B3;
  bottom:0;
  left: 50%;
}

.block-style-two .img-holder-one .bubble-four {
  width: 120px;
  height: 120px;
  right: 27%;
  bottom:-13%;
  animation: rotatedTwo 17s infinite linear;
}
.block-style-two .img-holder-one .bubble-four:before {
  width: 12px;
  height: 12px;
  background: #2EEAED;
  bottom:0;
  left: 50%;
}
.block-style-two .img-holder-one .progress-line-one {
  width: 16%;
  height: 0.95%;
  background: #03C4C6;
  border-radius: 4px;
  right: 28%;
  top:33.5%;
}
.block-style-two .img-holder-one .progress-line-two {
  width: 16%;
  height: 0.85%;
  background: #FF3793;
  border-radius: 4px;
  left: 10%;
  top:52.6%;
}
.block-style-two .img-holder-two .screen-one {
  top:-6%;
  right: -29%;
  z-index: 1;
  animation: jumpTwo 3s infinite linear;
}
.block-style-two .img-holder-two .screen-two {
  bottom:1%;
  right: -97%;
  z-index: -5;
  max-width: inherit;
}
.block-style-two .img-holder-two .dot-shape {
  z-index: -1;
  top:44%;
  right: 38%;
  width: 69%;
}
.block-style-two .img-holder-two .bubble-one {
  width: 120px;
  height: 120px;
  left: 15%;
  top:-8%;
  animation: rotated 14s infinite linear;
}
.block-style-two .img-holder-two .bubble-one:before {
  width: 7px;
  height: 7px;
  background: #FFB651;
  top:0;
  left: 50%;
}
.block-style-two .img-holder-two .bubble-two {
  width: 160px;
  height: 160px;
  top:-31%;
  right: -71%;
  animation: rotatedTwo 25s infinite linear;
}
.block-style-two .img-holder-two .bubble-two:before {
  width: 17px;
  height: 17px;
  background: #FF6CC4;
  top:50%;
  right: 100%;
}
.block-style-two .img-holder-two .bubble-three {
  width: 100px;
  height: 100px;
  top: 47%;
  right: -58%;
  animation: rotated 17s infinite linear;
}
.block-style-two .img-holder-two .bubble-three:before {
  width: 8px;
  height: 8px;
  background: #4BE8DA;
  bottom:0;
  left: 50%;
}

.block-style-two .img-holder-two .bubble-four {
  width: 130px;
  height: 130px;
  left: 51%;
  bottom:-12%;
  animation: rotatedTwo 15s infinite linear;
}
.block-style-two .img-holder-two .bubble-four:before {
  width: 13px;
  height: 13px;
  background: #A49BFF;
  bottom:0;
  left: 50%;
}

.block-style-two .img-holder-three .screen-one {
  bottom:-42%;
  left: -30%;
  z-index: 1;
  width: 79%;
  animation: jumpTwo 3s infinite linear;
}
.block-style-two .img-holder-three .screen-two {
  top:-26%;
  left: -71%;
  z-index: -5;
  max-width: inherit;
}
.block-style-two .img-holder-three .dot-shape {
  z-index: -1;
  top:44%;
  right: 14%;
  width: 58%;
}
.block-style-two .img-holder-three .bubble-one {
  width: 95px;
  height: 95px;
  left: 24%;
  top:-12%;
  animation: rotated 14s infinite linear;
}
.block-style-two .img-holder-three .bubble-one:before {
  width: 8px;
  height: 8px;
  background: #6AEE90;
  top:0;
  left: 50%;
}
.block-style-two .img-holder-three .bubble-two {
  width: 160px;
  height: 160px;
  top:9%;
  left: -27%;
  animation: rotatedTwo 25s infinite linear;
}
.block-style-two .img-holder-three .bubble-two:before {
  width: 15px;
  height: 15px;
  background: #FF6CC4;
  top:50%;
  right: 100%;
}
.block-style-two .img-holder-three .bubble-three {
  width: 100px;
  height: 100px;
  bottom: -5%;
  left: -42%;
  animation: rotated 17s infinite linear;
}
.block-style-two .img-holder-three .bubble-three:before {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  bottom:0;
  left: 50%;
}

.block-style-two .img-holder-three .bubble-four {
  width: 130px;
  height: 130px;
  left: 14%;
  bottom:-42%;
  animation: rotatedTwo 15s infinite linear;
}
.block-style-two .img-holder-three .bubble-four:before {
  width: 14px;
  height: 14px;
  background: #FFCD8B;
  bottom:0;
  left: 50%;
}
/*------------------ Useable Tools  --------------*/
.useable-tools-section.bg-shape {
  position: relative;
  z-index: 5;
  background-image: url(../images/shape/7.svg);
  background-repeat: no-repeat;
  background-position: top center;
  background-size: contain;
  padding: 500px 0 350px;
}
.useable-tools-section.bg-color {
  background: var(--blue-dark);
  padding: 130px 0 120px;
}
.useable-tools-section h6 {
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  padding-bottom: 15px;
  color: #fff;
}
.useable-tools-section.bg-transparent h6 {color: rgba(0,0,0,0.3);}
.useable-tools-section.bg-transparent h2 {color: #2A2A2A; margin: 0;}
.useable-tools-section h2 {
  font-family: 'gilroy-black';
  font-size: 58px;
  line-height: 1.20em;
  color: #fff;
  margin: 20px 0 120px;
}
.useable-tools-section .sub-text {
  text-align: center;
  font-size: 22px;
  line-height: 1.59em;
  color: #000;
}
.useable-tools-section .img-box a {display: block;height: 100%;}
.useable-tools-section .img-box img {
  position: relative;
  top:50%;
  transform: translateY(-50%);
  margin: 0 auto;
}
.useable-tools-section .img-box {
  background: #fff;
  border-radius: 50%;
  box-shadow: 15.436px 30.294px 50px 0px rgba(103, 43, 246, 0.5);
  width: 175px;
  height: 175px;
  margin: 0 auto 25px;
  transition: all 0.3s ease-in-out;
}
.useable-tools-section.bg-transparent .img-box {box-shadow: 15px 30px 50px rgba(23,32,90,0.06);}
.useable-tools-section .img-box:hover {transform: scale(1.1);}
.useable-tools-section .img-box.bx-b {width: 121px;height: 121px; margin-top: 45px;}
.useable-tools-section .img-box.bx-d {width: 151px;height: 151px; margin-top: 25px;}
.useable-tools-section .img-box.bx-f {width: 135px;height: 135px; margin-top: 20px;}
.useable-tools-section .img-box.bx-g {width: 197px;height: 197px;}
.useable-tools-section .img-box.bx-h {width: 138px;height: 138px; margin-top: 20px;}
.useable-tools-section [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.useable-tools-section [class*="bubble"]:before {
  content: '';
  border-radius: 50%;
  position: absolute;
  background: #9A71FF;
}
.useable-tools-section .bubble-one {
  width: 120px;
  height: 120px;
  left:43%;
  top:20%;
  animation: rotated 14s infinite linear;
}
.useable-tools-section .bubble-one:before {
  width: 6px;
  height: 6px;
  top:0;
  right: 50%;
}
.useable-tools-section .bubble-two {
  width: 140px;
  height: 140px;
  right:20%;
  top:14%;
  animation: rotatedTwo 14s infinite linear;
}
.useable-tools-section .bubble-two:before {
  width: 12px;
  height: 12px;
  top:0;
  left: 50%;
}
.useable-tools-section .bubble-three {
  width: 95px;
  height: 95px;
  left:10%;
  top:44%;
  animation: rotated 14s infinite linear;
}
.useable-tools-section .bubble-three:before {
  width: 10px;
  height: 10px;
  top:100%;
  left: 50%;
}
.useable-tools-section .bubble-four {
  width: 95px;
  height: 95px;
  right:10%;
  top:48%;
  animation: rotatedTwo 14s infinite linear;
}
.useable-tools-section .bubble-four:before {
  width: 8px;
  height: 8px;
  top:0;
  left: 50%;
}
.useable-tools-section .bubble-five {
  width: 95px;
  height: 95px;
  right:56%;
  bottom:8%;
  animation: rotated 14s infinite linear;
}
.useable-tools-section .bubble-five:before {
  width: 8px;
  height: 8px;
  top:0;
  left: 50%;
}
/*-------------------- Fancy Feature Three -------------------*/
.counter-info-classic {
  max-width: 445px;
  height: 442px;
  position: relative;
  z-index: 5;
  margin-top: 100px;
}
.counter-box-one {
  border-radius: 50%;
  position: absolute;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.counter-box-one.color-one {
  background: rgba(0,166,255,0.65);
  width: 195px;
  height: 195px;
  top:40px;
  left: 40px;
  z-index: -2;
}
.counter-box-one.color-two {
  background: rgba(140,39,255,0.55);
  width: 154px;
  height: 154px;
  top:136px;
  left: 248px;
  z-index: 2;
}
.counter-box-one.color-three {
  background: rgba(255,173,58,0.72);
  width: 212px;
  height: 212px;
  top:200px;
  left: 110px;
  z-index: 1;
}
.counter-box-one .number {color: #fff; font-size: 52px;}
.counter-box-one p {font-size: 20px; color: #fff;}
.counter-info-classic [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.counter-info-classic .bubble-one {
  width: 8px;
  height: 8px;
  background: #7DEC72;
  top:0;
  left: 62%;
  animation: scale-up-three 4s infinite linear;
}
.counter-info-classic .bubble-two {
  width: 22px;
  height: 22px;
  background: #FFB56A;
  top:15%;
  left: 63%;
  animation: scale-up-one 4s infinite linear;
}
.counter-info-classic .bubble-three {
  width: 8px;
  height: 8px;
  background: #88D9FF;
  top:16%;
  right: 11%;
  animation: scale-up-three 4s infinite linear;
}
.counter-info-classic .bubble-four {
  width: 17px;
  height: 17px;
  background: #FF6CC4;
  bottom:24%;
  right: 13%;
  animation: scale-up-one 4s infinite linear;
}
.counter-info-classic .bubble-five {
  width: 8px;
  height: 8px;
  background: #77F4F5;
  bottom:20%;
  right: 5%;
  animation: scale-up-three 4s infinite linear;
}
.counter-info-classic .bubble-six {
  width: 26px;
  height: 26px;
  background: #A595FF;
  bottom:32%;
  left: 0;
  animation: scale-up-one 4s infinite linear;
}
.counter-info-classic .bubble-seven {
  width: 9px;
  height: 9px;
  background: #66E19E;
  bottom:40%;
  left: 14%;
  animation: scale-up-three 4s infinite linear;
}
.counter-info-classic .dot-shape {
  z-index: -5;
  top:35px;
  right: 0;
}
.clients-feedback-classic {
  position: relative;
  padding: 20px 0 0 90px;
  z-index: 5;
}
.clients-feedback-classic:before {
  content: url(../images/shape/9.svg);
  position: absolute;
  top:-120px;
  right: -185px;
  z-index: -1;
}
.clients-feedback-classic .feedback-wrapper {
  background: #fff;
  position: relative;
  box-shadow: 0 20px 80px rgba(49,62,103,0.07);
  border-radius: 5px;
  padding: 54px 64px 48px;
  margin-bottom: 108px;
}
.clients-feedback-classic .feedback-wrapper:before {
  content: url(../images/icon/05.svg);
  position: absolute;
  bottom: 55px;
  right: 60px;
}
.clients-feedback-classic .feedback-wrapper p {padding-bottom: 30px;}
.clients-feedback-classic .feedback-wrapper .media-meta {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  position: absolute;
  top:-25px;
  left: -35px;
  z-index: 1;
}
.clients-feedback-classic .feedback-wrapper .name {
  font-family: 'gilroy-semibold';
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 1.6px;
}
.clients-feedback-classic .feedback-wrapper .disg-info {
  display: block;
  font-size: 16px;
  color: #B7B7B7;
}
.clients-feedback-classic .feedback-wrapper:last-child {margin-bottom: -100px;}
/*------------------ Fancy Short Banner One ----------------*/
.fancy-short-banner-one {
  position: relative;
  z-index: 3;
  background: linear-gradient(45deg, #FFFBF2, #EDFFFD);
  padding: 235px 0 170px;
}
.fancy-short-banner-one.space-fix {padding: 170px 0 170px;}
.fancy-short-banner-one:before,
.fancy-short-banner-one:after {
  content: url(../images/shape/10.svg);
  position: absolute;
  opacity: 0.9;
}
.fancy-short-banner-one:before {top:-133px; left: 0; animation: jumpTwo 4s infinite linear;}
.fancy-short-banner-one:after {bottom:-133px; right: 0; animation: jumpThree 4s infinite linear;}

.fancy-short-banner-one .form-wrapper {padding-left: 65px;}
.fancy-short-banner-one .form-wrapper form {position: relative;}
.fancy-short-banner-one .form-wrapper form input {
  font-family: 'Inter', serif;
  font-size: 16px;
  width: calc(100% - 180px);
  border:none;
  border-bottom: 2px solid #545454;
  height: 50px;
  background: transparent;
}
.fancy-short-banner-one .form-wrapper form button {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 150px;
  height: 50px;
  border-radius: 25px;
  color: #fff;
  font-weight: 500;
  font-size: 16px;
  background: var(--blue-dark);
  box-shadow: 0 15px 30px rgba(139,110,209,0.3);
  transition: all 0.3s ease-in-out;
}
.fancy-short-banner-one .form-wrapper form button:hover {background: #FFBA12; box-shadow: 0px 15px 30px rgba(255,186,18,0.2);}
.fancy-short-banner-one .form-wrapper p {
  font-size: 16px;
  padding-top: 10px;
}
.fancy-short-banner-one .form-wrapper p a {color: var(--blue-dark);}
.fancy-short-banner-one .form-wrapper p a:hover {text-decoration: underline;}
.fancy-short-banner-one [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.fancy-short-banner-one .bubble-one {
  width: 17px;
  height: 17px;
  background: #FF6CC4;
  top:13%;
  left: 41%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-short-banner-one .bubble-two {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  top:29%;
  right: 11%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-one .bubble-three {
  width: 20px;
  height: 20px;
  background: #FFBA65;
  bottom:-10px;
  right: 29%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-short-banner-one .bubble-four {
  width: 8px;
  height: 8px;
  background: #6AEE90;
  bottom:22%;
  left: 42%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-one .bubble-five {
  width: 25px;
  height: 25px;
  background: #B183FF;
  bottom:-12px;
  left: 12%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-short-banner-one .bubble-six {
  width: 8px;
  height: 8px;
  background: #FFCD8B;
  top:32%;
  left: 14%;
  animation: scale-up-three 4s infinite linear;
}
/*----------------------- Faq Classic ----------------------*/
.faq-classic.with-bg {
  background: #F7FAFF;
  padding: 120px 0 250px;
  z-index: 5;
  position: relative;
}
.faq-classic.with-bg .shape-one {
  top:13%;
  right: 4%;
}
.faq-classic.with-bg .shape-two {
  top:36%;
  right: 11%;
}
.faq-classic.with-bg .shape-three {
  top:63%;
  right: 0;
}
.faq-classic.with-bg .shape-four {
  top:63%;
  left: 0%;
}
.faq-classic.with-bg .shape-five {
  top:41%;
  left: 5%;
}
.faq-classic.with-bg .shape-six {
  top:14%;
  left: 0;
}
.faq-classic .card {
  background: #fff;
  box-shadow: 0px 5px 20px 0px rgb(90 111 155 / 5%);
  margin-bottom: 20px;
  border-radius: 0;
  border: none;
}
.faq-classic .card .card-header {
  background: transparent;
  border-radius: 0;
  padding: 0;
  border:none;
}
.faq-classic .card .card-header button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 20px 15px 20px 70px;
  font-family: 'Inter', serif;
  font-size: 22px;
  border:none;
  border-radius: 0;
  margin: 0;
  color: var(--heading);
  text-decoration: none;
  position: relative;
}
.faq-classic .card .card-header button:before {  
  content: "+";
  position: absolute;
  font-size: 24px;
  left: 40px;
  top:50%;
  transform: translateY(-50%);
  transition: all 0.3s ease-in-out;
}
.faq-classic .card .card-body {padding: 0 50px 20px 70px;}
.faq-classic .card .card-body p {font-size: 17px;}
.faq-classic .card .card-body p a {
  text-decoration: underline;
  color: var(--p-color);
}
.faq-classic .card:last-child .card-body {border:none;}
/*------------------ Footer One --------------*/
.footer-bg-wrapper {position: relative; padding-top: 230px;}
.footer-bg-wrapper:before {
  content: url(../images/shape/11.svg);
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
}
.footer-bg-wrapper [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.footer-bg-wrapper .bubble-one {
  width: 25px;
  height: 25px;
  background: #FF6CC4;
  top:4%;
  left: 26%;
  animation: scale-up-one 4s infinite linear;
}
.footer-bg-wrapper .bubble-two {
  width: 14px;
  height: 14px;
  background: #FFBE50;
  top:48%;
  left: 10%;
  animation: scale-up-one 4s infinite linear;
}
.footer-bg-wrapper .bubble-three {
  width: 7px;
  height: 7px;
  background: #04E8F4;
  bottom:18%;
  left: 20%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-two .content-wrapper {position: relative;}
.fancy-short-banner-two .content-wrapper:before {
  content: url(../images/shape/12.svg);
  position: absolute;
  top:-117px;
  right: -117px;
  z-index: -1;
  opacity: 0.9;
  animation: jumpTwo 5s infinite linear;
}
.fancy-short-banner-two .bg-wrapper {
  position: relative;
  overflow: hidden;
  background: #7034FF;
  box-shadow: 0 20px 60px 0 rgba(104,103,255,0.2);
  border-radius: 5px;
  padding: 50px 55px;
  z-index: 1;
}
.fancy-short-banner-two h2 {
  font-size: 36px;
  line-height: 1.16em;
  color: #fff;
  max-width: 560px;
}
.fancy-short-banner-two a {
  width: 178px;
  line-height: 54px;
  background: #fff;
  border-radius: 5px;
  color: var(--blue-dark);
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.fancy-short-banner-two a:hover {background: #FFBA12; color: #fff;}
.fancy-short-banner-two [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.fancy-short-banner-two .bubble-one {
  width: 13px;
  height: 13px;
  background: rgba(216,216,216,0.14);
  top:27%;
  left: 60%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-short-banner-two .bubble-two {
  width: 6px;
  height: 6px;
  background: rgba(216,216,216,0.14);
  top:62%;
  right: 28%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-two .bubble-three {
  width: 5px;
  height: 5px;
  background: rgba(216,216,216,0.14);
  bottom:37%;
  right: 51%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-two .bubble-four {
  width: 245px;
  height: 245px;
  background: rgba(216,216,216,0.14);
  top:-103px;
  right: -70px;
  animation: jello 3s infinite linear;
}
.fancy-short-banner-two .bubble-five {
  width: 68px;
  height: 68px;
  background: rgba(216,216,216,0.14);
  bottom:-24px;
  left: -28px;
  animation: jello 3s infinite linear;
}
.theme-footer-one {position: relative;}
.theme-footer-one:before {
  content: url(../images/assets/ils_01.svg);
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
}
.theme-footer-one .top-footer [class*="col-"] {margin-bottom: 30px;}
.theme-footer-one .top-footer .footer-title {
  font-family: 'Inter', serif;
  font-size: 24px;
  padding-bottom: 25px;
}
.theme-footer-one .top-footer .footer-list ul li a {
  font-size: 16px;
  line-height: 38px;
  color: #727272;
  transition: all 0.3s ease-in-out;
}
.theme-footer-one .top-footer .footer-list ul li a:hover {color: var(--blue-dark);}
.theme-footer-one .top-footer .address-list ul li a {
  font-size: 16px;
  color: #727272;
  margin-bottom: 10px;
}
.theme-footer-one .top-footer .address-list ul li a:hover {text-decoration: underline;}
.theme-footer-one .top-footer .address-list ul li a.mobile-num {
  font-size: 20px;
  color: #030303
}
.theme-footer-one .bottom-footer-content ul li {display: inline-block;}
.theme-footer-one .bottom-footer-content ul li a {
  font-size: 16px;
  color: #727272;
  margin-left: 20px;
}
.theme-footer-one .bottom-footer-content ul li a:hover {color: #000;}
.theme-footer-one .bottom-footer-content p {font-size: 15px; color: #6A6A6A;}
.theme-footer-one .bottom-footer-content {padding: 26px 0 30px;}
.theme-footer-one .bottom-footer-content ul {padding-right: 97px;}
/*--------------------- Fancy Hero One --------------------*/
.fancy-hero-one {
  position: relative;
  z-index: 5;
  text-align: center;
  padding: 75px 0 170px;
}
.fancy-hero-one h2 {font-size: 80px; line-height: 1.1em;}
.fancy-hero-one p {
  font-size: 24px;
  line-height: 1.75em;
  padding: 20px 25px 0;
  color: #232830;
}
.fancy-hero-one .page-title {
  font-size: 18px;
  text-transform: uppercase;
  color: rgba(42,42,42,0.3);
  letter-spacing: 1.4px;
}
.fancy-hero-one .search-form {
  max-width: 720px;
  height: 70px;
  margin: 55px auto 0;
  position: relative;
}
.fancy-hero-one .search-form input {
  width: 100%;
  height: 100%;
  border:2px solid #000;
  border-radius: 5px;
  padding: 0 95px 0 38px;
}
.fancy-hero-one .search-form button {
  width: 80px;
  background: var(--blue-dark);
  position: absolute;
  top:7px;
  right: 7px;
  bottom: 7px;
  z-index: 5;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}
.fancy-hero-one .search-form button img {margin: 0 auto;}
.fancy-hero-one .search-form button:hover {background: var(--yellow-deep);}
.fancy-hero-one [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.fancy-hero-one [class*="bubble"]:before {
  content: '';
  border-radius: 50%;
  position: absolute;
}
.fancy-hero-one .bubble-one {
  width: 120px;
  height: 120px;
  left: 4%;
  top:15%;
  animation: rotated 12s infinite linear;
}
.fancy-hero-one .bubble-one:before {
  width: 6px;
  height: 6px;
  background: #FAC3FF;
  top:0;
  left: 50%;
}
.fancy-hero-one .bubble-two {
  width: 160px;
  height: 160px;
  top:30%;
  left: 15%;
  animation: rotated 17s infinite linear;
}
.fancy-hero-one .bubble-two:before {
  width: 10px;
  height: 10px;
  background: #8FE9E0;
  top:50%;
  right: 100%;
}
.fancy-hero-one .bubble-three {
  width: 120px;
  height: 120px;
  bottom: 33%;
  left: 4%;
  animation: rotatedTwo 12s infinite linear;
}
.fancy-hero-one .bubble-three:before {
  width: 6px;
  height: 6px;
  background: #FFD5AD;
  bottom:0;
  left: 50%;
}

.fancy-hero-one .bubble-four {
  width: 120px;
  height: 120px;
  right: 5%;
  top:13%;
  animation: rotated 12s infinite linear;
}
.fancy-hero-one .bubble-four:before {
  width: 6px;
  height: 6px;
  background: #8FE9E1;
  top:0;
  left: 50%;
}
.fancy-hero-one .bubble-five {
  width: 160px;
  height: 160px;
  top:30%;
  right: 7%;
  animation: rotated 17s infinite linear;
}
.fancy-hero-one .bubble-five:before {
  width: 10px;
  height: 10px;
  background: #FFD5AD;
  top:50%;
  right: 100%;
}
.fancy-hero-one .bubble-six {
  width: 120px;
  height: 120px;
  bottom: 33%;
  right: 4%;
  animation: rotatedTwo 12s infinite linear;
}
.fancy-hero-one .bubble-six:before {
  width: 6px;
  height: 6px;
  background: #FAC3FF;
  bottom:0;
  left: 50%;
}
/*------------------- Fancy Text block One ----------------*/
.fancy-text-block-one {
  position: relative;
  z-index: 3;
  background: linear-gradient(45deg, #FFFBF2, #EDFFFD);
  padding: 55px 0;
}
.fancy-text-block-one:before,
.fancy-text-block-one:after {
  content: url(../images/shape/10.svg);
  position: absolute;
  opacity: 0.9;
}
.fancy-text-block-one:before {top:-133px; left: 0; animation: jumpTwo 4s infinite linear;}
.fancy-text-block-one:after {bottom:-133px; right: 0; animation: jumpThree 4s infinite linear;}
.fancy-text-block-one .quote-wrapper {padding-left: 25px;}
.fancy-text-block-one .quote-wrapper p {
  font-family: 'gilroy-semibold';
  font-size: 20px;
  color: #000000;
  padding-bottom: 21px;
}
.fancy-text-block-one .quote-wrapper p span {color: #FFB147;}
.fancy-text-block-one .quote-wrapper blockquote {
  font-size: 40px;
  line-height: 1.5em;
  color: #181818;
  position: relative;
  margin-bottom: 43px;
}
.fancy-text-block-one .quote-wrapper blockquote:before {
  content: url(../images/icon/06.svg);
  position: absolute;
  left: -92px;
  top:11px;
}
.fancy-text-block-one .quote-wrapper h6 {font-size: 20px; font-weight: 500;}
.fancy-text-block-one .quote-wrapper h6 span {font-weight: normal; color: #9C9C9C; font-size: 18px;}
.fancy-text-block-one [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.fancy-text-block-one .bubble-one {
  width: 17px;
  height: 17px;
  background: #FF6CC4;
  top:15%;
  left: 42%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-text-block-one .bubble-two {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  top:29%;
  right: 8%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-text-block-one .bubble-three {
  width: 20px;
  height: 20px;
  background: #FFBA65;
  bottom:7%;
  right: 26%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-text-block-one .bubble-four {
  width: 8px;
  height: 8px;
  background: #6AEE90;
  bottom:29%;
  left: 43%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-text-block-one .bubble-five {
  width: 25px;
  height: 25px;
  background: #B183FF;
  bottom:7%;
  left: 9%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-text-block-one .bubble-six {
  width: 8px;
  height: 8px;
  background: #FFCD8B;
  top:31%;
  left: 11%;
  animation: scale-up-three 4s infinite linear;
}
/*----------------- Counter Info Standard --------------*/
.counter-info-standard {border-bottom: 1px solid #EFEFEF;}
.counter-box-two {text-align: center; margin-bottom: 30px;}
.counter-box-two .number {font-size: 80px;color: var(--blue-dark);}
.counter-box-two em {
  font-size: 20px;
  display: block;
  color: #9E9E9E;
  font-style: normal;
}
.counter-box-two p {
  font-size: 28px;
  line-height: 1.35em;
  color: #212121;
  padding-top: 20px;
}
/*---------------- Fancy Text block Two -------------*/
.fancy-text-block-two .fancy_img_media {border-radius: 8px; margin-left: auto;}
/*---------------- Fancy Text block Three -------------*/
.fancy-text-block-three {position: relative; z-index: 1; padding-bottom: 110px;}
.fancy-text-block-three:before {
  content: '';
  width: 100%;
  position: absolute;
  bottom: 0;
  top:16%;
  left: 0;
  background: #FAFAFF;
  z-index: -2;
}
.fancy-text-block-three .feature-content {padding-bottom: 60px;}
.fancy-text-block-three .feature-content.light-bg {
  background: #5E43F0;
  border-radius: 5px 0 0 5px;
}
.fancy-text-block-three .feature-content.dark-bg {
  background: #462EC5;
  border-radius: 0 5px 5px 0;
}
.fancy-text-block-three .feature-content .header {padding: 65px 20px 55px 55px; border-bottom: 1px solid rgba(0,0,0,0.1);}
.fancy-text-block-three .feature-content .header h3 {
  font-family: 'gilroy-semibold';
  color: #fff;
  font-size: 50px;
}
.fancy-text-block-three .feature-content .header p {
  font-size:20px;
  line-height: 1.5em;
  color: #fff;
  padding-top: 18px;
}
.fancy-text-block-three .feature-content .feature-list {padding: 50px 20px 0 55px;}
.fancy-text-block-three .feature-content .feature-list .icon-box {
  width: 55px;
  height: 55px;
  background: #fff;
  border-radius: 50%;
}
.fancy-text-block-three .feature-content .feature-list .text-meta {
  padding-left: 20px;
  width: calc(100% - 55px);
}
.fancy-text-block-three .feature-content .feature-list h4 {
  font-family: 'Inter';
  font-size: 22px;
  color: #fff;
}
.fancy-text-block-three .feature-content .feature-list p {color: rgba(255,255,255,0.8); padding-top:7px;}
.fancy-text-block-three .slogan {
  text-align: center;
  font-size: 40px;
  line-height: 1.38em;
  padding-top: 80px;
  color: #000;
}
.fancy-text-block-three [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.fancy-text-block-three .bubble-one {
  width: 15px;
  height: 15px;
  background: #FF6CC4;
  top:27%;
  right: 10%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-text-block-three .bubble-two {
  width: 7px;
  height: 7px;
  background: #F5A623;
  top:70%;
  right: 10%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-text-block-three .bubble-three {
  width: 20px;
  height: 20px;
  background: #B183FF;
  bottom:16%;
  left: 9%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-text-block-three .bubble-four {
  width: 8px;
  height: 8px;
  background: #6AEE90;
  top:40%;
  left:8%;
  animation: scale-up-three 4s infinite linear;
}
/*----------------- Team Section One --------------*/
.team-section-one .controls{ border-bottom: 1px solid #E5E5E5; text-align: center; }
.team-section-one .controls .control {
  font-family: 'Inter', serif;
  font-size: 20px;
  display: inline-block;
  position: relative;
  margin: 0 25px;
  padding-bottom: 20px;
  color: #7F7F7F;
}
.team-section-one .controls .control:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: var(--blue-dark);
  left: 0;
  bottom: -1px;
  border-radius: 3px;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.team-section-one .controls .control.mixitup-control-active {color: var(--blue-dark);}
.team-section-one .controls .control.mixitup-control-active:before {opacity: 1;}
.team-section-one .mixitUp-container {text-align: center;}
.team-section-one .team-member {cursor: pointer; transition: all 0.3s ease-in-out;}
.team-section-one .team-member:hover {transform: translateY(-5px);}
.team-section-one .team-member img {
  width: 180px; 
  height: 180px; 
  border-radius: 50%;
  margin: 0 auto;
}
.team-section-one .team-member h4 {
  font-family: 'Roboto', sans-serif;
  font-size: 24px;
  padding: 25px 0 6px;
}
.team-section-one .team-member strong {color: #A5A5A5; font-weight: normal;}
.team-section-one .mix,.team-section-one .gap {
  width: calc(100%/4 - (((4 - 1) * 1rem) / 4)); 
  display: inline-block;
}
.team-section-one .mix {margin-bottom: 100px;}
/*-------------- Feature Blog One -------------*/
.feature-blog-one .header .title-style-one {max-width: 560px;}
.feature-blog-one .row [class*="col-"] {display: flex;}
.feature-blog-one .post-meta {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.08);
  margin-bottom: 50px;
  padding: 20px 20px 80px;
  position: relative;
  width: 100%;
  transition: all 0.5s ease-in-out;
}
.feature-blog-one .post-meta .image-meta {width: 100%;border-radius: 5px;}
.feature-blog-one .post-meta .tag {
  font-family: 'Inter', serif;
  font-size: 16px;
  color: rgba(10,10,10,0.24);
  padding: 25px 0 3px;
}
.feature-blog-one .post-meta .title {
  font-family: 'Inter';
  font-size: 30px;
  line-height: 1.2em;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.feature-blog-one .post-meta:hover a.title {color: var(--blue-dark);}
.feature-blog-one .post-meta .read-more {
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  color: var(--blue-dark);
  letter-spacing: 1px;
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
}
.feature-blog-one .post-meta .read-more i {font-size: 22px;}
/*---------------- Fancy Text block Four -------------*/
.fancy-text-block-four {background: #FCF7EF; position: relative; z-index: 5;}
.fancy-text-block-four:before {
  content: url(../images/shape/13.svg);
  position: absolute;
  top:-48px; 
  left: 0; 
  animation: jumpTwo 4s infinite linear;
}
.fancy-text-block-four:after {
  content: url(../images/shape/14.svg);
  position: absolute;
  bottom:-140px; 
  right: 0; 
  animation: jumpThree 4s infinite linear;
}
.fancy-text-block-four .wrapper {position: relative; z-index: 1;}
.fancy-text-block-four .wrapper:before {
  content: url(../images/assets/ils_02.svg);
  position: absolute;
  top:-219px;
  right: -253px;
  z-index: -1;
  animation: jumpTwo 10s infinite linear;
}
.block-style-three {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 15px 30px rgba(0,0,0,0.02);
  padding: 25px 40px 70px 50px;
  margin-top: 35px;
}
.block-style-three .icon {height: 75px;}
.block-style-three h4 {
  font-family: 'Inter';
  font-size: 22px;
  padding: 25px 0 20px;
}
.block-style-three p {
  font-size: 17px;
  line-height: 1.64em;
}
/*---------------- Fancy Text block Five -------------*/
.fancy-text-block-five {position: relative; z-index: 5;}
.fancy-text-block-five:before {
  content: url(../images/assets/ils_03.svg);
  position: absolute;
  top:150px;
  left: 7%;
  z-index: -1;
  animation: jumpTwo 10s infinite linear;
}
.block-style-four {
  background: #fff;
  border:1px solid #E6E6E6;
  border-radius: 5px;
  padding: 25px 30px 20px 40px;
  margin-top: 35px;
  transition: all 0.3s ease-in-out;
}
.block-style-four:hover {
  box-shadow: 0 25px 60px rgba(12,28,65,0.05);
  border-color: #fff;
  transform: translateY(-5px);
}
.block-style-four .icon {height: 75px;}
.block-style-four h4 {
  font-family: 'Inter';
  font-size: 24px;
  padding: 9px 0 20px;
}
.block-style-four p {
  font-size: 17px;
  line-height: 1.64em;
}
.block-style-four a {
  font-size: 30px; 
  color: #303030; 
  margin-top: 30px;
}
.block-style-four:hover a {color: var(--blue-dark);}
/*-------------- Feature Blog Two -------------*/
.feature-blog-two {background: #F2FBFD;}
.feature-blog-two .header .title-style-one {max-width: 560px;}
.feature-blog-two .row [class*="col-"] {display: flex;}
.feature-blog-two .post-meta {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.08);
  margin-top: 35px;
  padding: 20px 20px 80px;
  position: relative;
  transition: all 0.5s ease-in-out;
}
.feature-blog-two .post-meta:hover {transform: translateY(-10px);}
.feature-blog-two .post-meta .image-meta {width: 100%;border-radius: 5px;}
.feature-blog-two .post-meta .title {
  font-family: 'Inter';
  font-size: 30px;
  line-height: 1.2em;
  color: #000;
  transition: all 0.3s ease-in-out;
  margin-top: 50px;
}
.feature-blog-two .post-meta:hover .title {color: var(--blue-dark);}
.feature-blog-two .post-meta .read-more {
  font-family: 'Inter', serif;
  font-size: 16px;
  color: rgba(10,10,10,0.24);
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
  transition: all 0.2s ease-in-out;
}
.feature-blog-two .post-meta .read-more i {font-size: 22px; color: var(--blue-dark);}
.feature-blog-two .post-meta .read-more:hover {color: var(--blue-dark);}
/*------------------- Contact Us Light --------------*/
.contact-us-light {
  background: linear-gradient(45deg, #FFFBF2, #EDFFFD);
  position: relative;
  z-index: 1;
}
.contact-us-light:before {
  content: url(../images/shape/15.svg);
  position: absolute;
  left: 0;
  bottom:-140px;
}
.contact-us-light [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.contact-us-light .bubble-one {
  width: 30px;
  height: 30px;
  background: #FFDFDF;
  left: 39%;
  top:-15px;
  z-index: 2;
  animation: scale-up-one 4s infinite linear;
}
.contact-us-light .bubble-two {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  top:14%;
  right: 8%;
  animation: scale-up-three 4s infinite linear;
}
.contact-us-light .bubble-three {
  width: 8px;
  height: 8px;
  background: #6AEE90;
  top: 32%;
  left: 44%;
  animation: scale-up-three 4s infinite linear;
}
.contact-us-light .bubble-four {
  width: 8px;
  height: 8px;
  background: #FFCD8B;
  left: 11%;
  top:15%;
  animation: scale-up-three 4s infinite linear;
}
.contact-us-light .address-info {text-align: center; margin-bottom: 50px;}
.contact-us-light .address-info .icon {height: 82px;}
.contact-us-light .address-info .icon img {margin: 0 auto; max-height: 100%;}
.contact-us-light .address-info .title {
  font-size: 18px;
  color: #9E9E9E;
  padding: 30px 0 5px;
}
.contact-us-light .address-info p {
  font-size: 24px;
  line-height: 1.45em;
  color: #000;
}
.contact-us-light .address-info ul li a {
  font-size: 22px;
  margin: 5px 15px;
  color: rgba(0,0,0,0.2);
  transition: all 0.2s ease-in-out;
}
.contact-us-light .address-info ul li a:hover {color: var(--blue-dark);}
.form-style-light {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 10px 30px rgba(14,49,42,0.05);
  padding: 100px 100px;
  margin-top: 90px;
}
.form-style-light .input-group-meta {
  height: 55px;
  position: relative;
}
.form-style-light .input-group-meta.lg {height: 220px;}
.form-style-light .input-group-meta input {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  font-size: 16px;
  border: solid 1px #D6D6D6;
  padding: 0 52px 0 20px;
  color: var(--heading);
  background: transparent;
  font-family: 'Inter', serif;
}
.form-style-light .input-group-meta textarea {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  font-size: 16px;
  color: var(--heading);
  border: solid 1px #D6D6D6;
  resize: none;
  padding: 20px;
  font-family: 'Inter', serif;
}
.form-style-light .input-group-meta input:focus {border-color: var(--blue-dark);}
.form-style-light .input-group-meta label {
  font-size: 14px;
  padding: 0 6px;
  font-weight: normal;
  color: #CACACA;
  position: absolute;
  left: 20px;
  line-height: 10px;
  top:-5px;
  z-index: 1;
  background: #fff;
}
.form-style-light .input-group-meta .placeholder_icon {
  position: absolute;
  line-height: 55px;
  top:0;
  right:0;
  bottom: 0;
  width: 50px;
  text-align: center;
  z-index: 1;
  color: rgba(0,0,0,0.45);
  font-size: 17px;
  cursor: pointer;
}
.form-style-light .input-group-meta .placeholder_icon img {
  position: relative;
  top:50%;
  margin: 0 auto;
  transform: translateY(-50%);
  transition: all 0.2s ease-in-out;
}
.form-style-light .input-group-meta .valid-sign {opacity: 1; visibility: visible;}
.form-style-light .input-group-meta input:valid + .valid-sign {opacity: 0; visibility: hidden;}
#contact-form .form-group .help-block {
  position: absolute;
  left: 0;
  bottom: -24px;
  font-size: 14px;
  line-height: 22px;
  color: #fff;
  padding: 0 15px;
  border-radius: 3px;
  background: #dc3545;
  box-shadow: 0px 10px 25px 0px rgba(123,147,171,0.15);
}
#contact-form .form-group .help-block li {position: relative;}
#contact-form .form-group .help-block li:before {
  content: '';
  font-family: 'font-awesome';
  position: absolute;
  top:-12px;
  left:0;
  color: #dc3545;
}

/*=======================================================================
                              CUSTOMER SUPPORT                
=========================================================================*/
.theme-main-menu>div {position: relative;}
.theme-menu-two .logo {
  position: absolute;
  left: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-two .right-widget {
  position: absolute;
  right: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-two .user-login-button li a {
  font-size: 16px;
  font-weight: 500;
  line-height: 43px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.theme-menu-two .user-login-button li a:hover {color: var(--red-light);}
.theme-menu-two .user-login-button li .signUp-action {
  border:1px solid rgba(255,122,81,0.4);
  background: rgba(255,104,40,0.07);
  padding: 0 45px;
  border-radius: 22px;
  color: var(--red-light);
  margin-left: 25px;
}
.theme-menu-two .user-login-button li .signUp-action:hover {
  background: var(--red-light);
  color: #fff;
}
.theme-menu-two .language-button-group li a {
  font-size: 16px;
  font-weight: 500;
  color: #B7B7B7;
  margin: 0 4px;
}
.theme-menu-two .language-button-group li a.active {color: #000;}
.theme-menu-two .language-button-group {margin-right: 80px;}
/*---------------------Theme Hero Banner/Two ---------------*/
.hero-banner-two {padding: 90px 0 110px; position: relative; z-index: 1;}
.hero-banner-two .container {
  max-width: 1490px; 
  position: relative; 
  padding: 0 40px;
}
.hero-banner-two .hero-text-wrapper h1 {
  font-family: 'gilroy-black'; 
  font-size: 78px;
  line-height: 1.11em;
}
.hero-banner-two .hero-text-wrapper h1 span {position: relative;}
.hero-banner-two .hero-text-wrapper h1 span img {
  left: 0;
  bottom: -15px;
  width: 100%;
}
.hero-banner-two .hero-text-wrapper .sub-text {
  font-size: 28px;
  line-height: 1.5em;
  color: #565657;
  padding: 40px 60px 75px 0;
}
.hero-banner-two .hero-text-wrapper form {
  max-width: 550px;
  height: 70px;
  position: relative;
}
.hero-banner-two .hero-text-wrapper form input {
  border: 2px solid #040404;
  border-radius: 5px;
  width: 100%;
  height: 100%;
  padding: 0 200px 0 30px;
}
.hero-banner-two .hero-text-wrapper form button {
  position: absolute;
  right: 10px;
  top:8px;
  bottom: 8px;
  background: var(--red-light);
  width: 184px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-size: 17px;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
}
.hero-banner-two .hero-text-wrapper form button:hover {background: var(--yellow-deep);}
.hero-banner-two .hero-text-wrapper .list-item li {
  font-size: 15px;
  color: #141518;
  position: relative;
  padding-left: 22px;
  margin-right: 13px;
}
.hero-banner-two .hero-text-wrapper .list-item.lg li {
  font-size: 19px;
  line-height: 1.57em;
  position: relative;
  padding-left: 22px;
  margin-bottom: 15px;
}
.hero-banner-two .hero-text-wrapper .list-item li:before {
  content: url(../images/icon/19.svg);
  position: absolute;
  left: 5px;
  top:-1px;
}
.hero-banner-two .illustration-holder .shape-one {
  z-index: 1;
  top: 7%;
  left: -3%;
  width: 51.8%;
  animation: jumpTwo 8s infinite linear;
}
.hero-banner-two .illustration-holder .shape-two {
  z-index: 1;
  top: 40%;
  left: -11%;
  width: 54.6%;
  animation: jumpThree 8s infinite linear;
}
.hero-banner-two .illustration-holder .shape-three {
  z-index: 1;
  top: 45%;
  right: -5%;
  width: 53.2%;
  animation: jumpTwo 8s infinite linear;
}
.hero-banner-two .trusted-companies p {
  font-size: 20px;
  color: #000;
  padding-bottom: 50px;
}
.hero-banner-two .trusted-companies p span {
  font-size: 1.6em;
  font-weight: 500;
  color: #FF6559;
  text-decoration: underline;
}
/*.hero-banner-two .trusted-companies .slider-arrow li {
  width: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  margin: 0 10px;
  font-size: 24px;
  opacity: 0.5;
  transition: all 0.3s ease-in-out;
  transform: scale(0.8);
}
.hero-banner-two .trusted-companies .slider-arrow li:first-child i {transform: rotate(180deg); display: inline-block;}
.hero-banner-two .trusted-companies .slider-arrow li:hover {
  opacity: 1;
  transform: scale(1);
}*/
/*---------------------- Fancy Feature Two -------------------*/
.fancy-feature-four {padding: 0 15px;}
.fancy-feature-four .inner-content {position: relative;}
.fancy-feature-four .bg-wrapper {
  max-width: 1460px;
  margin: 0 auto;
  position: relative;
  background: #FFF6EC;
  z-index: 1;
  padding: 100px 0 190px;
}
.fancy-feature-four .bg-wrapper:before {
  content: '';
  width: 100%;
  height: 28px;
  background: url(../images/shape/17.svg) no-repeat center;
  background-size: cover;
  position: absolute;
  left: 0;
  bottom: -25px;
}
.block-style-five {
  background: #fff;
  transition: all 0.3s ease-in-out;
  padding: 46px 30px 50px 48px;
  margin-top: 30px;
  border-radius: 5px;
  cursor: pointer;
}
.block-style-five:hover {
  box-shadow: 0px 30px 50px rgba(0,0,0,0.04);
  transform: translateY(-5px);
}
.block-style-five .icon {height: 72px;}
.block-style-five .icon img {max-height: 100%;}
.block-style-five .title {
  font-family: 'Inter';
  font-size: 20px;
  margin: 24px 0 29px;
  display: inline-block;
  position: relative;
}
.block-style-five .title:before {
  content: '';
  width: 100%;
  height: 3px;
  background: var(--yellow-deep);
  border-radius: 2px;
  position: absolute;
  left: 0;
  bottom: -2px;
}
.block-style-five p {
  font-size: 24px;
  line-height: 1.45em;
  color: #000;
}
.fancy-feature-four .shape-right {right: -16px;top:30%;}
.fancy-feature-four .shape-left {left: -8px;top:62%;}
.fancy-feature-four .shape-one {top:-20px;left: -64px; animation: jumpTwo 5s infinite linear;}
.fancy-feature-four .shape-two {bottom:-85px;right: -104px; animation: rotated 50s infinite linear;}
/*---------------------- Fancy Text block Six ---------------*/
.fancy-text-block-six {position: relative;}
.fancy-text-block-six .illustration-holder .shape-one {
  right: -20%;
  top: 29%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-six .img-meta-container {
  position: absolute;
  right: 0;
  top:23px;
  max-width: 42%;
}
.fancy-text-block-six .img-meta-container .shape-one {
  top:-7%;
  left: -4%;
  animation: rotated 18s infinite linear;
}
.fancy-text-block-six .img-meta-container .shape-two {
  top:-11%;
  left: 26%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-six .img-meta-container .shape-three {
  top:-6%;
  right: 31%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-six .img-meta-container .shape-four {
  top:44%;
  left: -7%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-six .img-meta-container .shape-five {
  bottom:-15%;
  left: -10%;
  animation: rotated 50s infinite linear;
}
.fancy-text-block-six .img-meta-container .shape-six {
  bottom:-11%;
  left: 28%;
  animation: rotated 20s infinite linear;
}
.fancy-text-block-six .img-meta-container .shape-seven {
  bottom:-12%;
  left: 57%;
  animation: jumpTwo 5s infinite linear;
}
.accordion-style-two .card {
  background: #fff;
  margin-bottom: 0;
  border-radius: 0;
  border: none;
  border-bottom: 2px solid #000;
}
.accordion-style-two .card .card-header {
  background: transparent;
  border-radius: 0;
  padding: 0;
  border:none;
}
.accordion-style-two .card .card-header button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 22px 70px 22px 0;
  font-family: 'Inter', serif;
  font-size: 30px;
  line-height: 1.40em;
  border:none;
  border-radius: 0;
  margin: 0;
  color: var(--heading);
  text-decoration: none;
  position: relative;
}
.accordion-style-two .card .card-header button:before {  
  content: "";
  font-family: 'font-awesome';
  position: absolute;
  font-size: 28px;
  right: 0;
  top:30%;
  transition: all 0.3s ease-in-out;
}
/*.accordion-style-two .card .card-header button[data-toggle="collapse"].collapsed:before {content: "";}*/
.accordion-style-two .card .card-body {padding: 0 70px 30px 0;}
.accordion-style-two .card .card-body p {font-size: 20px; line-height: 1.55em;}
.accordion-style-two .card:last-child {border:none;}
/*------------------ Counter With Icon One --------------*/
.counter-with-icon-one .border-style {
  border-top:1px solid #efefef;
  border-bottom:1px solid #efefef;
  padding: 58px 0 90px;
}
.counter-box-three {text-align: center; margin-top: 40px;}
.counter-box-three .icon {height: 62px; display: inline-block;}
.counter-box-three .number {font-size: 38px;padding: 5px 0 3px;}
.counter-box-three p {
  font-weight: 300;
  font-size: 24px;
  color: #000;
}
/*------------------- Fancy Text block Seven ----------------*/
.fancy-text-block-seven .bg-wrapper {
  max-width: 1460px;
  margin: 0 auto;
  position: relative;
  background: url(../images/shape/bg.svg) no-repeat top center;
  background-size: cover;
  z-index: 1;
  padding: 42px 0 70px;
}
.fancy-text-block-seven .bg-wrapper.no-bg {background: none;}
.fancy-text-block-seven .img-holder {position: relative;}
.fancy-text-block-seven .quote-wrapper blockquote {
  font-size: 42px;
  line-height: 1.35em;
  color: #000;
  padding: 22px 0 30px;
}
.fancy-text-block-seven .quote-wrapper h6 {font-size: 20px; font-weight: 500;}
.fancy-text-block-seven .quote-wrapper h6 span {
  display: block;
  font-size: 18px;
  color: #9EADBA;
  font-weight: normal;
  padding-top: 8px;
}
.fancy-text-block-seven .shape-one {
  top:13%;
  left: 5%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-seven .shape-two {
  top:21%;
  right:8%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-seven .shape-three {
  bottom:12%;
  right:8%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-seven .shape-four {
  bottom:47%;
  left:45%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-seven .shape-five {
  bottom:12%;
  left:8%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-seven .shape-six {
  top:14%;
  left:-6%;
  z-index: 1;
  animation: jumpThree 5s infinite linear;
}
/*---------------- Fancy Text block Eight ---------------*/
.block-style-six {position: relative;}
.block-style-six:nth-child(even):before {
  content: '';
  position: absolute;
  width: 100%;
  height: 13px;
  left: 0;
  bottom: 0;
  background: url(../images/shape/line-shape-4.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.block-style-six .text-details .title {
  font-size: 42px;
  line-height: 1.26em;
  padding: 24px 0 34px;
}
.block-style-six .text-details .text-meta {
  font-size: 22px;
  line-height: 1.54em;
  padding-bottom: 65px;
}
.block-style-six .text-details .quote-wrapper {
  background: #F4F9FC;
  border-radius: 5px;
  padding: 35px 50px 25px 35px;
  position: relative;
}
.block-style-six .text-details .quote-wrapper .quote-icon {
  width: 57px;
  height: 57px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  left: 27px;
  top:-29px;
}
.block-style-six .text-details .quote-wrapper blockquote {
  font-size: 18px;
  line-height: 1.55em;
  color: #0D0D0D;
}
.block-style-six .text-details .name {
  font-size: 16px;
  color: #A8A8A8;
  margin-top: 24px;
  line-height: 1.44em;
}
.block-style-six .text-details .name strong {
  font-weight: 500;
  color: #191717;
}
.block-style-six .illustration-one .shape-one {
  width: 6px;
  height: 6px;
  background: #F2DAD5;
  top:-7%;
  right: 41%;
  animation: jumpThree 5s infinite linear;
}
.block-style-six .illustration-one .shape-two {
  width: 14px;
  height: 14px;
  background: #F77A56;
  top:14%;
  right: -10%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-six .illustration-one .shape-three {
  width: 7px;
  height: 7px;
  background: #F2DAD5;
  bottom:29%;
  right: -5%;
  animation: jumpThree 5s infinite linear;
}
.block-style-six .illustration-one .shape-four {
  width: 10px;
  height: 10px;
  background: #F77A56;
  bottom:-2%;
  right: 51%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-six .illustration-one .shape-five {
  width: 7px;
  height: 7px;
  background: #F2DAD5;
  top:45%;
  left: 5%;
  animation: jumpThree 5s infinite linear;
}
.block-style-six .illustration-one .shape-six {
  top:17%;
  left: 6%;
  animation: jumpTwo 8s infinite linear;
}
.block-style-six .illustration-one .shape-seven {
  top:42%;
  right: -3%;
  animation: rotatedTwo 35s infinite linear;
}
.block-style-six .illustration-two .shape-one {
  width: 6px;
  height: 6px;
  background: #F2DAD5;
  top:-5%;
  left: 22%;
  animation: jumpThree 5s infinite linear;
}
.block-style-six .illustration-two .shape-two {
  width: 8px;
  height: 8px;
  background: #F2DAD5;
  top:63%;
  right: 12%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-six .illustration-two .shape-three {
  width: 6px;
  height: 6px;
  background: #F77A56;
  bottom:-11%;
  right: 44%;
  animation: jumpThree 5s infinite linear;
}
.block-style-six .illustration-two .shape-four {
  width: 10px;
  height: 10px;
  background: #F77A56;
  bottom:39%;
  left: -10%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-six .illustration-two .shape-five {
  top:9%;
  left: -3%;
  animation: rotatedTwo 35s infinite linear;
}
.block-style-six .illustration-two .shape-six {
  bottom:3%;
  right:20%;
  animation: jumpTwo 8s infinite linear;
}
/*-------------------- Useable Tools Two --------------------*/
.useable-tools-section-two.bg-shape {padding: 0 15px;}
.useable-tools-section-two.bg-shape .bg-wrapper {
  max-width: 1460px;
  margin: 0 auto;
  background: #FDF3E7;
  position: relative;
  padding: 108px 0 0;
  z-index: 1;
}
.useable-tools-section-two.bg-shape .bg-wrapper:before {
  content: '';
  position: absolute;
  width:100%;
  height: 15px;
  background-image: url(../images/shape/37.svg);
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
  top:-13px;
  left: 0;
}
.useable-tools-section-two.bg-shape .bg-wrapper:after {
  content: '';
  position: absolute;
  width:100%;
  height: 24px;
  background-image: url(../images/shape/38.svg);
  background-position: bottom center;
  background-repeat: no-repeat;
  background-size: cover;
  bottom:-23px;
  left: 0;
}
.useable-tools-section-two.bg-shape .bg-wrapper .shapes {z-index: 1;}
.useable-tools-section-two.bg-shape .bg-wrapper .shape-one {
  width: 6px;
  height: 6px;
  background: #F2DAD5;
  top:6%;
  left: 11%;
  animation: jumpThree 5s infinite linear;
}
.useable-tools-section-two.bg-shape .bg-wrapper .shape-two {
  width: 10px;
  height: 10px;
  background: #F77A56;
  top:10%;
  right: 11%;
  animation: jumpTwo 5s infinite linear;
}
.useable-tools-section-two.bg-shape .bg-wrapper .shape-three {
  width: 6px;
  height: 6px;
  background: #F2DAD5;
  top:45%;
  right: 24%;
  animation: jumpThree 5s infinite linear;
}
.useable-tools-section-two.bg-shape .bg-wrapper .shape-four {
  width: 8px;
  height: 8px;
  background: #F77A56;
  top:43%;
  left: 16%;
  animation: jumpTwo 5s infinite linear;
}
.useable-tools-section-two .icon-wrapper ul {
  margin: 0 -15px;
  position: relative;
  z-index: 1;
  transform: translateY(58px);
}
.useable-tools-section-two .icon-wrapper ul li {
  padding: 0 15px;
  float: left;
  width:11.11111%;
}
.useable-tools-section-two .icon-wrapper ul li:nth-child(odd) {margin-top: 50px;}
.useable-tools-section-two .icon-wrapper ul li:nth-child(4),
.useable-tools-section-two .icon-wrapper ul li:nth-child(6) {margin-top:110px;}
.useable-tools-section-two .icon-wrapper ul li .icon-box {
  background: #fff;
  border-radius: 10px;
  height: 100px;
  margin: 20px 0;
  cursor: pointer;
}
.useable-tools-section-two .theme-btn-two {border-radius: 5px;}
/*--------------- Client Feedback Slider One -----------------*/
.client-feedback-slider-one {
  max-width: 1920px;
  margin: 0 auto;
  position: relative;
}
.client-feedback-slider-one .shapes-holder{
  position: absolute;
  top:60px;
  left: -35px;
  width: 40%;
}
.client-feedback-slider-one .shapes-holder .title-style-two {
  position: absolute;
  right: 0;
  top:43%;
  transform: translateY(-50%);
  z-index: 5;
}
.client-feedback-slider-one .shapes-holder .shape-one {
  z-index: 1;
  top:-11%;
  right: 21%;
  animation: jumpTwo 8s infinite linear;
}
.client-feedback-slider-one .shapes-holder [class*="cp-img"] {
  position: absolute;
  z-index: 1;
  border-radius: 50%;
}
.client-feedback-slider-one .shapes-holder .cp-img-one {
  width: 60px;
  height: 60px;
  top:13%;
  left: 26%;
}
.client-feedback-slider-one .shapes-holder .cp-img-two {
  width: 75px;
  height: 75px;
  top:46%;
  left: 13%;
}
.client-feedback-slider-one .shapes-holder .cp-img-three {
  width: 50px;
  height: 50px;
  bottom:7%;
  left: 25%;
}
.client-feedback-slider-one .shapes-holder .cp-img-four {
  width: 80px;
  height: 80px;
  bottom:22%;
  right: 21%;
}
.client-feedback-slider-one .feedback-meta {
  position: relative;
  padding-top: 138px;
}
.client-feedback-slider-one .feedback-meta .watermark {
  font-size: 160px;
  color: #F9FAFC;
  position: absolute;
  top:0;
  left: 0;
}
.client-feedback-slider-one .clientSliderOne p {
  font-size: 24px;
  line-height: 1.87em;
  padding: 52px 0 60px;
}
.client-feedback-slider-one .clientSliderOne .c_img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.client-feedback-slider-one .clientSliderOne .info {padding-left: 20px;}
.client-feedback-slider-one .clientSliderOne .info strong {
  font-weight: 500;
  font-size: 24px;
  color: #252525;
  display: block;
}
.client-feedback-slider-one .clientSliderOne .info span {color: #C3C3C3}
.client-feedback-slider-one .slider-arrow li {
  width: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  margin-right: 15px;
  font-size: 24px;
  opacity: 0.5;
  transition: all 0.3s ease-in-out;
  transform: scale(0.8);
}
.client-feedback-slider-one .slider-arrow li:first-child i {transform: rotate(180deg); display: inline-block;}
.client-feedback-slider-one .slider-arrow li:hover {
  opacity: 1;
  transform: scale(1);
}
/*----------------- Fancy Short Banner Three -------------------*/
.fancy-short-banner-three .bg-wrapper {
  background: url(../images/shape/bg2.svg) no-repeat;
  background-size: cover;
  border-radius: 40px;
  padding: 82px 50px 89px;
  position: relative;
}
.fancy-short-banner-three .bg-wrapper:before {
  content: url(../images/shape/43.svg);
  position: absolute;
  top:-124px;
  right: -104px;
  z-index: -1;
}
.fancy-short-banner-three .title-style-one h2 {font-size: 48px;}
.fancy-short-banner-three .form-wrapper {padding-left: 65px;}
.fancy-short-banner-three .form-wrapper form {
  position: relative;
  height: 70px;
  background: #fff;
  border-radius: 7px;
}
.fancy-short-banner-three .form-wrapper form input {
  font-style: italic;
  font-size: 18px;
  width: 100%;
  border:none;
  height: 100%;
  padding: 0 150px 0 25px;
  background: transparent;
}
.fancy-short-banner-three .form-wrapper form button {
  position: absolute;
  right: 8px;
  bottom: 8px;
  top:8px;
  width: 150px;
  border-radius: 6px;
  color: #fff;
  font-weight: 500;
  font-size: 16px;
  background: var(--red-light);
  transition: all 0.3s ease-in-out;
}
.fancy-short-banner-three .form-wrapper form button:hover {background: #FFBA12; box-shadow: 0px 15px 30px rgba(255,186,18,0.2);}
.fancy-short-banner-three .form-wrapper p {
  font-size: 16px;
  padding-top: 10px;
}
.fancy-short-banner-three .form-wrapper p a {color: #000;}
.fancy-short-banner-three .form-wrapper p a:hover {text-decoration: underline;}
/*----------------- Footer Style Two --------------*/
.theme-footer-two {position: relative; overflow: hidden;}
.theme-footer-two:before {
  content: url(../images/shape/44.svg);
  position: absolute;
  bottom: -10px;
  left: 11%;
  z-index: -1;
}
.theme-footer-two:after {
  content: " ";
  position: absolute;
  width: 8px;
  height: 8px;
  background: #F77A56;
  bottom: 19%;
  right: 30%;
  z-index: -1;
}
.theme-footer-two .top-footer [class*="col-"] {margin-bottom: 30px;}
.theme-footer-two .top-footer .footer-title {
  font-family: 'Inter', serif;
  font-size: 24px;
  padding-bottom: 25px;
}
.theme-footer-two .top-footer .footer-list ul li a {
  font-size: 16px;
  line-height: 38px;
  color: #727272;
  transition: all 0.3s ease-in-out;
}
.theme-footer-two .top-footer .footer-list ul li a:hover {color: var(--red-light);}
.theme-footer-two .top-footer .address-list ul.info li a {
  font-size: 16px;
  color: #727272;
  margin-bottom: 10px;
}
.theme-footer-two .top-footer .address-list ul.info li a:hover {text-decoration: underline;}
.theme-footer-two .top-footer .address-list ul.info li a.mobile-num {
  font-size: 20px;
  color: #030303
}
.theme-footer-two .top-footer .address-list .social-icon a {
  font-size: 22px;
  margin-right: 20px;
  color: rgba(0,0,0,0.35);
}
.theme-footer-two .top-footer .address-list .social-icon a:hover {color: #000;}
.theme-footer-two .bottom-footer-content ul li {display: inline-block;}
.theme-footer-two .bottom-footer-content ul li a {
  font-size: 16px;
  color: #727272;
  margin-left: 20px;
}
.theme-footer-two .bottom-footer-content ul li a:hover {color: #000;}
.theme-footer-two .bottom-footer-content p {font-size: 15px; color: #6A6A6A;}
.theme-footer-two .bottom-footer-content {padding: 26px 0 30px;}
.theme-footer-two .bottom-footer-content ul {padding-right: 97px;}
/*--------------------- Fancy Hero Two --------------------*/
.fancy-hero-two {padding: 0 70px;}
.fancy-hero-two .bg-wrapper {
  background: url(../images/shape/bg3.svg) no-repeat center;
  background-size: cover;
  border-radius: 20px;
  padding: 108px 0 110px;
  text-align: center;
}
.fancy-hero-two .page-title {font-size: 24px;color: #B1B1B1;}
.fancy-hero-two .heading {
  font-family: 'gilroy-black';
  font-size: 80px;
  line-height: 1.10em;
  padding: 0 0 38px;
}
.fancy-hero-two .sub-heading {
  font-size: 24px;
  line-height: 1.41em;
  color: #2A2A2A;
}
/*------------------ Fancy Text block Nine ----------------*/
.fancy-text-block-nine {position: relative;}
.fancy-text-block-nine .text-meta {
  font-size: 24px;
  line-height: 1.66em;
  padding-top: 40px;
  text-align: center;
  color: #515151;
}
.fancy-text-block-nine .shape-one {
  width: 10px;
  height: 10px;
  background: #FFDBD0;
  top:5%;
  right: 13%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-nine .shape-two {
  width: 6px;
  height: 6px;
  background: #F77A56;
  bottom:9%;
  right: 13%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-nine .shape-three {
  width: 8px;
  height: 8px;
  background: #FFDBD0;
  bottom:9%;
  left: 13%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-nine .shape-four {
  width: 11px;
  height: 11px;
  background: #FF7052;
  top:12%;
  left: 13%;
  animation: jumpThree 5s infinite linear;
}
/*------------------ Fancy Text block Ten ----------------*/
.fancy-text-block-ten .container {
  max-width: 1300px;
  padding: 0 15px;
}
.fancy-text-block-ten p {
  font-size: 42px;
  line-height: 1.35em;
  color: #000000;
  padding: 25px 0 50px;
}
.fancy-text-block-ten .name {
  font-size: 20px;
  font-weight: 500;
  color: #000;
  position: relative;
  padding-left: 28px;
}
.fancy-text-block-ten .name span {
  font-size: 18px;
  color: #AEB3B7;
  font-weight: normal;
}
.fancy-text-block-ten .name:before {
  content: '';
  width: 15px;
  height: 3px;
  border-radius: 2px;
  background: #000;
  position: absolute;
  left: 0;
  top:14px;
}
.fancy-text-block-ten .img-meta {border-radius: 5px; margin: 0 auto;}
.fancy-text-block-ten .img-gallery {position: relative;}
.fancy-text-block-ten .img-gallery:before {
  content: url(../images/shape/45.svg);
  position: absolute;
  top:-14%;
  right: -5%;
  z-index: 1;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-ten .img-gallery:after {
  content: url(../images/shape/46.svg);
  position: absolute;
  bottom:-12%;
  left: -12%;
  z-index: -1;
  animation: rotatedTwo 50s infinite linear;
}
/*-------------------- Fancy Feature Five -------------------*/
.fancy-feature-five {padding: 0 70px;}
.fancy-feature-five .bg-wrapper {
  background: #F2F7FF;
  border-radius: 10px;
  padding: 120px 0 140px;
  position: relative;
  z-index: 5;
}
.block-style-seven {
  padding: 40px 22px 0;
  text-align: center;
}
.block-style-seven .icon {height: 80px;}
.block-style-seven .icon img {margin: 0 auto; max-height: 100%;}
.block-style-seven .feature-info {
  color: rgba(0,0,0,0.3);
  padding: 30px 0 7px;
}
.block-style-seven p {
  font-size: 28px;
  line-height: 1.35em;
  color: #000;
}
.fancy-feature-five .shape-one {
  width: 6px;
  height: 6px;
  background: #F2DAD5;
  top:14%;
  right: 15%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-five .shape-two {
  width: 12px;
  height: 12px;
  background: #F77A56;
  top:58%;
  right: 6%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-five .shape-three {
  width: 7px;
  height: 7px;
  background: #F2DAD5;
  top:84%;
  left: 7%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-five .shape-four {
  width: 8px;
  height: 8px;
  background: #F77A56;
  top:27%;
  left: 8%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-five .shape-five {
  bottom: 0;
  right: 0;
}
/*------------------ Team Section Two --------------*/
.team-section-two {position: relative;}
.team-section-two .shape-one {
  width: 11px;
  height: 11px;
  background: #F77A56;
  right: 11%;
  top:42%;
  animation: jumpThree 5s infinite linear;
}
.team-section-two .shape-two {
  width: 6px;
  height: 6px;
  background: #F2DAD5;
  right: 11%;
  top:87%;
  animation: jumpThree 5s infinite linear;
}
.team-section-two .shape-three {
  width: 10px;
  height: 10px;
  background: #F77A56;
  left: 13%;
  top:75%;
  animation: jumpTwo 5s infinite linear;
}
.team-section-two .shape-four {
  width: 8px;
  height: 8px;
  background: #F2DAD5;
  left: 12%;
  top:24%;
  animation: jumpThree 5s infinite linear;
}
.team-section-two .team-member {
  text-align: center; 
  margin-top: 50px;
  cursor: pointer;
}
.team-section-two .team-member img {
  width: 100%; 
  border-radius: 10px;
  transition: all 0.3s ease-in-out;
}
.team-section-two .team-member:hover img {transform: translateY(8px);}
.team-section-two .team-member .name {
  font-size: 24px;
  font-weight: 500;
  color: #2A2A2A;
  padding: 35px 0 0;
}
.team-section-two .team-member .position {font-size: 16px; color: rgba(42,42,42,0.4);}
/*---------------- Fancy Hero Three --------------*/
.fancy-hero-three {
  background: #FFF7EF;
  position: relative;
  padding: 225px 0 180px;
  z-index: 5;
  text-align: center;
}
.fancy-hero-three:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 24px;
  background: url(../images/shape/48.svg) no-repeat center;
  background-size: cover;
  left: 0;
  bottom: -20px;
}
.fancy-hero-three.bg-transparent:before {display: none;}
.fancy-hero-three .shape-one {
  width: 6px;
  height: 6px;
  background: #F77A56;
  top:29%;
  right: 27%;
  animation: jumpThree 5s infinite linear;
}
.fancy-hero-three .shape-two {
  width: 12px;
  height: 12px;
  background: #FFDBD0;
  top:59%;
  right: 12%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-hero-three .shape-three {
  width: 8px;
  height: 8px;
  background: #F77A56;
  top:84%;
  right: 25%;
  animation: jumpThree 5s infinite linear;
}
.fancy-hero-three .shape-four {
  width: 10px;
  height: 10px;
  background: #FFDBD0;
  top:79%;
  left: 21%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-hero-three .shape-five {
  width: 6px;
  height: 6px;
  background: #F77A56;
  top:50%;
  left: 12%;
  animation: jumpThree 5s infinite linear;
}
.fancy-hero-three .shape-six {
  width: 11px;
  height: 11px;
  background: #FF7052;
  top:27%;
  left: 21%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-hero-three .heading {
  font-family: 'gilroy-black';
  font-size: 80px;
  line-height: 1.10em;
  padding: 0 0 38px;
}
.fancy-hero-three .sub-heading {
  font-size: 24px;
  line-height: 1.41em;
  color: #2A2A2A;
}
.fancy-hero-three .trial-button {border-radius: 6px;}
/*---------------- Fancy Text block Eleven -----------------*/
.fancy-text-block-eleven .text-wrapper {padding: 50px 0 0 65px;}
.fancy-text-block-eleven .text-wrapper p {font-size: 22px; line-height: 1.81em;}
.fancy-text-block-eleven .img-gallery {
  position: relative;
  display: inline-block;
}
.fancy-text-block-eleven .img-gallery .overlay-img {
  position: absolute;
  right: -130px;
  top:-70px;
  z-index: 1;
}
.fancy-text-block-eleven .img-gallery .shape-one {
  top:-13%;
  left: -16%;
}
.fancy-text-block-eleven .img-gallery .shape-two {
  bottom:-19%;
  right: -21%;
  animation: rotatedTwo 50s infinite linear;
}
.fancy-text-block-eleven .img-gallery .shape-three {
  bottom:-9%;
  left: -16%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-eleven .video-box {
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.fancy-text-block-eleven .video-box .video-button {
  width: 85px;
  height: 85px;
  border-radius: 50%;
  background: #FD6A5E;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50% , -50%);
  z-index: 1;
  transition: all 0.3s ease-in-out;
}
.fancy-text-block-eleven .video-box .video-button:hover {background: #212121;}
/*--------------- Fancy Feature Six -------------*/
.fancy-feature-six {padding: 0 15px;}
.fancy-feature-six .bg-wrapper {
  max-width: 1440px;
  margin: 0 auto;
  padding: 110px 0 210px;
  background: url(../images/shape/bg4.svg) no-repeat top center;
  background-size: cover;
  position: relative;
  z-index: 5;
}
.fancy-feature-six .bg-wrapper .shape-one {
  width: 16px;
  height: 16px;
  background: #F96F60;
  z-index: 1;
  top:5px;
  left: 14%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-six .bg-wrapper .shape-two {
  width: 12px;
  height: 12px;
  background: #F96F60;
  top:36%;
  right: 4%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-six .bg-wrapper .shape-three {
  width: 7px;
  height: 7px;
  background: #F96F60;
  top:68%;
  right: 4%;
  opacity: .25;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-six .bg-wrapper .shape-four {
  width: 10px;
  height: 10px;
  background: #F96F60;
  top:91%;
  left: 7%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-six .bg-wrapper .shape-five {
  width: 7px;
  height: 7px;
  background: #F96F60;
  top:31%;
  left: 4%;
  opacity: .25;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-six .bg-wrapper .shape-six {
  top:78%;
  right: 2%;
  animation: rotatedTwo 50s infinite linear;
}
/*----------------- Fancy Text block Twelve -------------------*/
.fancy-text-block-twelve .text-wrapper .sub-text {
  font-size: 22px;
  line-height: 1.81em;
  padding-top: 40px;
}
.fancy-text-block-twelve .text-wrapper .list-item-one li {
  font-size: 18px;
  margin-top: 12px;
  padding-left: 30px;
  position: relative;
}
.fancy-text-block-twelve .text-wrapper .list-item-one li:before {
  content: url(../images/icon/177.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.fancy-text-block-twelve .img-gallery {
  display: inline-block;
  padding: 30px 45px 0 25px;
  position: relative;
}
.fancy-text-block-twelve .img-gallery .shape-one {top: -7%;left: -10%;}
.fancy-text-block-twelve .img-gallery .shape-two {
  bottom: -13%;
  right: -4%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-eight {
  background: #fff;
  transition: all 0.3s ease-in-out;
  padding: 20px 15px 25px 32px;
  margin-top: 30px;
  border:2px solid #DCE4E8;
  border-radius: 5px;
}
.block-style-eight:hover {border-color: #000;}
.block-style-eight .title {
  font-family: 'Inter';
  font-size: 20px;
  margin: 0 0 24px;
  display: inline-block;
  position: relative;
}
.block-style-eight .title:before {
  content: '';
  width: 100%;
  height: 3px;
  background: var(--yellow-deep);
  border-radius: 2px;
  position: absolute;
  left: 0;
  bottom: -2px;
}
.block-style-eight p {
  line-height: 1.72em;
  color: #000;
}
/*----------------- Fancy Feature Seven --------------*/
.fancy-feature-seven {
  background: #ECF6FF;
  padding: 170px 0 150px;
  position: relative;
  z-index: 1;
}
.fancy-feature-seven:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 45px;
  background: url(../images/shape/58.svg) no-repeat top center;
  background-size: cover;
  left: 0;
  top:-42px;
}
.fancy-feature-seven:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 23px;
  background: url(../images/shape/59.svg) no-repeat top center;
  background-size: cover;
  left: 0;
  bottom:-20px;
}
.block-style-nine {position: relative;}
.block-style-nine .text-wrapper h6 {
  font-family: 'Roboto', sans-serif;
  font-size: 20px;
  color: rgba(0,0,0,0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
}
.block-style-nine .text-wrapper .title {
  font-size: 42px;
  line-height: 1.28em;
  padding: 5px 0 30px;
}
.block-style-nine .text-wrapper p {
  font-size: 20px;
  line-height: 1.8em;
  color: rgba(14,14,14,0.8);
}
.block-style-nine:nth-child(1) {padding-bottom: 30px;}
.block-style-nine:nth-child(1):before {
  content: url(../images/shape/55.svg);
  position: absolute;
  left: 33%;
  bottom: -14%;
}
.block-style-nine:nth-child(2) {padding-bottom: 30px;}
.block-style-nine:nth-child(2):before {
  content: url(../images/shape/56.svg);
  position: absolute;
  right: 33%;
  bottom: -26%;
}
.block-style-nine:nth-child(3) {padding-bottom: 60px;}
.block-style-nine:nth-child(3):before {
  content: url(../images/shape/57.svg);
  position: absolute;
  left: 36%;
  bottom: -16%;
}
/*------------------ Fancy Text Block Thirteen ---------------*/
.fancy-text-block-thirteen .text-wrapper p {
  font-size: 24px;
  line-height: 1.70em;
  padding-top: 45px;
}
/*------------------ Fancy Text Block Fourteen ---------------*/
.fancy-text-block-fourteen .text-wrapper .sub-text {
  font-size: 24px;
  line-height: 1.58em;
  color: #0E0E0E;
  padding-top: 40px;
}
.fancy-text-block-fourteen .text-wrapper .name {
  font-family: 'Inter';
  font-size: 24px;
  color: #000;
  position: relative;
  padding-left: 28px;
  margin-top: 40px;
}
.fancy-text-block-fourteen .text-wrapper .name span {
  font-family: 'gilroy-semibold';
  color: #AEB3B7;
}
.fancy-text-block-fourteen .text-wrapper .name:before {
  content: '';
  width: 18px;
  height: 3px;
  border-radius: 2px;
  background: #000;
  position: absolute;
  left: 0;
  top:13px;
}
.fancy-text-block-fourteen .img-holder {position: relative; padding: 0 50px 0 60px;}
.fancy-text-block-fourteen .img-holder .shape-one {
  right: 0;
  top:-10%;
}
.fancy-text-block-fourteen .img-holder .shape-two {
  left: -1%;
  top:-12%;
  z-index: 1;
  animation: jumpTwo 5s infinite linear;
}
/*------------------- Pricing Section One ------------------*/
.pricing-nav-one {margin: 40px 0 20px; border:none;}
.pricing-nav-one .nav-item {margin: 0;}
.pricing-nav-one .nav-item .nav-link {
  font-family: 'Inter', serif;
  line-height: 50px;
  border: 2px solid var(--blue-dark);
  padding: 0 15px;
  width: 172px;
  text-align: center;
  color: var(--blue-dark);
  transition: all 0.3s ease-in-out;
}
.pricing-nav-one .nav-item:nth-child(1) .nav-link {
  border-right: none;
  border-radius: 3px 0 0 3px;
}
.pricing-nav-one .nav-item:nth-child(2) .nav-link {
  border-left: none;
  border-radius: 0 3px 3px 0;
}
.pricing-nav-one .nav-item .nav-link.active {
  background: var(--blue-dark);
  color: #fff;
}
.pricing-section-one .offer-text {
  text-align: center;
  color: var(--blue-dark);
}
.pricing-section-one .pricing-table-area {
  position: relative;
  margin-top: -40px;
  padding-bottom: 130px;
}
.pricing-section-one .pricing-table-area:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 78%;
  bottom: 0;
  left: 0;
  z-index: -1;
  background: linear-gradient(45deg, #FFFBF2, #EDFFFD);
}
.pricing-section-one .pricing-table-area .shape-one {
  left: 0;
  top:7%;
  animation: jumpTwo 5s infinite linear;
}
.pricing-section-one .pricing-table-area .shape-two {
  right: 0;
  bottom:-140px;
  animation: jumpThree 5s infinite linear;
}
.pricing-section-one .pr-table-wrapper {
  background: #fff;
  box-shadow: 0 -10px 40px rgba(60,67,113,0.05);
  padding: 35px 0 25px;
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.pricing-section-one .pr-table-wrapper:hover {transform: translateY(-5px);}
.pricing-section-one .pr-table-wrapper .pack-name {
  font-family: 'gilroy-semibold';
  font-size: 18px;
  text-transform: uppercase;
  color: #202020;
  letter-spacing: 2.1px;
}
.pricing-section-one .pr-table-wrapper .price {
  font-size: 60px;
  color: #000;
  padding: 20px 0 27px;
}
.pricing-section-one .pr-table-wrapper .price sup {font-size: 25px; top:-21px;}
.pricing-section-one .pr-table-wrapper .icon {
  margin: 28px auto 20px;
  height: 102px;
}
.pricing-section-one .pr-table-wrapper .bill-cycle {font-size: 17px; color: #464646;}
.pricing-section-one .pr-table-wrapper .pr-feature {
  text-align: left;
  border-top: 1px solid #E8E8E8;
  border-bottom: 1px solid #E8E8E8;
  padding: 46px 0 36px 20px;
  margin: 19px 0 33px;
}
.pricing-section-one .pr-table-wrapper .pr-feature li {
  font-family: 'Inter', serif;
  font-size: 17px;
  line-height: 42px;
  color: #464646;
  padding-left: 30px;
  position: relative;
}
.pricing-section-one .pr-table-wrapper .pr-feature li:before {
  content: url(../images/icon/39.svg);
  position: absolute;
  left: 0;
  top:2px;
}
.pricing-section-one .pr-table-wrapper .trial-text {
  font-size: 16px;
  padding-top: 12px;
  color: rgba(0,0,0,0.38);
}
/*----------------- Fancy Text Block Fifteen --------------*/
.fancy-text-block-fifteen {position: relative; padding-bottom: 170px;}
.fancy-text-block-fifteen:before {
  content: '';
  width: 100%;
  height: 82%;
  background: #FAFAFF;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.fancy-text-block-fifteen .shape-one {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #FF6CC4;
  right: 13%;
  top:28%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-fifteen .shape-two {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #F5A623;
  right: 13%;
  bottom:28%;
  animation: jumpThree 5s infinite linear;
}
.fancy-text-block-fifteen .shape-three {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #B183FF;
  left: 11%;
  bottom:16%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-fifteen .shape-four {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6AEE90;
  left: 11%;
  top:42%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-text-block-fifteen .bg-wrapper {
  background: #5E43F0;
  border-radius: 10px;
  padding: 85px 60px 0 15px;
}
.fancy-text-block-fifteen .bg-wrapper .main-img {margin: 0 auto;}
.fancy-text-block-fifteen .text-wrapper .more-text {
  font-family: 'gilroy-semibold';
  font-size: 20px;
  color: #fff;
  padding: 28px 0 20px;
}
.fancy-text-block-fifteen .text-wrapper .more-text span {text-decoration: underline;}
.fancy-text-block-fifteen .text-wrapper p {
  font-size: 40px;
  line-height: 1.5em;
  color: #fff;
}
.fancy-text-block-fifteen .text-wrapper h6 {
  font-size: 20px; 
  font-weight: 500; 
  color: #fff;
  padding-top: 37px;
}
.fancy-text-block-fifteen .text-wrapper h6 span {font-weight: normal; font-size: 18px;}
.fancy-text-block-fifteen .contact-banner p {
  font-size: 42px;
  line-height: 1.42em;
  color: #000;
}
.fancy-text-block-fifteen .contact-banner .theme-btn-four {
  line-height: 55px;
  width: 220px;
  font-size: 22px;
  text-align: center;
  display: block;
}
/*-------------------- Fancy Hero Four ---------------------*/
.fancy-hero-four {
  background: #ECF6FF;
  position: relative;
  padding: 185px 0 390px;
  z-index: 5;
  text-align: center;
}
.fancy-hero-four.bg-event {background: #FBF3EC;}
.fancy-hero-four.bg-doc {background: #F7FAFF;}
.fancy-hero-four.space-fix {padding: 220px 0 320px;}
.fancy-hero-four h6 {
  font-size: 18px;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  color: rgba(42,42,42,0.4);
  padding-bottom: 25px;
}
.fancy-hero-four h2 {
  font-family: 'gilroy-black'; 
  font-size: 80px;
  line-height: 1.1em;
}
.fancy-hero-four .sub-heading {
  font-size: 24px;
  line-height: 1.75em;
  padding: 55px 0 15px;
  color: #2A2A2A;
}
.fancy-hero-four .shape-one {
  width: 6px;
  height: 6px;
  background: #F77A56;
  top:29%;
  right: 27%;
  animation: jumpThree 5s infinite linear;
}
.fancy-hero-four .shape-two {
  width: 12px;
  height: 12px;
  background: #FFDBD0;
  top:59%;
  right: 12%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-hero-four .shape-three {
  width: 8px;
  height: 8px;
  background: #F77A56;
  top:84%;
  right: 25%;
  animation: jumpThree 5s infinite linear;
}
.fancy-hero-four .shape-four {
  width: 10px;
  height: 10px;
  background: #FFDBD0;
  top:79%;
  left: 21%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-hero-four .shape-five {
  width: 6px;
  height: 6px;
  background: #F77A56;
  top:50%;
  left: 12%;
  animation: jumpThree 5s infinite linear;
}
.fancy-hero-four .shape-six {
  width: 11px;
  height: 11px;
  background: #FF7052;
  top:27%;
  left: 21%;
  animation: jumpTwo 5s infinite linear;
}
/*--------------------- Pricing Section Two -----------------*/
.pricing-nav-two {margin: 40px 0 20px; border:none;}
.pricing-nav-two .nav-item {margin: 0;}
.pricing-nav-two .nav-item .nav-link {
  font-family: 'Inter', serif;
  line-height: 50px;
  border: 2px solid var(--red-light);
  padding: 0 15px;
  width: 172px;
  text-align: center;
  color: var(--red-light);
  transition: all 0.3s ease-in-out;
}
.pricing-nav-two .nav-item:nth-child(1) .nav-link {
  border-right: none;
  border-radius: 3px 0 0 3px;
}
.pricing-nav-two .nav-item:nth-child(2) .nav-link {
  border-left: none;
  border-radius: 0 3px 3px 0;
}
.pricing-nav-two .nav-item .nav-link.active {
  background: var(--red-light);
  color: #fff;
}
.pricing-section-two .pricing-table-area {
  position: relative;
  margin-top: -280px;
  z-index: 6;
  border: 1px solid #F1F1F1;
  border-right: none;
}
.pricing-section-two .pricing-table-area .tab-content {background: #fff;}
.pricing-section-two .pricing-table-area .pr-bg {border-right: 1px solid #F1F1F1;}
.pricing-section-two .pr-table-wrapper {
  text-align: center; 
  padding: 20px 0 40px;
  width: 100%;
}
.pricing-section-two .pr-table-wrapper .pack-name {
  font-family: 'gilroy-semibold';
  font-size: 18px;
  text-transform: uppercase;
  color: #202020;
  letter-spacing: 2.1px;
}
.pricing-section-two .pr-table-wrapper .price {
  font-family: 'gilroy-semibold';
  font-size: 72px;
  color: #000;
  padding: 44px 0 20px;
}
.pricing-section-two .pr-table-wrapper .price sup {font-size: 25px; top:-45px;}
.pricing-section-two .pr-table-wrapper .bill-cycle {
  font-size: 18px; 
  color: #000;
  margin-bottom: 22px;
}
.pricing-section-two .pr-table-wrapper .theme-btn-three {line-height: 42px; padding: 0 40px;}
.pricing-section-two .pr-table-wrapper .pr-feature {
  text-align: left;
  border-top: 1px solid #E8E8E8;
  padding: 34px 0 0 26px;
  margin: 35px 0 0;
}
.pricing-section-two .pr-table-wrapper .pr-feature li {
  font-size: 17px;
  line-height: 39px;
  color: rgba(0,0,0,0.7);
  padding-left: 26px;
  position: relative;
}
.pricing-section-two .pr-table-wrapper .pr-feature li:before {
  content: url(../images/icon/43.svg);
  position: absolute;
  left: 0;
  top:2px;
}
.pricing-section-two .pricing-table-area .shape-one {
  left: -7%;
  top:-7%;
  animation: jumpTwo 5s infinite linear;
}
.pricing-section-two .pricing-table-area .shape-two {
  right: -7%;
  bottom:-9%;
  animation: rotatedTwo 50s infinite linear;
}
/*------------------- FAQ Section -------------------*/
.accordion-style-three .card {
  background: transparent;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #000000;
}
.accordion-style-three .card .card-header {
  background: transparent;
  border-radius: 0;
  padding: 0;
  border:none;
}
.accordion-style-three .card .card-header button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 35px 35px 35px 0;
  font-family: 'Inter', serif;
  font-size: 24px;
  border:none;
  border-radius: 0;
  margin: 0;
  color: var(--heading);
  text-decoration: none;
  position: relative;
  border-bottom: 1px solid transparent;
}
.accordion-style-three .card:first-child .card-header button {border-top: 1px solid #000000;}
.accordion-style-three .card .card-header button:before {  
  content: "\f107";
  font-family: 'font-awesome';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.accordion-style-three .card .card-body {
  border-bottom: 1px solid #000000;
  padding: 0 50px 20px 0;
}
.accordion-style-three .card .card-body p {padding-bottom: 15px;}
.accordion-style-three .card .card-body p a {
  text-decoration: underline;
  color: var(--p-color);
}
.accordion-style-three .card:last-child .card-body {border:none; padding-bottom: 0;}
.more-faq-meta h3 {font-size: 36px;}
.more-faq-meta a {
  font-size: 16px;
  font-weight: 500;
  line-height: 45px;
  border:1px solid rgba(255,122,81,0.4);
  background: rgba(255,104,40,0.07);
  padding: 0 45px;
  border-radius: 22px;
  color: var(--red-light);
  transition: all 0.3s ease-in-out;
}
.more-faq-meta a:hover {
  background: var(--red-light);
  color: #fff;
}
/*-------------- Contact Style Two --------------*/
.contact-style-two .contact-info-wrapper {
  position: relative;
  z-index: 5;
  margin-top: -195px;
}
.contact-style-two .contact-info-wrapper .shape-one {
  left: -7%;
  top:-18%;
  animation: jumpTwo 5s infinite linear;
}
.contact-style-two .contact-info-wrapper .shape-two {
  right: -8%;
  bottom:-26%;
  animation: rotatedTwo 50s infinite linear;
}
.contact-style-two .contact-info-wrapper .address-info {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 15px 30px rgba(0,0,0,0.04);
  padding: 55px 15px 22px;
  text-align: center;
  margin-bottom: 35px;
  height: 100%;
  width: 100%;
}
.contact-style-two .contact-info-wrapper .address-info .icon {height: 82px;}
.contact-style-two .contact-info-wrapper .address-info .icon img {margin: 0 auto; max-height: 100%;}
.contact-style-two .contact-info-wrapper .address-info .title {
  font-size: 20px;
  color: #9E9E9E;
  padding: 17px 0 22px;
  position: relative;
  display: inline-block;
}
.contact-style-two .contact-info-wrapper .address-info .title:before {
  content: '';
  width: 100%;
  height: 3px;
  background: #FFB840;
  border-radius: 2px;
  position: absolute;
  left: 0;
  bottom: 22px;
}
.contact-style-two .contact-info-wrapper .address-info p {
  font-size: 28px;
  line-height: 1.35em;
  color: #000;
}
.contact-style-two .contact-info-wrapper .address-info ul li a {
  font-size: 22px;
  margin: 5px 15px;
  color: rgba(0,0,0,0.2);
  transition: all 0.2s ease-in-out;
}
.contact-style-two .contact-info-wrapper .address-info ul li a:hover {color: var(--red-light);}

.form-style-classic .input-group-meta {
  height: 58px;
  position: relative;
}
.form-style-classic .input-group-meta.lg {height: 200px;}
.form-style-classic .input-group-meta input {
  width: 100%;
  height: 100%;
  font-size: 23px;
  border:none;
  border-bottom: solid 2px #000;
  color: var(--heading);
  background: transparent;
  font-family: 'Inter', serif;
}
.form-style-classic .input-group-meta textarea {
  width: 100%;
  height: 100%;
  font-size: 23px;
  color: var(--heading);
  border:none;
  border-bottom: solid 2px #000;
  resize: none;
  padding: 20px 0;
  font-family: 'Inter', serif;
}
.form-style-classic .input-group-meta label {
  font-size: 14px;
  font-weight: normal;
  color: #BFBFBF;
  position: absolute;
  left: 0;
  top:-20px;
  z-index: 1;
}
.form-style-classic [class*="theme-btn"] {border-radius: 5px;}
/*---------------------- Faqs -------------------*/
.faqs-inner-page {
  background: linear-gradient(45deg, #FFFBF2, #EDFFFD);
  padding: 170px 0 185px;
  position: relative;
  z-index: 5;
}
.faqs-header .icon {height: 55px; margin-right: 32px;}
.faqs-header h3 {
  font-family: 'gilroy-semibold';
  font-size: 36px; 
}
.faqs-header .collection-preview {font-size: 20px; line-height: 1.6em; padding-bottom: 25px;}
.faqs-header .avatar {margin-top: -7px;}
.faqs-header .avatar img {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 3px white;
  box-shadow: 0 0 0 3px white;
  position: relative;
}
.faqs-header .avatar img:nth-child(n+2) {z-index: 1; margin-left: -10px;}
.faqs-header .avatar_fallback {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--blue-dark);
  border:3px solid #fff;
  line-height: 42px;
  color: #fff;
  font-size: 17px;
  z-index: 5;
  margin-left: -10px;
  text-align: center;
}
.faqs-header .collection-info {
  font-size: 16px;
  line-height: 1.5em;
  padding: 10px 0 0 0;
  color: #878787;
}
.faqs-header .collection-info span {color: var(--heading); font-weight: 500;}
.all-faqs .faqs-all-qus {margin-bottom: 70px;}
.all-faqs .faqs-all-qus .article-preview {
  background: #fff;
  padding: 45px 40px;
  margin-bottom: 18px;
  box-shadow: 0 15px 30px rgba(23,59,40,0.05);
}
.all-faqs .faqs-all-qus .article-preview[onclick*="location"] {cursor: pointer;}
.faqs-all-qus .article-preview .avatar-img {
  width: 50px; 
  height: 50px; 
  border-radius: 50%;
  margin-right: 22px;
}
.faqs-all-qus .article-preview h3 {
  font-size: 24px; 
  padding-bottom: 10px;
}
.faqs-all-qus .article-preview .avatar-info {
  font-size: 16px;
  line-height: 1.43em;
  color: #606060;
}
.faqs-all-qus .article-preview .avatar-info span {color: var(--heading);}
.faqs-all-qus .article-preview .article-details {
  border-top: 1px solid #D8D8D8;
  margin-top: 46px;
  padding: 38px 0 48px;
  color: rgb(0,0,0,0.8);
}
.faqs-all-qus .article-preview .article-details .list-meta {padding: 31px 0 32px 43px;}
.faqs-all-qus .article-preview .article-details .list-meta li {
  padding-left: 25px;
  position: relative;
  margin-bottom: 20px;
}
.faqs-all-qus .article-preview .article-details .list-meta li:before {
  content: url(../images/icon/49.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.faqs-all-qus .article-preview .article-details .reaction-wrapper {
  border: 2px solid #979797;
  border-radius: 5px;
  text-align: center;
  padding: 50px 15px;
  margin-top: 68px;
  position: relative;
  z-index: 1;
}
.faqs-all-qus .article-preview .article-details .reaction-wrapper h4 {font-size: 24px; padding-bottom: 24px;}
.faqs-all-qus .article-preview .article-details .reaction-wrapper button {margin: 0 11px; transition: all 0.2s ease-in-out;}
.faqs-all-qus .article-preview .article-details .reaction-wrapper button:hover {transform: translateY(-3px);}
.faqs-inner-page .more-faq-ask h3 {font-size: 36px;}
.faqs-inner-page .shape-one {
  left: 0;
  bottom: -140px;
  animation: jumpTwo 5s infinite linear;
}
.faqs-inner-page .shape-two {
  width: 30px;
  height: 30px;
  background: #FFDFDF;
  border-radius: 50%;
  top:-15px;
  left: 40%;
}
.faqs-inner-page .shape-three {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  border-radius: 50%;
  top:190px;
  right: 9%;
}
.faqs-inner-page .shape-four {
  width: 8px;
  height: 8px;
  background: #FFCD8B;
  border-radius: 50%;
  top:211px;
  left: 12%;
}
/*------------------ Login/Sign Up ----------------------*/
.user-data-page .illustration-wrapper {
  float: left;
  width: 48%;
  padding: 40px 0;
  min-height: 100vh;
  background: #F8FAFE;
  text-align: center;
}
.user-data-page .illustration-wrapper h3 {
  font-size: 36px;
  line-height: 1.27em;
  padding: 40px 0;
}
.user-data-page .illustration-wrapper h3 a {text-decoration: underline;}
.user-data-page .illustration-wrapper .illustration-holder {width: 100%;}
.user-data-page .illustration-wrapper .illustration-holder .illustration {width: 100%;}
.user-data-page .illustration-wrapper .illustration-holder .shape-one {
  bottom: 2%;
  left: 2%;
  z-index: 1;
  width: 60%;
  animation: jumpTwo 5s infinite linear;
}
.user-data-page .illustration-wrapper .illustration-holder .shape-two {
  bottom: 2%;
  left: 59%;
  z-index: 1;
  width: 40%;
  animation: jumpThree 5s infinite linear;
}
.user-data-page .form-wrapper {
  float: left;
  width: 47%;
  height: 100%;
  padding: 50px 4% 20px;
  position: relative;
}
.user-data-page .go-back-button {font-size: 15px;}
.user-data-page .go-back-button:hover {color: #000; text-decoration: underline;}
.user-data-page .form-wrapper h2 {
  font-family: 'Inter';
  font-size: 58px;
  line-height: 1.17em;
}
.user-data-page .form-wrapper .header-info {font-size: 22px; color: var(--heading);}
.user-data-page .form-wrapper .header-info a {color: var(--blue-dark); text-decoration: underline;}
.user-data-page .form-wrapper .copyright-text {font-size: 16px;}
.user-data-form .input-group-meta {
  height: 55px;
  position: relative;
}
.user-data-form .input-group-meta input {
  font-family: 'Inter', serif;
  width: 100%;
  height: 100%;
  font-size: 18px;
  border:none;
  border-bottom: solid 2px #000;
  padding: 0 52px 0 0;
  color: var(--heading);
  background: transparent;
}
.user-data-form .input-group-meta input:focus {border-color: var(--blue-dark);}
.user-data-form .input-group-meta label {
  font-size: 15px;
  font-weight: normal;
  color: #BFBFBF;
  position: absolute;
  left: 0;
  top:-21px;
}
.user-data-form .input-group-meta .placeholder_icon {
  position: absolute;
  line-height: 55px;
  top:0;
  right:0;
  bottom: 0;
  width: 50px;
  text-align: center;
  z-index: 1;
  color: rgba(0,0,0,0.45);
  font-size: 17px;
}
.user-data-form .input-group-meta .valid-sign img {opacity: 0; transition: all 0.2s ease-in-out;}
.user-data-form .input-group-meta input:valid + .valid-sign img {opacity: 1;}
.user-data-form .input-group-meta .placeholder_icon img {
  position: relative;
  top:50%;
  margin: 0 auto;
  transform: translateY(-50%);
}
.user-data-form .input-group-meta .placeholder_icon span {
  width: 100%;
  height: 100%; 
  cursor: pointer;
  display: block;
  position: relative;
}
.user-data-form .input-group-meta .placeholder_icon span:before {
  content: '';
  width: 2px;
  height: 26px;
  background: #000;
  position: absolute;
  top:15px;
  left: 24px;
  transform: rotate(45deg);
  z-index: 5;
  transition: all 0.2s ease-in-out;
}
.user-data-form .input-group-meta .placeholder_icon span.eye-slash:before {opacity: 0;}
.user-data-form .agreement-checkbox label {
  position: relative;
  font-size: 15px;
  color: var(--heading);
  cursor: pointer;
  padding-left: 22px;
  line-height: 18px;
  transition: all 0.1s ease-in-out;
}
.user-data-form .agreement-checkbox label a {
  color: var(--p-color);
  text-decoration: underline;
}
.user-data-form .agreement-checkbox input[type="checkbox"] {display: none;}
.user-data-form .agreement-checkbox label:before {
  content: '';
  width: 13px;
  height: 13px;
  line-height: 11px;
  border-radius: 2px;
  border: 2px solid rgba(0,0,0,0.3);
  font-size: 8px;
  text-align: center;
  position: absolute;
  left:0;
  top:2px;
  transition: all 0.1s ease-in-out;
}
.user-data-form .agreement-checkbox input[type="checkbox"]:checked + label:before {
  content: "";
  font-family: 'font-awesome';
  background: #000;
  color: #fff;
  border-color:  #000;
}
.user-data-form .agreement-checkbox a {
  position: relative;
  font-size: 15px;
  color: rgba(30,30,30,0.55);
}
.user-data-form .agreement-checkbox a:hover {text-decoration: underline; color:var(--p-color);}
.user-data-form .theme-btn-one { width: 100%;}
.user-data-page .full-height {
  min-height: 100vh; 
  padding: 20px 0;
  flex-direction: column;
  text-align: center;
}
.full-height .user-data-form .button-solid-one {width: 200px; margin: 0 auto 30px;}
/*------------------- Blog Pages ----------------*/
.blog-page-bg {
  background: linear-gradient(45deg, #FFFBF2, #EDFFFD);
  padding: 180px 0 150px;
  position: relative;
  z-index: 5;
}
.blog-page-white-bg {padding: 140px 0 0;}
.blog-page-bg .shape-one {
  width: 30px;
  height: 30px;
  background: #B183FF;
  border-radius: 50%;
  top:-15px;
  left: 40%;
  z-index: 1;
}
.blog-page-bg .shape-two {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  border-radius: 50%;
  top:190px;
  right: 9%;
}
.blog-page-bg .shape-three {
  width: 8px;
  height: 8px;
  background: #FFCD8B;
  border-radius: 50%;
  top:211px;
  left: 12%;
}
.feature-blog-one.width-lg .post-meta {padding: 35px 40px 80px;}
.feature-blog-one.width-lg .post-meta .read-more {left: 40px; right: 40px;}
.feature-blog-one.width-lg .post-meta p {
  font-size: 17px;
  line-height: 1.76em;
  padding-top: 35px;
}
.blog-sidebar-one {padding-left: 20px;}
.blog-sidebar-one .sidebar-search-form form {
  height: 60px;
  background: #fff;
  box-shadow: 5px 10px 25px rgba(15,24,40,0.04);
  position: relative;
}
.blog-v3 .blog-sidebar-one .sidebar-search-form form {
  box-shadow: none;
  border:1.5px solid #000;
  border-radius: 4px;
}
.blog-sidebar-one .sidebar-search-form form input {
  font-size: 17px;
  width: 100%;
  height: 100%;
  border:none;
  padding: 0 45px 0 20px;
  background: transparent;
}
.blog-sidebar-one .sidebar-search-form form button {
  width: 40px;
  top:0;
  right: 0;
  bottom: 0;
  position: absolute;
  background: transparent;
}
.blog-sidebar-one .sidebar-title {
  font-family: 'Inter';
  font-size: 30px;
  padding-bottom: 15px;
}
.blog-sidebar-one .sidebar-categories ul li a {
  font-family: 'Inter', serif;
  font-size: 18px;
  line-height: 60px;
  color: #000;
  border-bottom: 1px solid #ececec;
  display: block;
  transition: all 0.3s ease-in-out;
}
.blog-sidebar-one .sidebar-categories ul li a span {float: right;}
.blog-sidebar-one .sidebar-categories ul li:first-child a {border-top: 1px solid #ececec;}
.blog-sidebar-one .sidebar-categories ul li a:hover {color: var(--blue-dark);}
.blog-v3 .blog-sidebar-one .sidebar-categories ul li a:hover {color: var(--red-light);}
.blog-sidebar-one .sidebar-recent-news .title {
  font-family: 'Inter';
  font-size: 24px;
  line-height: 1.25em;
  color: #000;
  display: block;
  margin-bottom: 10px;
  transition: all 0.3s ease-in-out;
}
.blog-sidebar-one .sidebar-recent-news ul li a:hover .title {color: var(--blue-dark);}
.blog-v3 .blog-sidebar-one .sidebar-recent-news ul li a:hover .title {color: var(--red-light);}
.blog-sidebar-one .sidebar-recent-news .date {color: #A2A2A2;}
.blog-sidebar-one .sidebar-recent-news ul li a{
  border-bottom: 1px solid #ececec;
  padding: 17px 0;
}
.blog-sidebar-one .sidebar-recent-news ul li:first-child a {border-top: 1px solid #ececec;}
.blog-sidebar-one .sidebar-keyword ul {margin: 0 -5px;}
.blog-sidebar-one .sidebar-keyword ul li {float: left;padding: 0 5px; margin-bottom: 18px;}
.blog-sidebar-one .sidebar-keyword ul li a {
  font-weight: 500;
  font-size: 14px;
  background: #fff;
  line-height: 35px;
  border-radius: 4px;
  padding: 0 25px;
  color: #000;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease-in-out;
}
.blog-v3 .blog-sidebar-one .sidebar-keyword ul li a {
  box-shadow: none;
  border: 1px solid #000;
  border-radius: 4px;
}
.blog-sidebar-one .sidebar-keyword ul li a:hover {background: var(--blue-dark); color: #fff;}
.blog-v3 .blog-sidebar-one .sidebar-keyword ul li a:hover {background: var(--red-light); border-color: var(--red-light);}
.blog-sidebar-one .sidebar-keyword .sidebar-title {
  border-bottom: 1px solid #ececec;
  margin-bottom: 15px;
}
.feature-blog-three {padding-right: 40px;}
.feature-blog-three .post-meta .image-meta {border-radius: 5px 5px 0 0;}
.feature-blog-three .post-meta .post {
  border:1px solid #E4E4E4;
  margin-top: -8px;
  padding: 40px 38px 33px;
  border-radius: 8px;
}
.feature-blog-three .post-meta .post .date {font-size: 16px; color: rgba(0,0,0,0.3);}
.feature-blog-three .post-meta .post .title {
  font-family: 'Inter';
  font-size: 30px;
  line-height: 1.26em;
  color: #000;
  display: block;
  margin: 5px 0 25px;
  transition: all 0.3s ease-in-out;
}
.feature-blog-three .post-meta:hover .post .title {color: var(--red-light);}
.feature-blog-three .post-meta .post p {
  font-size: 17px;
  line-height: 1.64em;
}
.feature-blog-three .post-meta .post .read-more {
  font-size: 16px;
  font-weight: 500;
  color: #2A2A2A;
  margin-top: 30px;
  transition: all 0.3s ease-in-out;
}
.feature-blog-three .post-meta .post .read-more:hover {color: var(--red-light);}
.feature-blog-three .post-meta {margin-bottom: 60px;}
.blog-v4 .wrapper {max-width: 1000px; margin: 0 auto;}
.blog-v4 .post-meta {margin-bottom: 80px;}
.blog-v4 .post-meta .post {padding: 45px 0 0;}
.blog-v4 .post-meta .post .date {font-size: 16px; color: rgba(0,0,0,0.3);}
.blog-v4 .post-meta .post .title {
  font-family: 'gilroy-black'; 
  font-size: 48px;
  line-height: 1.04em;
  color: #000;
  display: block;
  margin: 5px 0 45px;
  transition: all 0.3s ease-in-out;
}
.blog-v4 .post-meta .post p {
  font-size: 20px;
  line-height: 1.7em;
}
.blog-v4 .post-meta .post .read-more {
  font-family: 'Inter';
  font-size: 18px;
  color: #2A2A2A;
  margin-top: 30px;
  transition: all 0.3s ease-in-out;
}
.blog-v4 .post-meta .post .read-more span {text-decoration: underline;}
.blog-v4 .post-meta.text-post {
  border:2px solid #000;
  border-radius: 5px;
}
.blog-v4 .post-meta.text-post .post {padding: 45px 40px;}
.blog-details-post-v1 .post-meta .mark-text {
  font-family: 'Inter';
  font-size: 23px;
  color: #000;
  line-height: 1.41em;
  padding-top: 35px;
}
.blog-details-post-v1 .post-meta h4 {
  font-family: 'gilroy-black'; 
  font-size: 36px;
  padding-top: 60px;
}
.blog-details-post-v1 .share-area {
  border-top: 1px solid rgba(0,0,0,0.09);
  padding: 25px 0;
  margin-top: 40px;
  width: 100%;
}
.blog-details-post-v1 .share-option li {
  font-size: 15px;
  color: var(--heading);
  font-style: italic;
  margin-left: 10px;
}
.blog-details-post-v1 .share-option li:first-child {margin-left: 0;}
.blog-details-post-v1 .share-option li a {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  text-align: center;
  line-height: 38px;
  color: #fff;
  font-size: 20px;
}
.blog-details-post-v1 .share-area .tag-feature li:first-child {
  font-family: 'Inter';
  color: #000;
  font-size: 15px;
}
.blog-details-post-v1 .share-area .tag-feature li a {font-size: 15px;}
.feature-blog-one.width-lg.blog-details-post-v1 .post-meta {padding-bottom: 0;}
.blog-details-post-v1 .comment-area {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 10px 40px rgb(0 0 0 / 8%);
  margin-bottom: 50px;
  padding: 50px 40px 30px;
}
.blog-details-post-v1 .comment-area .title {
  font-family: 'gilroy-black'; 
  font-size: 36px;
  padding-bottom: 40px;
}
.blog-details-post-v1 .comment-area .single-comment {
  border-top:1px solid #E5E5E5;
  padding: 35px 0;
  position: relative;
}
.blog-details-post-v1 .comment-area .user-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}
.blog-details-post-v1 .comment-area .comment {padding-left: 20px; position: relative;}
.blog-details-post-v1 .comment-area .name {
  font-family: 'gilroy-semibold';
  font-size: 20px;
}
.blog-details-post-v1 .comment-area .time {
  font-size: 16px;
  color: #ADADAD;
  padding: 5px 0 15px;
}
.blog-details-post-v1 .comment-area p {
  font-size: 17px;
  line-height: 1.58em;
}
.blog-details-post-v1 .comment-area .reply {
  position: absolute;
  top:0;
  right: 0;
  width: 70px;
  height: 29px;
  border-radius: 15px;
  background: var(--blue-dark);
  text-transform: uppercase;
  color: #fff;
  font-weight: 500;
  font-size: 14px;
}
.blog-details-post-v1 .comment-form-section {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 10px 40px rgb(0 0 0 / 8%);
  margin-bottom: 50px;
  padding: 50px 40px 60px;
}
.blog-details-post-v1 .comment-form-section .title {
  font-family: 'gilroy-black'; 
  font-size: 36px;
  padding-bottom: 15px;
}
.blog-details-post-v1 .comment-form-section p {
  font-size: 18px;
  color: #2A2A2A;
}
.blog-details-post-v1 .comment-form-section p a {
  font-weight: 500; 
  color: #000;
  text-decoration:underline;
}
.blog-details-post-v1 .comment-form-section .form-style-light {
  padding: 0;
  box-shadow: none;
  margin-top: 50px;
}
.blog-details-post-v1 .comment-form-section .form-style-light [class*="col-"] {display: block;}
/*=======================================================================
                              Documentation                
=========================================================================*/
.theme-menu-three .logo {
  position: absolute;
  left: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-three .right-widget {
  position: absolute;
  right: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-three .navbar {position: static;}
.theme-menu-three .user-login-button li a {
  font-size: 16px;
  font-weight: 500;
  line-height: 46px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.theme-menu-three .user-login-button li a:hover {color: var(--blue-light);}
.theme-menu-three .user-login-button .signIn-action img {margin-right: 12px; margin-top: -3px;}
.theme-menu-three .user-login-button li .signUp-action {
  border:2px solid #000;
  background: #000;
  padding: 0 20px;
  color: #fff;
  margin-left: 35px;
}
.theme-menu-three .user-login-button li .signUp-action img {margin-left: 12px;}
.theme-menu-three .user-login-button li .signUp-action:hover {
  background: var(--blue-light);
  border-color: var(--blue-light);
  color: #fff;
}
/*-------------------- Theme Hero Banner/Three -----------------*/
.hero-banner-three {padding: 40px 0 0; position: relative;}
.hero-banner-three:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 83%;
  background: url(../images/shape/67.svg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center top;
  left: 0;
  bottom: 78px;
  z-index: -1;
}
.hero-banner-three h1 {
  font-size: 100px;
  line-height: 1.05em;
  text-align: center;
}
.hero-banner-three .sub-text {
  font-size: 28px;
  line-height: 1.39em;
  font-weight: 300;
  color: #2A2A2A;
  text-align: center;
  padding: 25px 0 45px;
}
.hero-banner-three .sing-in-call {
  font-size: 16px; 
  padding-top: 25px; 
  color: #2C2C2C;
  text-align: center;
}
.hero-banner-three .sing-in-call a {color: var(--blue-light); transition: all 0.25s ease-in-out;}
.hero-banner-three .sing-in-call a:hover {text-decoration: underline;}
.hero-banner-three .illustration {margin: 95px auto 0;}
.search-filter-form form {
  max-width: 750px;
  margin: 0 auto;
  height: 78px;
  position: relative;
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 30px 50px rgba(7,21,36,0.06);
}
.search-filter-form input {
  width: 100%;
  height: 100%;
  border:none;
  padding: 0 260px 0 35px;
  font-size: 16px;
  border-radius: 5px;
}
.search-filter-form button {
  width: 85px;
  right: 7px;
  bottom: 7px;
  top:7px;
  background: #111111;
  position: absolute;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}
.search-filter-form button img {margin: 0 auto;}
.search-filter-form button:hover {background: var(--blue-light);}
.search-filter-form select {
  position: absolute;
  width: 135px;
  height: 35px;
  position: absolute;
  right: 120px;
  top:21px;
  border:none;
  border-radius: 0;
  border-left: 1px solid rgba(0,0,0,0.4) !important;
  outline: none !important;
  box-shadow: none !important;
}
.hero-banner-three .shape-one {
  top:5%;
  right: 15%;
  animation: rotatedTwo 25s infinite linear;
}
.hero-banner-three .shape-two {
  top:11%;
  right: 7%;
}
.hero-banner-three .shape-three {
  top:24%;
  right: 20%;
}
.hero-banner-three .shape-four {
  top:28%;
  right: 11%;
  animation: jumpTwo 5s infinite linear;
}
.hero-banner-three .shape-five {
  top:40%;
  right: 8%;
}
.hero-banner-three .shape-six {
  top:6%;
  left: 9%;
}
.hero-banner-three .shape-seven {
  top:19%;
  left: 7%;
}
.hero-banner-three .shape-eight {
  top:24%;
  left: 16%;
  animation: jumpThree 5s infinite linear;
}
.hero-banner-three .shape-nine {
  top:35%;
  left: 21%;
}
.hero-banner-three .shape-ten {
  top:42%;
  left: 11%;
  animation: rotatedTwo 50s infinite linear;
}
/*------------------ Fancy Feature Eight --------------*/
.block-style-ten {
  border:2px solid #EEF3F7;
  border-radius: 10px;
  text-align: center;
  padding: 75px 80px 45px;
  margin-top: 40px;
  transition: all 0.3s ease-in-out;
}
.block-style-ten:hover {border-color: #000;}
.block-style-ten .icon {height: 80px;}
.block-style-ten .icon img {margin: 0 auto; max-height: 100%;}
.block-style-ten .title {
  font-size: 20px;
  color: rgba(0,0,0,0.3);
  padding: 20px 0;
}
.block-style-ten p {
  font-size: 24px;
  line-height: 1.45em;
  color: #000;
  padding-bottom: 27px;
}
/*------------------ Fancy Text block Sixteen ---------------*/
.fancy-text-block-sixteen {
  position: relative;
  padding-bottom: 150px;
  z-index: 5;
}
.fancy-text-block-sixteen:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 82%;
  left: 0;
  bottom: 0;
  background: #F7FAFF;
  z-index: -1;
}
.fancy-text-block-sixteen .img-slick-slider {
  box-shadow: 0 30px 100px rgba(8,17,40,0.05);
  margin-bottom: 90px;
}
.fancy-text-block-sixteen .img-slick-slider .slick-dots {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: -84px;
}
.fancy-text-block-sixteen .img-slick-slider .slick-dots li {
  float: left;
  width: 33.333333%;
  height: 4px;
  background: rgba(216,216,216,0.35);
  position: relative;
}
.fancy-text-block-sixteen .img-slick-slider .slick-dots li button {
  text-indent: -50000px;
  position: absolute;
  left: 0;
  top:0;
  height: 4px;
  background: #FFD084;
  border-radius: 4px;
  width: 75%;
  opacity: 0;
  transform: scale(0,1);
  transform-origin: 0 100%;
  transition: all 0.3s ease-in-out;
}
.fancy-text-block-sixteen .img-slick-slider .slick-dots li.slick-active button {
  opacity: 1;
  transform: scale(1,1);
}
.fancy-text-block-sixteen .img-slick-slider .slick-dots li:last-child button {
  left: auto;
  right: 0;
}
.block-style-eleven .num {font-size: 30px;color: #202020;}
.block-style-eleven .title {
  font-size: 18px;
  color: rgba(0,0,0,0.5);
  padding: 17px 0 10px;
}
.block-style-eleven p {
  font-size: 25px;
  color: #000;
  line-height: 1.35em;
}
/*---------------------- Fancy Feature Eight --------------*/
.block-style-twelve .text-wrapper h6 {
  font-size: 16px;
  text-transform: uppercase;
  color: rgba(0,0,0,0.4);
  letter-spacing: 2px;
}
.block-style-twelve .text-wrapper .title {
  font-size: 42px;
  line-height: 1.28em;
  padding: 20px 0 30px;
}
.block-style-twelve .text-wrapper p {
  font-size: 22px;
  line-height: 1.59em;
}
.block-style-twelve {
  padding: 140px 0;
  position: relative;
}
.block-style-twelve:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 13px;
  left: 0;
  bottom: -7px;
  background: url(../images/shape/line-shape-9.svg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.block-style-twelve:last-child {padding-bottom: 0;}
.block-style-twelve:last-child:before {display: none;}
/*-------------------- Client Feedback Slider Two ---------------------*/
.client-feedback-slider-two {
  background: #F7FAFF;
  position: relative;
  padding: 90px 0 120px;
  z-index: 5;
}
.client-feedback-slider-two .slider-content {
  max-width: 1710px;
  padding: 0 15px;
  margin: 0 auto;
}
.client-feedback-slider-two .bg-wrapper {
  background: #fff;
  padding: 50px 40px 45px;
  position: relative;
  margin: 0 15px 70px;
  transition: all 0.3s ease-in-out;
}
.client-feedback-slider-two .slick-center .bg-wrapper {
  box-shadow: 0px 40px 40px rgba(2,7,7,0.03);
}
.client-feedback-slider-two .bg-wrapper:before {
  content: url(../images/icon/58.svg);
  position: absolute;
  right: 40px;
  bottom: 45px;
}
.client-feedback-slider-two .bg-wrapper .logo {height: 40px;}
.client-feedback-slider-two .bg-wrapper p {
  font-size: 22px;
  line-height: 1.72em;
  color: #2C2C2C;
  padding: 35px 0 40px;
}
.client-feedback-slider-two .bg-wrapper .name {font-size: 22px; color: #000;}
.client-feedback-slider-two .bg-wrapper .desig {
  font-size: 18px;
  color: #B7B7B7;
}
.client-feedback-slider-two .slick-dots {text-align: center;}
.client-feedback-slider-two .slick-dots li {
  display: inline-block;
  margin: 0 10px;
}
.client-feedback-slider-two .slick-dots button {
  text-indent: -550000px;
  width: 8px;
  height: 8px;
  background: rgba(0,0,0,0.12);
  border-radius: 50%;
}
.client-feedback-slider-two .slick-dots li.slick-active button {background: #FAAA4B;}
.client-feedback-slider-two .shape-one {
  top:-15px;
  right: 17%;
}
.client-feedback-slider-two .shape-two {
  top:14%;
  right: 7%;
  animation: rotatedTwo 15s infinite linear;
}
.client-feedback-slider-two .shape-three {
  bottom:11%;
  right: 0;
}
.client-feedback-slider-two .shape-four {
  bottom:-13px;
  right: 22%;
}
.client-feedback-slider-two .shape-five {
  bottom:-15px;
  left: 22%;
}
.client-feedback-slider-two .shape-six {
  bottom:13%;
  left: 8%;
  animation: rotatedTwo 15s infinite linear;
}
.client-feedback-slider-two .shape-seven {
  top:10%;
  left: 0;
}
.client-feedback-slider-two .shape-eight {
  top:-13px;
  left: 19%;
}
/*------------------- Useable Tools -------------*/
.useable-tools-section-three {position: relative;}
.useable-tools-section-three .logo-wrapper {
  position: absolute;
  right: -7%;
  top:50%;
  transform: translateY(-50%);
  max-width: 63%;
}
.useable-tools-section-three .logo-wrapper .logo {
  border-radius: 50%;
  margin: 12px 4%;
}
.useable-tools-section-three .logo-wrapper .logo.bg-white {
  background: #FFFFFF;
  box-shadow: 15px 30px 50px rgba(23, 32, 90, 0.0696193);
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(1) {
  width: 165px;
  height: 165px;
  background: #FFE3E3;
  animation: jumpTwo 5s infinite linear;
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(2) {
  width: 110px;
  height: 110px;
  background: #E8F8FF;
  animation: jumpThree 5s infinite linear;
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(3) {
  width: 165px;
  height: 165px;
  background: #EAFFE7;
  animation: jumpTwo 5s infinite linear;
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(4) {
  width: 140px;
  height: 140px;
  background: #F3F1FF;
  animation: jumpThree 5s infinite linear;
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(5) {
  width: 125px;
  height: 125px;
  background: #F0F5EF;
  animation: jumpThree 5s infinite linear;
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(6) {
  width: 185px;
  height: 185px;
  background: #FFF9E5;
  animation: jumpTwo 5s infinite linear;
}
.useable-tools-section-three .logo-wrapper .logo:nth-child(7) {
  width: 125px;
  height: 125px;
  background: #E5FFFE;
  animation: jumpThree 5s infinite linear;
}
.useable-tools-section-three .sub-text {
  font-size: 22px;
  line-height: 1.59em;
  color: #000;
  padding: 50px 50px 40px 0;
}
.useable-tools-section-three .all-button {
  font-weight: 500;
  font-size: 20px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.useable-tools-section-three .all-button i {margin-left: 5px;}
.useable-tools-section-three .all-button:hover {color: var(--blue-light)}
/*------------------ Fancy Short Banner Four ----------------*/
.fancy-short-banner-four {
  margin-top: -135px;
  position: relative;
  z-index: 5;
}
.fancy-short-banner-four .bg-wrapper {
  background: #1B1B1B;
  border-radius: 3px;
  padding: 65px 50px;
}
.fancy-short-banner-four .title h6 {
  font-size: 16px;
  text-transform: uppercase;
  color: #C0C0C0;
  letter-spacing: 1px;
  padding-bottom: 13px;
}
.fancy-short-banner-four .title h2 {
  font-family: 'Inter';
  font-size: 52px;
  line-height: 1.15em;
  color: #fff;
}
.fancy-short-banner-four .form-wrapper form {
  position: relative;
  height: 70px;
  border-radius: 5px;
  border:1.5px solid #fff;
}
.fancy-short-banner-four .form-wrapper form input {
  font-style: italic;
  color: #fff;
  font-size: 18px;
  width: 100%;
  border:none;
  height: 100%;
  padding: 0 150px 0 25px;
  background: transparent;
}
.fancy-short-banner-four .form-wrapper form button {
  position: absolute;
  right: 7px;
  bottom: 7px;
  top:7px;
  width: 160px;
  border-radius: 5px;
  color: #000;
  font-size: 16px;
  background: #fff;
  transition: all 0.3s ease-in-out;
}
.fancy-short-banner-four .form-wrapper form button:hover {background: var(--blue-light); color: #fff;}
.fancy-short-banner-four .form-wrapper p {
  font-size: 16px;
  color: #999999;
  padding-top: 10px;
}
.fancy-short-banner-four .form-wrapper p a {color: #fff;}
.fancy-short-banner-four .form-wrapper p a:hover {text-decoration: underline;}
/*------------------ Footer Style Three ----------------*/
.theme-footer-three {position: relative;}
.theme-footer-three:before {
  content: url(../images/shape/92.svg);
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.theme-footer-three .top-footer [class*="col-"] {margin-bottom: 30px; z-index: 1; position: relative;}
.theme-footer-three .top-footer .footer-title {
  font-family: 'Inter', serif;
  font-size: 24px;
  padding-bottom: 18px;
}
.theme-footer-three .top-footer .footer-list ul li a {
  font-size: 16px;
  line-height: 40px;
  color: #727272;
  transition: all 0.3s ease-in-out;
}
.theme-footer-three .top-footer .footer-list ul li a:hover {color: var(--blue-light);}
.theme-footer-three .top-footer .address-list p {
  font-size: 16px;
  line-height: 25px;
  color: rgba(0,0,0,0.35);
  padding-bottom: 15px;
}
.theme-footer-three .top-footer .address-list ul.info li a {
  color: #1C1C1C;
  margin-bottom: 10px;
}
.theme-footer-three .top-footer .address-list ul.info li a:hover {text-decoration: underline;}
.theme-footer-three .top-footer .address-list .social-icon a {
  font-size: 22px;
  margin-right: 20px;
  color: rgba(0,0,0,0.35);
}
.theme-footer-three .top-footer .address-list .social-icon a:hover {color: #000;}
.theme-footer-three .bottom-footer-content ul li {display: inline-block;}
.theme-footer-three .bottom-footer-content ul li a {
  font-size: 16px;
  color: #727272;
  margin-left: 20px;
}
.theme-footer-three .bottom-footer-content ul li a:hover {color: #000;}
.theme-footer-three .bottom-footer-content p {font-size: 15px; color: #212121; padding-left: 35px;}
.theme-footer-three .bottom-footer-content {padding: 26px 0 30px;}
/*-------------------- Fancy Hero Five ---------------------*/
.fancy-hero-five {
  background: #F7FAFF;
  padding: 220px 0 130px;
  position: relative;
  z-index: 5;
}
.fancy-hero-five .page-title {
  font-size: 20px;
  color: #B1B1B1;
  letter-spacing: 2px;
  text-transform: uppercase;
  padding-bottom: 10px;
}
.fancy-hero-five .heading {
  font-family: 'gilroy-black';
  font-size: 80px;
  line-height: 1.08em;
}
.fancy-hero-five span {position: relative;z-index: 1;}
.fancy-hero-five span img {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.fancy-hero-five .sub-heading {
  font-size: 24px;
  line-height: 1.58em;
  color: #2A2A2A;
  padding-top: 50px;
  padding-right: 50px;
}
.fancy-hero-five .sub-heading.space-xs {padding: 15px 0 0;}
.fancy-hero-five .img-meta {
  bottom: -250px;
  right: 33px;
  position: absolute;
}
.fancy-hero-five .shape-one {
  right: 0;
  top:24%;
}
.fancy-hero-five .shape-two {
  left: 0;
  top:28%;
}
/*------------------- Fancy Text block Seventeen ----------------*/
.fancy-text-block-seventeen .text-meta {
  font-size: 24px;
  line-height: 1.66em;
  color: #2A2A2A;
  padding-bottom: 40px;
}
/*------------------- Fancy Text block Eighteen ----------------*/
.fancy-text-block-eighteen {
  background: linear-gradient(180deg, #FFFFFF, #EDF2F9);
  padding-bottom: 425px;
}
.fancy-text-block-eighteen .illustration {
  margin: 0 auto;
}
/*------------------ Team Section Three --------------*/
.team-section-three {
  position: relative;
  z-index: 1;
  margin-top: -295px;
  max-width: 1920px;
  margin-left: auto;
  margin-right: auto;
}
.teamSliderOne {
  max-width: 1680px;
  margin-right: -127px;
  margin-left: auto;
}
.teamSliderOne .team-member {margin: 50px 15px;}
.team-block-one {cursor: pointer; transition: all 0.3s ease-in-out;}
.team-block-one:hover {transform: translateY(-5px);}
.team-block-one .info {
  background: #fff;
  border-radius: 10px 10px 0 0;
  padding: 32px 45px;
}
.team-block-one .info .desig {color: rgba(0,0,0,0.3);}
.team-block-one .info .name {
  font-size: 28px;
  font-weight: 500;
  color: #2A2A2A;
  padding-top: 5px;
}
.team-block-one .img-meta {overflow: hidden;}
.team-block-one .img-meta img {
  width: 100%;
  border-radius: 0 0px 10px 10px;
}
.team-section-three .slider-arrows li {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #fff;
  text-align: center;
  line-height: 50px;
  color: #2A2A2A;
  font-size: 28px;
  margin-left: 15px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.team-section-three .slider-arrows li:hover {background: var(--blue-light); color: #fff;}
.teamSliderOne .slick-current .team-member {
  box-shadow: 0 20px 30px rgba(0,0,0,0.05);
}
/*---------------------- Map Area One ----------------*/
.map-area-one {height: 700px; position: relative;}
.map-area-one .map-canvas {height: 100%;}
.map-area-one .si-content {
  background: #fff;
  padding: 35px 30px 35px 50px;
  border-bottom: 5px solid #33D4B8;
  position: absolute;
  top:20%;
  left:20%;
  width: 440px;
  height: 240px;
  z-index: 5;
}
.map-area-one .si-content h3 {
  font-family: 'gilroy-black';
  font-size: 42px;
}
.map-area-one .si-content p {
  font-size: 18px;
  line-height: 29px;
  color: #2A2A2A;
  padding: 5px 0 25px;
}
.map-area-one .si-content span {
  font-size: 18px;
  color: rgba(42,42,42,0.3);
}
/*----------------- Documentation ------------------*/
.doc-container.full-width {
  padding: 0 45px;
  max-width: 1920px;
  margin-left:auto;
  margin-right: auto;
}
.doc-container.top-border {
  border-top: 1px solid rgba(0,0,0,0.09);
  margin-top: 150px;
}
.doc-container.top-border .doc-main-body,
.doc-container.top-border .doc-sidebar,
.doc-container.top-border .doc-sideNav {
  padding-top: 50px;
}
.doc-sidebar {
  border-right: 1px solid rgba(0,0,0,0.09); 
  height: 100vh;
  position: sticky;
  top: 90px;
}
.doc-sidebar .search-form {
  border-bottom: 1px solid rgba(0,0,0,0.09);
  padding-bottom: 30px;
  margin-bottom: 30px;
}
.doc-sidebar .search-form form {position: relative; height: 50px;}
.doc-sidebar .search-form form input {
  width: 100%;
  height: 100%;
  border:2px solid #000;
  border-radius: 5px;
  font-size: 16px;
  color: #2A2A2A;
  padding: 0 15px 0 50px;
}
.doc-sidebar .search-form form button {
  position: absolute;
  top:0;
  bottom: 0;
  left:0;
  width: 50px;
}
.doc-sidebar .search-form form button img {margin: 0 auto;}
#doc-sidebar-nav {
  max-height: 100vh; 
  overflow-y: auto; 
  padding-right: 45px;
}
.doc-sidebar .list-item>li {
  border-bottom: 2px solid #000;
  padding: 18px 0;
}
.doc-sidebar .list-item>li:first-child {padding-top: 0;}
.doc-sidebar .list-item li .sub-menu {padding-bottom: 15px;}
.doc-sidebar .list-item li h4 {
  font-family: 'gilroy-semibold';
  display: block;
  position: relative;
  line-height: 44px;
  font-size: 24px;
  color: #2A2A2A;
  cursor: pointer;
  margin-bottom: 6px;
}
.doc-sidebar .list-item li h4 .expander {
  position: absolute;
  background: transparent;
  line-height: 50px;
  right: 0;
  top:0;
  color: inherit;
  font-size: 12px;
}
.doc-sidebar .list-item li .sub-menu a {
  font-size: 16px;
  line-height: 36px;
  color: #2A2A2A;
  display: block;
  padding: 0 15px;
  transition: all 0.3s ease-in-out;
}
.doc-sidebar .list-item li .sub-menu a.active {
  background: #000;
  color: #fff;
  line-height: 32px;
  text-decoration: none;
}
.doc-sidebar .list-item li .sub-menu a.sec-menu {position: relative;}
.doc-sidebar .list-item li .sub-menu a.sec-menu:before {
  content: '';
  position: absolute;
  font-family: 'font-awesome';
  right: 10px;
  top:0;
  line-height: 32px;
  color: #000;
}
.doc-sidebar .list-item li .sub-menu .main-side-nav {margin-top: 10px;}
.doc-sidebar .list-item li .sub-menu .main-side-nav a {margin-left: 15px;}
.doc-sidebar .list-item li .sub-menu a:hover {text-decoration: underline;}
.doc-sidebar ul .sub-menu {
  display: none;
  transition: all 0.3s ease-in-out;
}
.doc-sidebar ul .sub-menu.show {display: block;}
.doc-sidebar ul .sub-menu.open {display: block;}

.doc-container .doc-main-body {
  padding-left: 50px;
  padding-bottom: 65px;
}
.doc-container.full-width .doc-main-body {
  border-right: 1px solid rgba(0,0,0,0.09);
  padding-right: 50px;
}
.doc-container .doc-main-body h2 {
  font-family: 'gilroy-black';
  font-size: 42px;
  padding-bottom: 25px;
}
.doc-container .doc-main-body h3 {
  font-family: 'gilroy-semibold';
  font-size: 28px;
  padding-bottom: 30px;
}
.doc-container .doc-main-body h3:not(:first-child) {padding-top: 20px;}
.doc-container .doc-main-body p {color: #2A2A2A; padding-bottom: 30px;}
.doc-container .doc-main-body p code {
  background: #F0F3F9;
  border-radius: 4px;
}
.doc-container .doc-main-body .mark-blue {
  background: #F4FAFA;
  border-left: 4px solid #9CDFC9;
  border-radius: 0 8px 8px 0;
  padding: 22px 40px;
  margin-bottom: 45px;
}
.doc-container .doc-main-body .mark-blue pre {
  font-size: 16px;
  color: #414141;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
}
.doc-container .doc-main-body .mark-blue code {color: #e83e8c;}
.doc-container .doc-main-body .mark-blue pre .s1 {color: #78D0B6;}
.doc-container .doc-main-body .mark-red {
  background: #FCFAF3;
  border-left: 4px solid #FAE6BF;
  border-radius: 0 8px 8px 0;
  padding: 22px 40px;
  margin-bottom: 45px;
}
.doc-container .doc-main-body .mark-red pre {
  font-size: 16px;
  color: #575858;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
}
.doc-container .doc-main-body .mark-red pre .s1 {
  color: #78D0B6;
  background: #fff;
}
.doc-container .doc-main-body .bg-black {
  background: #2D2D2D;
  border-radius: 8px;
  margin-bottom: 40px;
  padding: 30px 45px 0;
  max-height: 740px;
  white-space: pre-wrap;
  color: inherit;
  font-size: 16px;
}
.doc-container .doc-main-body .bg-black code {white-space: pre-wrap;}
.doc-container .doc-main-body .bg-black .s1 {color: #5E5E5E;}
.doc-container .doc-main-body .bg-black .s2 {color: #CACACA;}
.doc-container .doc-main-body .bg-black .s3 {color: #21B787;}
.doc-container .doc-main-body .list-item-one li {
  font-size: 16px;
  line-height: 30px;
  color: #000000;
  position: relative;
  padding-left: 15px;
  margin-bottom: 5px;
}
.doc-container .doc-main-body .list-item-one li:before {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #000;
  left:0;
  top:11px;
}
.doc-container .doc-main-body .img-meta {
  margin: 40px auto;
}
.doc-pagination ul li a {
  width: 35px;
  height: 35px;
  line-height: 31px;
  border:2px solid #000;
  text-align: center;
  border-radius: 50%;
  font-size: 28px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.doc-pagination ul li a:hover {background: #000; color: #fff;}
.doc-pagination ul li span {
  font-family: 'gilroy-semibold';
  font-size: 22px;
  color: #000;
}
.doc-sideNav .wrapper {padding-left: 40px;}
.doc-sideNav .nav-link {
  display: block;
  font-size: 16px;
  line-height: 36px;
  color: #767676;
  padding: 0 15px;
  transition: all 0.3s ease-in-out;
}
.doc-sideNav .nav-link.active {
  line-height: 32px;
  background: #000;
  color: #fff;
}
.doc-sideNav {
  top: 100px;
  position: sticky;
  height: 100%;
}
/*=======================================================================
                              Event               
=========================================================================*/
.theme-main-menu.sticky-menu.fixed.theme-menu-four {background: #FBF3EC;}
.theme-menu-four .logo img {max-width: 120px;}
.theme-menu-four .navbar-nav .nav-item .nav-link {
  font-family: 'Roboto', sans-serif;
  font-size: 20px;
  margin: 0 24px;
}
.theme-menu-four .right-button-group .signIn-action {
  font-weight: 500;
  font-size: 20px;
  line-height: 45px;
  color: var(--heading);
  margin-right: 40px;
  transition: all 0.3s ease-in-out;
}
.theme-menu-four .right-button-group .signIn-action:hover {color: var(--vin-red);}
.theme-menu-four .right-button-group {margin-left: 100px;}
/*---------------------- Theme Hero Banner/Four ---------------*/
.hero-banner-four {
  position: relative;
  background: #FBF3EC;
  padding: 250px 0 110px;
  z-index: 1;
}
.hero-banner-four:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 103px;
  left: 0;
  bottom: -100px;
  background: url(../images/shape/99.svg) no-repeat center bottom;
  background-size: cover;
}
.hero-banner-four .shape-four {
  top: 1%;
  left: 15%;
}
.hero-banner-four .shape-five {
  top: 11%;
  right: 31%;
}
.hero-banner-four .shape-six {
  bottom: 5%;
  left: 0;
}
.hero-banner-four .shape-seven {
  bottom: 8%;
  right: 0;
}
.hero-banner-four h1 {
  font-family: 'Inter', serif;
  font-size: 85px;
  line-height: 1.14em;
}
.hero-banner-four h1 span {position: relative; z-index: 5;}
.hero-banner-four h1 span:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 25px;
  background: rgba(241,193,83,0.4);
  left:0;
  bottom: 12px;
  z-index: -1;
}
.hero-banner-four .sub-text {
  font-size: 28px;
  color: #565657;
  line-height: 1.4em;
  padding: 50px 0 65px;
}
.hero-banner-four .review-text {
  text-align: right;
  font-size: 20px;
  color: #000;
  padding-top: 30px;
}
.hero-banner-four .review-text span {
  font-size: 1.6em;
  color: var(--vin-red);
  text-decoration: underline;
}
.hero-banner-four .illustration {max-width: 660px;}
.hero-banner-four .illustration-holder .shape-one {
  width: 14%;
  top:9%;
  right: 8%;
  z-index: 1;
  animation: jump10p 5s infinite linear;
}
.hero-banner-four .illustration-holder .shape-two {
  width: 14%;
  top:0;
  right: 30%;
  z-index: 1;
  animation: jump10pRsv 5s infinite linear;
}
.hero-banner-four .illustration-holder .shape-three {
  width: 15%;
  top:9%;
  right: 51%;
  z-index: 1;
  animation: jump10p 5s infinite linear;
}
/*-------------Fancy Feature Eight -----------*/
.fancy-feature-eight .bg-wrapper {
  position: relative;
  z-index: 5;
  background: #fff;
  padding: 35px 0 42px;
}
.fancy-feature-eight .bg-wrapper:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 24px;
  background: url(../images/shape/97.svg) no-repeat center top;
  left: 0;
  top: -20px;
}
.fancy-feature-eight .bg-wrapper:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 13px;
  background: url(../images/shape/98.svg) no-repeat center bottom;
  left: 0;
  bottom: -10px;
}
.block-style-thirteen {
  position: relative;
  text-align: center;
  padding: 32px 40px 20px;
}
.block-style-thirteen.style-border {
  border-left: 1px solid #E7E7E7;
  border-right: 1px solid #E7E7E7;
}
.block-style-thirteen .icon {height: 70px;}
.block-style-thirteen .icon img {margin: 0 auto; max-height: 100%;}
.block-style-thirteen .title {
  font-size: 18px;
  color: rgba(0,0,0,0.3);
  padding: 15px 0 20px;
}
.block-style-thirteen p {
  font-size: 24px;
  line-height: 1.45em;
  color: #000;
}
/*------------------ Fancy Text block Nineteen ----------------*/
.fancy-text-block-nineteen .text-wrapper .client-info {
  font-size: 24px;
  color: #A09A92;
  padding-bottom: 20px;
}
.fancy-text-block-nineteen .text-wrapper .client-info span {
  color: #000;
  text-decoration: underline;
}
.fancy-text-block-nineteen .text-wrapper p {
  font-size: 24px;
  color: #0E0E0E;
  line-height: 1.58em;
  padding-top: 40px;
}
.fancy-text-block-nineteen .text-wrapper .name {
  font-size: 24px;
  color: #000;
  position: relative;
  padding-left: 28px;
  margin-top: 50px;
}
.fancy-text-block-nineteen .text-wrapper .name:before {
  content: '';
  width: 18px;
  height: 3px;
  border-radius: 2px;
  background: #000;
  position: absolute;
  left: 0;
  top:13px;
}
/*------------------ Fancy Feature Nine --------------*/
.fancy-feature-nine {
  position: relative;
  background: #DFF1EE;
  padding: 140px 0 135px;
  z-index: 5;
}
.fancy-feature-nine:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 52px;
  background: url(../images/shape/105.svg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -50px;
}
.fancy-feature-nine:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 75px;
  background: url(../images/shape/106.svg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -70px;
}
.fancy-feature-nine .shape-one {
  top: 7%;
  right: 13%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-nine .shape-two {
  top: 45%;
  right: 5%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-nine .shape-three {
  top: 88%;
  right: 10%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-nine .shape-four {
  top: 89%;
  left: 11%;
  animation: jumpTwo 5s infinite linear;
}
.fancy-feature-nine .shape-five {
  top: 43%;
  left: 4%;
  animation: jumpThree 5s infinite linear;
}
.fancy-feature-nine .shape-six {
  top: 9%;
  left: 15%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-fourteen {
  position: relative;
  padding: 0 25px;
  margin-top: 55px;
  text-align: center;
}
.block-style-fourteen .illustration {height: 220px;}
.block-style-fourteen .illustration img {
  margin: 0 auto;
  max-height: 100%;
}
.block-style-fourteen .title {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.35);
  padding: 20px 0 15px;
}
.block-style-fourteen p {
  font-size: 23px;
  line-height: 1.52em;
  color: #000;
}
.block-style-fourteen.arrow-shape:before,
.block-style-fourteen.arrow-shape:after {
  content: url(../images/shape/104.svg);
  position: absolute;
  top:26%;
  z-index: 1;
}
.block-style-fourteen.arrow-shape:before {left: -20%;}
.block-style-fourteen.arrow-shape:after {right: -20%;}
/*--------------------- Pricing Section Three ------------------*/
.pricing-section-three {
  background: #FDECD8;
  position: relative;
  z-index: 1;
  padding: 105px 0 170px;
}
.pricing-section-three:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 85px;
  background: url(../images/shape/115.svg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -84px;
}
.pricing-section-three:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 33px;
  background: url(../images/shape/116.svg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -32px;
}
.pricing-section-three .shape-one {
  top: 7%;
  right: 13%;
  animation: jumpThree 5s infinite linear;
}
.pricing-section-three .shape-two {
  top: 45%;
  right: 5%;
  animation: jumpTwo 5s infinite linear;
}
.pricing-section-three .shape-three {
  top: 88%;
  right: 10%;
  animation: jumpThree 5s infinite linear;
}
.pricing-section-three .shape-four {
  top: 89%;
  left: 11%;
  animation: jumpTwo 5s infinite linear;
}
.pricing-section-three .shape-five {
  top: 43%;
  left: 4%;
  animation: jumpThree 5s infinite linear;
}
.pricing-section-three .shape-six {
  top: 9%;
  left: 15%;
  animation: jumpTwo 5s infinite linear;
}
.pricing-section-three .pricing-table-area-three {position: relative;}
.pricing-section-three .pricing-table-area-three:before {
  content: url(../images/shape/117.svg);
  position: absolute;
  left: -5%;
  top: -5%;
  z-index: -1;
}
.pricing-table-area-three .pr-table-wrapper {
  background: #fff;
  padding: 40px 20px 40px;
  text-align: center;
  margin-top: 45px;
  position: relative;
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-three .pr-table-wrapper:hover {transform: translateY(-10px);}
.pricing-table-area-three .pr-table-wrapper.skew-right:before {
  content: '';
  position: absolute;
  top: -8px;
  right: 0;
  width: 100%;
  height: 25px;
  background: #fff;
  transform: skewY(2.3deg);
}
.pricing-table-area-three .pr-table-wrapper.skew-left:before {
  content: '';
  position: absolute;
  top: -8px;
  right: 0;
  width: 100%;
  height: 25px;
  background: #fff;
  transform: skewY(-2.3deg);
}
.pricing-table-area-three .pr-table-wrapper .pack-name {
  font-size: 32px;
  color: #000;
}
.pricing-table-area-three .pr-table-wrapper .pack-name span {position: relative;z-index: 1;}
.pricing-table-area-three .pr-table-wrapper .pack-name span:before {
  content: '';
  width: 109%;
  height: 15px;
  position: absolute;
  left: 50%;
  bottom: 5px;
  transform: translateX(-50%);
  z-index: -1;
}
.pricing-table-area-three .pr-table-wrapper .pack-name.pc1 span:before {background: #D4FDF1;}
.pricing-table-area-three .pr-table-wrapper .pack-name.pc2 span:before {background: #DBF7FF;}
.pricing-table-area-three .pr-table-wrapper .pack-name.pc3 span:before {background: #FFE3E3;}

.pricing-table-area-three .pr-table-wrapper .price {
  font-size: 65px;
  color: #000;
  padding: 24px 0 0;
  line-height: initial;
}
.pricing-table-area-three .pr-table-wrapper .user-condition {font-size: 24px; color: #000;}
.pricing-table-area-three .pr-table-wrapper .line {
  width: 100%;
  margin: 25px auto 38px;
}
.pricing-table-area-three .pr-table-wrapper ul {text-align: left; padding-left: 18px;}
.pricing-table-area-three .pr-table-wrapper ul li {
  font-family: 'Inter', serif;
  font-size: 17px;
  line-height: 42px;
  color: #464646;
  position: relative;
  padding-left:30px;
}
.pricing-table-area-three .pr-table-wrapper ul li.disable {color: #B9B9B9;}
.pricing-table-area-three .pr-table-wrapper ul li:before{
  content: url(../images/icon/63.svg);
  position: absolute;
  left: 0;
  top: 1px;
}
.pricing-table-area-three .pr-table-wrapper ul li.disable:before{
  content: url(../images/icon/64.svg);
  top: 5px;
  left: -8px;
}
.pricing-table-area-three .pr-table-wrapper .subscribe-btn {
  display: block;
  width: 95%;
  margin: 37px auto 0;
  line-height: 46px;
  border: 2px solid var(--purple-blue);
  text-align: center;
  color: var(--purple-blue);
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-three .pr-table-wrapper .subscribe-btn:hover {
  background:var(--purple-blue);
  color: #fff;
}
.pricing-section-three-inner-page {
  background: #FBF3EC;
  position: relative;
  z-index: 1;
  padding: 215px 0 110px;
}
.pricing-section-three-inner-page .shape-one {left: 0; top: 19%;}
.pricing-section-three-inner-page .shape-two {right: 0; top: 19%;}
.pricing-section-three-inner-page:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 103px;
  left: 0;
  bottom: -100px;
  background: url(../images/shape/99.svg) no-repeat center bottom;
  background-size: cover;
}
/*-------------------- Client Feedback Slider Three ---------------------*/
.client-feedback-slider-three {position: relative; z-index: 5;}
.client-feedback-slider-three .shape_1 {left: 18%; top:-3%;}
.client-feedback-slider-three .shape_2 {left: 9%; top:38%;}
.client-feedback-slider-three .shape_3 {left: 13%; top:85%;}
.client-feedback-slider-three .shape_4 {right: 18%; top:-4%;}
.client-feedback-slider-three .shape_5 {right: 4%; top:33%;}
.client-feedback-slider-three .shape_6 {right: 19%; top:87%;}
.clientSliderThree {text-align: center;}
.clientSliderThree p {
  font-family: 'Inter', serif;
  font-size: 28px;
  line-height: 1.71em;
  color: rgba(0, 0, 0, 0.8);
  padding: 25px 0 45px;
}
.clientSliderThree .name {
  font-weight: 500;
  font-size: 24px;
  color: #000;
}
.clientSliderThree .desig {color: #A8AFAE;}
.client-feedback-slider-three .slider-arrow li {
  cursor: pointer;
  margin: 0 10px;
  font-size: 40px;
  color: #5D5D5D;
  transition: all 0.3s ease-in-out;
  transform: scale(0.8);
}
.client-feedback-slider-three .slider-arrow li:first-child i {transform: rotate(180deg); display: inline-block;}
.client-feedback-slider-three .slider-arrow li:hover {
  transform: scale(1);
}
/*------------------- Faq Section Four ----------------*/
.faq-section-four {
  background: #DFF1EE;
  position: relative;
  z-index: 1;
  padding: 78px 0 150px;
}
.faq-section-four.bg-white {background: #fff;}
.faq-section-four.bg-white:before,.faq-section-four.bg-white:after {display: none;}
.faq-section-four:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100px;
  background: url(../images/shape/118.svg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -98px;
}
.faq-section-four:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 33px;
  background: url(../images/shape/119.svg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -32px;
}
.faq-section-four .shape-one {left: 0; top: 21%}
.faq-section-four .shape-two {right: 0; top: 75%}
.accordion-style-four .card {
  background: #fff;
  border-radius: 0;
  border: none;
  margin-bottom: 10px;
}
.faq-section-four.bg-white .accordion-style-four .card {background: #F7F7F7;}
.accordion-style-four .card .card-header {
  background: transparent;
  border-radius: 0;
  padding: 0;
  border:none;
}
.accordion-style-four .card .card-header button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 27px 35px 27px 45px;
  font-family: 'Inter', serif;
  font-size: 24px;
  border:none;
  border-radius: 0;
  margin: 0;
  color: var(--heading);
  text-decoration: none;
  position: relative;
}
.accordion-style-four .card .card-header button:before {  
  content: "\f107";
  font-family: 'font-awesome';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.accordion-style-four .card .card-body {
  padding: 0 50px 20px 45px;
}
.accordion-style-four .card .card-body p {padding-bottom: 15px;}
.accordion-style-four .card .card-body p a {
  text-decoration: underline;
  color: var(--p-color);
}
/*------------------ Fancy Short Banner Five ---------------*/
.fancy-short-banner-five.with-bg {
  position: relative;
  background: #DFF1EE;
  z-index: 1;
}
.fancy-short-banner-five.with-bg .shape-one {right: 5%; top: 20%;}
.fancy-short-banner-five.with-bg .shape-two {left: 5%; top: 20%;}
.fancy-short-banner-five .sub-heading {
  font-size: 28px;
  line-height: 1.32em;
  color: #000;
  text-align: center;
  padding: 40px 0 55px;
}
.fancy-short-banner-five form {
  height: 70px;
  position: relative;
  margin-bottom: 17px;
}
.fancy-short-banner-five form input {
  width: 100%;
  height: 100%;
  border: 2px solid #000;
  padding: 0 170px 0 40px;
  color: #000;
}
.fancy-short-banner-five form ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #000;
  opacity: 1; /* Firefox */
}
.fancy-short-banner-five form :-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: #000;
}
.fancy-short-banner-five form ::-ms-input-placeholder { /* Microsoft Edge */
  color: #000;
}
.fancy-short-banner-five form button {
  position: absolute;
  width: 160px;
  right: 8px;
  top: 8px;
  bottom: 8px;
  background: var(--purple-blue);
  color: #fff;
  font-size: 16px;
  transition: all 0.3s ease-in-out;
}
.fancy-short-banner-five form button:hover {background: var(--vin-red);}
.fancy-short-banner-five .info-text {
  text-align: center;
  font-size: 16px;
  color: #999999;
}
.fancy-short-banner-five .info-text a {color: #000;}
.fancy-short-banner-five .info-text a:hover {text-decoration: underline;}
/*-------------------- Footer Style Four --------------------*/
.theme-footer-four {
  background: #FBF3EC;
  padding: 80px 0 70px;
  position: relative;
  z-index: 5;
}
.theme-footer-four:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 52px;
  background: url(../images/shape/122.svg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -50px;
}
.theme-footer-four .footer-about-widget ul li a {
  font-size: 20px; 
  color:#343434;
  margin-bottom: 5px;
  transition: all 0.2s ease-in-out;
}
.theme-footer-four .footer-about-widget ul li a:hover {
  text-decoration: underline; 
  color: var(--purple-blue);
}
.theme-footer-four .footer-title {
  font-size: 28px;
  color: #0E0E0E;
  position: relative;
  margin: 5px 0 21px;
}
.theme-footer-four .footer-list ul li a {
  font-family: 'Inter', serif;
  color:#343434;
  line-height: 40px;
  transition: all 0.2s ease-in-out;
}
.theme-footer-four .footer-list ul li a:hover {color: var(--purple-blue);text-decoration: underline;}
.theme-footer-four [class*="col-"] {margin-bottom: 35px;}
.theme-footer-four .bottom-footer-content p {
  font-size: 16px;
  color: rgba(0, 0, 0, .65);
  margin-top: -70px;
}
/*----------------- Fancy Text block Twenty -------------------*/
.fancy-text-block-twenty {
  position: relative;
  padding: 215px 0 100px;
  z-index: 5;
}
.fancy-text-block-twenty:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 36%;
  background: url(../images/shape/123.svg) no-repeat center bottom;
  top: 0;
  left: 0;
  z-index: -1;
  background-size: cover;
}
.fancy-text-block-twenty .shape-one {left: 0; top: 19%;}
.fancy-text-block-twenty .shape-two {right: 0; top: 19%;}
.fancy-text-block-twenty .title {
  text-align: center;
  font-size: 72px;
  padding-bottom: 130px;
}
.fancy-video-box-one .main-img {width: 100%;}
.fancy-video-box-one {position: relative;}
.fancy-video-box-one .video-button {
  position: absolute;
  width: 100px;
  height: 100px;
  background: #fff;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50% , -50%);
  z-index: 1;
  padding-left: 8px;
}
.fancy-text-block-twenty .client-info {
  font-size: 24px;
  color: #A09A92;
  padding-bottom: 20px;
}
.fancy-text-block-twenty .client-info span {
  color: #000;
  text-decoration: underline;
}
.fancy-text-block-twenty .nav-tabs {border: none;}
.fancy-text-block-twenty .nav-tabs li {margin: 0 82px 0 0;}
.fancy-text-block-twenty .nav-tabs li:last-child {margin-right: 0;}
.fancy-text-block-twenty .nav-tabs li a {
  font-size: 22px;
  color: #969696;
  padding: 0;
  border: none;
}
.fancy-text-block-twenty .nav-tabs li a.active {
  color: #000;
  text-decoration: underline;
}
.fancy-text-block-twenty .tab-pane p {
  font-size: 20px;
  line-height: 1.9em;
  color: #000;
  padding-top: 35px;
}
/*----------------- Team Section Four --------------*/
.team-section-four {
  background: #DFF1EE;
  position: relative;
  padding: 100px 0 60px;
  z-index: 1;
}
.team-section-four:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 70px;
  background: url(../images/shape/127.svg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -58px;
}
.team-section-four:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 65px;
  background: url(../images/shape/128.svg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -63px;
}
.team-section-four .shape-one {
  right: 0;
  top: 5%;
}
.team-section-four .shape-two {
  left: 0;
  bottom: -3%;
}
.team-section-four .team-wrapper {position: relative; z-index: 5;}
.team-section-four .team-member {
  margin-bottom: 60px; 
  text-align: center;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  padding-bottom: 30px;
  cursor: pointer;
}
.team-section-four .team-member .img-holder {overflow: hidden; border-radius: 10px 10px 0 0; margin-bottom: 30px;}
.team-section-four .team-member .img-holder img {width: 100%; transition: all 0.5s ease-in-out;}
.team-section-four .team-member:hover .img-holder img {transform: scale3d(1.1,1.1,1);}
.team-section-four .team-member .name {font-size: 24px;}
.team-section-four .team-member .position {font-size: 16px; color: rgba(42, 42, 42, .4);}
.team-section-four .team-wrapper .shape-one {top:-6%; left: -5%;}
/*-------------------- Feature Blog Four -----------------*/
.feature-blog-four {
  background: #FDECD8;
  position: relative;
  z-index: 1;
  padding: 100px 0 100px;
}
.feature-blog-four:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 85px;
  background: url(../images/shape/115.svg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -84px;
}
.feature-blog-four:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 33px;
  background: url(../images/shape/116.svg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -32px;
}
.feature-blog-four .feature-article-meta {margin-bottom: 35px;}
.feature-blog-four .feature-article-meta .img-meta {
  position: relative;
  overflow: hidden;
  margin-bottom: 40px;
}
.feature-blog-four .feature-article-meta .img-meta img {transition: all 0.5s ease-in-out; width: 100%;}
.feature-blog-four .feature-article-meta:hover .img-meta img {transform: scale3d(1.1,1.1,1);}
.feature-blog-four .feature-article-meta .post-meta a {
  font-family: 'Inter', serif;
  font-size: 30px;
  line-height: 1.3em;
  transition: all 0.3s ease-in-out;
}
.feature-blog-four .feature-article-meta:hover .post-meta a {color: var(--vin-red);}
.feature-blog-four .feature-article-meta .post-meta .author_info {font-size: 20px; color: var(--heading); margin-top: 25px;}
.feature-blog-four .feature-article-meta .post-meta .author_info span {color: #B7B7B7;}
/*=======================================================================
                              Product landing               
=========================================================================*/
.theme-menu-five .logo {
  position: absolute;
  left: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-five .right-widget {
  position: absolute;
  right: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-five .right-widget .demo-button {
  width: 210px;
  font-size: 17px;
  line-height: 56px;
  border: 2px solid #000;
  text-align: center;
  border-radius: 8px;
  font-weight: 500;
  color: #000;
  position: relative;
  transition: all 0.3s ease-in-out;
}
.theme-menu-five .right-widget .demo-button:hover {
  background: #eef3f9;
  border-color: #eef3f9;
}
.theme-menu-five .right-widget .demo-button img {width: 20px; display: none; margin: 0 auto;}
.theme-menu-five .navbar-nav .nav-item .nav-link {
  font-size: 20px;
  margin: 0 23px;
}
.theme-menu-five .navbar-nav .nav-item .nav-link.active {text-decoration: underline;}

/*---------------------Theme Hero Banner/Five ---------------*/
.hero-banner-five {
  position: relative;
  text-align: center;
  padding: 56px 0 0;
}
.hero-banner-five .hero-heading {
  font-size: 82px;
  line-height:1.219em;
}
.hero-banner-five .hero-heading span {
  position: relative; 
  color: #FF2759;
  text-decoration: underline;
  text-decoration-thickness: 4px;
}
.hero-banner-five .hero-sub-heading {
  font-size: 24px; 
  padding: 20px 0 62px;
  color: #000;
}

.hero-banner-five .sing-in-call {font-size: 16px; padding-top: 20px; color: #979797;}
.hero-banner-five .sing-in-call a {color: #000; transition: all 0.25s ease-in-out;}
.hero-banner-five .sing-in-call a:hover {text-decoration: underline;}
.hero-banner-five .button-group a {
  width: 200px;
  height: 60px;
  padding: 0 5px 0 25px;
  margin: 10px 12px;
  border: 2px solid #111111;
  border-radius: 6px;
  color: #000;
  text-align: left;
  transition: all 0.3s ease-in-out;
}
.hero-banner-five .button-group a:hover {
  transform: translateY(-5px);
  box-shadow: -5px 10px 30px rgba(0, 0, 0, 0.1);
}
.hero-banner-five .button-group a .icon {margin-right: 15px;}
.hero-banner-five .button-group a span {
  font-size: 11px;
  color: #737373;
  display: block;
  margin-bottom: -11px;
  margin-top: -5px;
}
.hero-banner-five .button-group a strong {
  font-weight: 500;
  font-size: 18px;
  display: block;
}
.hero-banner-five .button-group a.ios-button {background: #111111; color: #fff;}
.hero-banner-five .button-group a.ios-button span {color: rgba(255, 255, 255, 0.7);}
.hero-banner-five .img-gallery {
  border-bottom: 1px solid #EAEAEA; 
  margin-top: 80px;
  position: relative;
  z-index: 1;
}
.hero-banner-five .img-gallery .screen-container {
  border: 4px solid #000;
  border-radius: 45px 45px 0 0;
  border-bottom: none;
  position: relative;
}
.hero-banner-five .img-gallery .screen-container .main-screen {
  width: 100%;
  border-radius: 40px 40px 0 0;
}
.hero-banner-five .img-gallery .screen-one {
  width: 19%;
  top: 26%;
  right: -10%;
  z-index: 1;
  box-shadow: 0 35px 70px rgba(12, 21, 44, 0.08);
  animation: jumpTwo 5s infinite linear;
}
.hero-banner-five .img-gallery .screen-two {
  width: 31%;
  bottom: -12%;
  left: -16%;
  z-index: 1;
  box-shadow: -10px 40px 80px rgba(25, 42, 70, 0.05);
  animation: jumpThree 5s infinite linear;
}
.hero-banner-five .shape-one {left: 0;top: 25%;}
.hero-banner-five .shape-two {right: 0;bottom: 9%;}
.hero-banner-five .shape-three {right: 10%;top: 27%;}
.hero-banner-five .shape-four {left: 11%;top: 56%;}
.partner-slider-two p {
  font-size: 20px;
  color: #000;
  padding-bottom: 45px;
}
.partner-slider-two p span {font-weight: 500;}
.partner-slider-two .img-meta {height: 80px;}
.partner-slider-two p.text-lg {
  font-size: 36px;
  line-height: 1.44em;
  font-weight: 500;
}
.partner-slider-two p.text-lg span {color: #FFB840;}
/*------------------ Fancy Feature Ten --------------*/
.fancy-feature-ten {padding-left: 15px; padding-right: 15px;}
.fancy-feature-ten .bg-wrapper {
  max-width: 1600px;
  margin: 0 auto;
  background: #F3F8FF;
  border-radius: 40px;
  padding: 130px 0 150px;
  position: relative;
  z-index: 1;
}
.fancy-feature-ten .shape-one {bottom: -37px;right: 10%;}
.fancy-feature-ten .sub-text {
  font-size: 24px;
  line-height: 1.91em;
  color: #000;
}
.block-style-fifteen {
  background: #fff;
  border-radius: 10px;
  position: relative;
  padding: 45px 35px 30px 40px;
  color: #000;
  z-index: 1;
}
.block-style-fifteen:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border: 2px solid #000;
  z-index: -1;
  border-radius: 10px;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-fifteen:hover:before {opacity: 1;}
.block-style-fifteen .icon {
  width: 65px;
  height: 65px;
  border-radius: 50%;
}
.block-style-fifteen h3 {
  font-weight: 500;
  font-size: 24px;
  text-transform: capitalize;
  padding: 27px 0 22px;
}
/*----------------- Fancy Text block Twenty One -------------------*/
.fancy-text-block-twentyOne .container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 15px;
}
.fancy-text-block-twentyOne .text-wrapper .client-info {
  font-size: 24px;
  color: #A09A92;
  padding: 36px 0 28px;
}
.fancy-text-block-twentyOne .text-wrapper .client-info span {
  color: #000;
  text-decoration: underline;
}
.fancy-text-block-twentyOne .text-wrapper p {
  font-size: 48px;
  color: #000000;
  line-height: 1.59em;
  padding-bottom: 40px;
}
.fancy-text-block-twentyOne .text-wrapper p span {
  color: #FF2759;
  text-decoration: underline;
}
.fancy-text-block-twentyOne .text-wrapper .name {
  font-size: 20px;
  color: #000;
  font-weight: 500;
  font-style: italic;
  position: relative;
  padding-left: 30px;
  margin-top: 32px;
}
.fancy-text-block-twentyOne .text-wrapper .name:before {
  content: '';
  width: 22px;
  height: 2px;
  border-radius: 2px;
  background: #000;
  position: absolute;
  left: 0;
  top:13px;
}
.fancy-text-block-twentyOne .img-meta {position: relative;}
.fancy-text-block-twentyOne .img-meta .shape-one {
  bottom: -123px;
  left: 6px;
}
/*-------------------- Counter Style Two --------------*/
.counter-style-two {padding: 0 15px;}
.counter-style-two .border-bottom {
  max-width: 1600px;
  margin: 0 auto;
  border-bottom: 1px solid #E8E8E8 !important;
  padding-bottom: 120px;
}
.counter-box-four {text-align: center; margin-top: 40px;}
.counter-box-four .number {
  font-family: 'Inter';
  font-size: 60px;
  color: #000;
}
.counter-box-four p {color: #000;}
/*-------------------- Fancy Feature Eleven ----------------*/
.fancy-feature-eleven {position: relative; z-index: 1;}
.fancy-feature-eleven .inner-container {
  max-width: 1630px;
  padding: 0 15px;
  margin: 0 auto;
  position: relative;
}
.fancy-feature-eleven .shape-one {top: 3%; left: 7%;}
.fancy-feature-eleven .shape-two {bottom: -60px; left: 37%; z-index: 1;}
.block-style-sixteen {
  margin-top: 120px;
  position: relative;
  border-radius: 40px;
  padding: 125px 0 120px;
  overflow: hidden;
}
.block-style-sixteen .icon {
  width: 68px;
  height: 68px;
  border-radius: 50%;
}
.block-style-sixteen .title {
  font-weight: 500;
  font-size: 48px;
  line-height: 1.31em;
  padding: 25px 0 40px;
}
.block-style-sixteen p {
  font-size: 24px;
  line-height: 1.91em;
  color: #000;
  padding-bottom: 75px;
}
.block-style-sixteen .screen-one {
  right: 70px;
  width: 48%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.block-style-sixteen .screen-two {
  left: 10px;
  width: 48%;
  bottom: 0;
  z-index: 1;
}
.block-style-sixteen .screen-three {
  right: 0;
  width: 48%;
  bottom: 0;
  z-index: 1;
}
/*----------------- Pricing Section Four ----------------*/
.pricing-nav-three {margin: 60px 0 100px; border:none;}
.pricing-nav-three .nav-item {margin: 0;}
.pricing-nav-three .nav-item .nav-link {
  font-weight: 500;
  font-size: 18px;
  line-height: 61px;
  border: 2px solid #000;
  padding: 0 15px;
  width: 150px;
  text-align: center;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.pricing-nav-three .nav-item:nth-child(1) .nav-link {
  border-right: none;
  border-radius: 10px 0 0 10px;
}
.pricing-nav-three .nav-item:nth-child(2) .nav-link {
  border-left: none;
  border-radius: 0 10px 10px 0;
}
.pricing-nav-three .nav-item .nav-link.active {
  background: #000;
  color: #fff;
}
.pricing-table-area-four {
  max-width: 1350px;
  padding: 0 15px;
  margin: 0 auto;
}
.pricing-table-area-four .row {margin: 0 -30px;}
.pricing-table-area-four .row [class*="col-"] {padding: 0 30px;}
.pricing-table-area-four .pr-table-wrapper {
  border: 1px solid #ECECEC;
  border-radius: 25px;
  transition: all 0.3s ease-in-out;
  padding: 25px 35px 35px;
  margin-top: 40px;
  position: relative;
  z-index: 1;
  text-align: center;
}
.pricing-table-area-four .pr-table-wrapper:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 25px;
  border: 2px solid #FFC107;
  z-index: -1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-four .pr-table-wrapper.most-popular:after {
  content: url(../images/shape/populer-tag.svg);
  position: absolute;
  top: -97px;
  right: -87px;
}
.pricing-table-area-four .pr-table-wrapper .pack-name {
  font-weight: 500;
  font-size: 28px;
  color: #000;
}
.pricing-table-area-four .pr-table-wrapper .pack-details {
  color: #878787;
  padding: 5px 0 45px;
}
.pricing-table-area-four .pr-table-wrapper .top-banner {
  padding: 9px 5px 15px 22px;
  color: #000;
  text-align: left;
}
.pricing-table-area-four .pr-table-wrapper .price {
  font-size:40px;
  font-weight: 500;
  padding: 25px 20px 0 0;
}
.pricing-table-area-four .pr-table-wrapper .price sup {
  font-size: 20px;
  top: -22px;
}
.pricing-table-area-four .pr-table-wrapper .top-banner span  {font-size: 22px;}
.pricing-table-area-four .pr-table-wrapper .top-banner em {
  font-size: 14px;
  font-style: normal;
  display: block;
  margin-top: -7px;
}
.pricing-table-area-four .pr-table-wrapper .pr-feature {
  text-align: left;
  padding: 38px 0 60px 20px;
}
.pricing-table-area-four .pr-table-wrapper .pr-feature li {
  position: relative;
  line-height: 42px;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 30px;
}
.pricing-table-area-four .pr-table-wrapper .pr-feature li:before {
  position: absolute;
  top: 0;
  left: 0;
}
.pricing-table-area-four .pr-table-wrapper .trial-button {
    display: block;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: 500;
    background: #572FF6;
    color: #fff;
    line-height: 52px;
	font-weight: bold;
    border-radius: 20px;
    transition: all 0.3s ease-in-out;
}
.trial-button:hover {
	background: #FFC107 !important;
    color: black !important;
}
.pricing-table-area-four .pr-table-wrapper .trial-text {font-size: 14px; padding-top: 20px; text-decoration: underline;}
.pricing-table-area-four .pr-table-wrapper:hover:before,
.pricing-table-area-four .pr-table-wrapper.active:before {opacity: 1;}
.pricing-table-area-four .pr-table-wrapper .trial-button:hover,
.pricing-table-area-four .pr-table-wrapper.active .trial-button {background: #FF2759; color: #fff;}
/*--------------------- Client Feedback Slider Four ---------------*/
.client-feedback-slider-four {padding: 0 15px;}
.client-feedback-slider-four .shape-one {top: -69px; left: 11%;}
.client-feedback-slider-four .shape-two {bottom: -42px; right: 9%;}
.client-feedback-slider-four .inner-container {
  max-width: 1600px;
  margin: 0 auto;
  background: #E7F2F2;
  border-radius: 40px;
  position: relative;
  padding: 140px 0 90px;
  z-index: 1;
}
.clientSliderFour {
  max-width: 1435px;
  margin: 100px auto 0;
}
.clientSliderFour .item {margin: 0 20px;}
.clientSliderFour .feedback-wrapper {
  background: #fff;
  border-radius: 12px;
  padding: 45px 50px 50px 40px;
}
.clientSliderFour .feedback-wrapper p {
  font-size: 20px;
  line-height: 1.75em;
  color: #000;
  padding: 20px 0 45px;
}
.clientSliderFour .feedback-wrapper .name {
  font-weight: 500;
  font-size: 18px;
}
.clientSliderFour .feedback-wrapper .name span {font-weight: normal;font-size: 16px; color: #C1C1C1;}
.clientSliderFour .feedback-wrapper ul li {
  font-size: 15px;
  color: #FFB043;
  margin-right: 9px;
}
.clientSliderFour .feedback-wrapper ul li [class*="star-o"] {color: #000;}
.clientSliderFour .slick-dots {text-align: center; margin-top: 80px;}
.clientSliderFour .slick-dots li {display: inline-block;}
.clientSliderFour .slick-dots li button {
  text-indent: -50000px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid #000;
  margin: 0 5px;
  transition: all 0.3s ease-in-out;
}
.clientSliderFour .slick-dots li.slick-active button {
  background: #FF2759;
  border-color: #FF2759;
}
/*------------------ Fancy Short Banner Six ----------------*/
.fancy-short-banner-six {position: relative; text-align: center;}
.fancy-short-banner-six .shape-one {left: 0; top: 22%;}
.fancy-short-banner-six p {
  font-size: 24px;
  color: #000;
  padding: 30px 0 75px;
}
/*---------------- Footer Style Five -----------------*/
.theme-footer-five {padding: 0 15px;}
.theme-footer-five .inner-container {
  border: 2px solid #151515;
  border-radius: 28px;
  max-width: 1600px;
  margin: 0 auto;
  padding: 60px 0 15px;
}
.theme-footer-five .inner-container [class*="col-"] {margin-bottom: 40px;}
.theme-footer-five .title {
  font-weight: 500;
  font-size: 20px;
  color: #000;
  margin-bottom: 30px;
  text-align: center;
}
.theme-footer-five .social-icon li a {
  width: 36px;
  height: 36px;
  border: 1px solid #DBDBDB;
  border-radius: 50%;
  line-height: 34px;
  text-align: center;
  color: #000000;
  margin: 0 5px;
  transition: all 0.3s ease-in-out;
}
.theme-footer-five .social-icon li a:hover {
  background: #FF2759;
  border-color: #FF2759; 
  color: #fff;
}
.theme-footer-five .email {color: #000;}
.theme-footer-five .email:hover {text-decoration: underline;}
.theme-footer-five .copyright {text-align: center; font-size: 16px; padding: 35px 0;}
/*---------------- Modal Contact Form One ------------------*/
.modal-backdrop {z-index: 10000;}
.modal {z-index: 10001;}
.modal-contact-popup-one .modal-dialog {
  max-width: 1330px;
  padding: 0 15px;
}
.modal-contact-popup-one .main-body {
  background: #fff;
  border-radius: 30px;
  overflow: hidden;
  position: relative;
  width: 100%;
  border: none;
  -ms-flex-direction: row;
  flex-direction: row;
}
.dark-style .modal-contact-popup-one .main-body {background: #2E2B3A;}
.modal-contact-popup-one .main-body .close {
  position: absolute;
  right: 30px;
  top: 27px;
  z-index: 5;
}
.modal-contact-popup-one .main-body .left-side {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
  background: #FDE3C5;
  text-align: center;
  padding: 50px 0 0;
}
.dark-style .modal-contact-popup-one .main-body .left-side {
  background: linear-gradient(319.09deg, #F2A530 15.78%, #ED5879 48.89%, #B439FF 81.59%);
}
.dark-style .modal-contact-popup-one .main-body .left-side blockquote,
.dark-style .modal-contact-popup-one .main-body .left-side .bio {color: #fff;}
.modal-contact-popup-one .main-body .left-side blockquote {
  font-weight: 500;
  font-size: 32px;
  line-height: 1.31em;
  color: #000;
  padding: 0 15px 15px;
}
.modal-contact-popup-one .main-body .left-side .bio {
  font-size: 20px;
  color: #000;
  display: block;
  padding-bottom: 15px;
}
.modal-contact-popup-one .main-body .left-side .illustration { width: 100%;}
.modal-contact-popup-one .main-body .right-side {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
  padding: 50px 5% 50px 5%;
  position: relative;
  z-index: 1;
}
.modal-contact-popup-one .main-body .right-side .form-title {
  font-size: 40px;
  font-weight: 500;
  color: #000;
  padding-bottom: 30px;
}
.dark-style .modal-contact-popup-one .main-body .right-side .form-title {color: #fff;}
.modal-contact-popup-one .main-body .input-group-meta {position: relative;}
.modal-contact-popup-one .main-body .input-group-meta label {
  font-size: 15px;
  color: rgba(0, 0, 0, 0.6);
  padding-bottom: 8px;
  font-weight: normal;
  display: block;
}
.dark-style .modal-contact-popup-one .main-body .input-group-meta label {color: rgba(255, 255, 255, 0.4);}
.modal-contact-popup-one .main-body .input-group-meta input {
  width: 100%;
  height: 60px;
  border: 2px solid #000;
  border-radius: 6px;
  padding: 0 20px;
  color: #000;
  font-size: 17px;
  background: transparent;
}
.modal-contact-popup-one .main-body .input-group-meta textarea {
  max-width: 100%;
  width: 100%;
  height: 150px;
  border: 2px solid #000;
  border-radius: 6px;
  padding: 10px 20px;
  color: #000;
  font-size: 17px;
  background: transparent;
}
.dark-style .modal-contact-popup-one .main-body .input-group-meta input,
.dark-style .modal-contact-popup-one .main-body .input-group-meta textarea {border-color: #fff; color: #fff;}
.modal-contact-popup-one .main-body .input-group-meta ::-webkit-input-placeholder { /* Edge */color: #000;}
.modal-contact-popup-one .main-body .input-group-meta :-ms-input-placeholder { /* Internet Explorer 10-11 */color: #000;}
.modal-contact-popup-one .main-body .input-group-meta ::placeholder {color: #000;}

.dark-style .modal-contact-popup-one .main-body .input-group-meta ::-webkit-input-placeholder { /* Edge */color: #fff;}
.dark-style .modal-contact-popup-one .main-body .input-group-meta :-ms-input-placeholder { /* Internet Explorer 10-11 */color: #fff;}
.dark-style .modal-contact-popup-one .main-body .input-group-meta ::placeholder {color: #fff;}
.dark-style .modal-contact-popup-one .theme-btn-seven {background: rgba(255, 255, 255, 0.25);}

.dark-style .modal-contact-popup-one .right-side:before {
  content: '';
  position: absolute;
  width: 568px;
  height: 568px;
  border-radius: 50%;
  top: -14%;
  left: 18%;
  background: rgba(89, 156, 255, 0.12);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -1;
}
.dark-style .modal-contact-popup-one .right-side:after {
  content: '';
  position: absolute;
  width: 568px;
  height: 568px;
  border-radius: 50%;
  top:7%;
  left: 7%;
  background: rgba(255, 4, 64, 0.16);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -2;
}
/*=======================================================================
                              Product landing Dark            
=========================================================================*/
/*-------------------- Menu ---------------*/
.dark-style .theme-main-menu {background: transparent;}
.dark-style .navbar .navbar-toggler {background: linear-gradient(90.6deg, #F548A0 2.93%, #F57C35 99.47%);}
.dark-style .theme-main-menu.sticky-menu.fixed {background: #17192b;}
.dark-style .navbar-nav .nav-item .nav-link {color: rgba(255, 255, 255, 0.6);}
.dark-style .theme-menu-five .navbar-nav .nav-item .nav-link.active {color: #fff;}
.dark-style .theme-menu-five .right-widget .demo-button {
  color: #fff;
  border-color: #fff;
}
.dark-style .theme-menu-five .right-widget .demo-button:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90.6deg, #F548A0 2.93%, #F57C35 99.47%);
  border-radius: 8px;
  z-index: -1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.dark-style .theme-menu-five .right-widget .demo-button:hover {
  background: transparent;
  border-color: transparent;
}
.dark-style .theme-menu-five .right-widget .demo-button:hover:before {opacity: 1;}
/*----------------------- Theme Hero Banner / Six ---------------*/
.hero-banner-six {
  position: relative;
  z-index: 1;
  padding: 125px 0;
}
.hero-banner-six .hero-heading {
  font-size: 100px;
  line-height: 115%;
  color: #FFFFFF;
}
.hero-banner-six .hero-heading span {
  color: #FF275B; 
  text-decoration: underline;
  text-decoration-thickness: 5px;
}
.hero-banner-six .hero-sub-heading {
  font-size: 22px;
  padding: 45px 0 90px;
}
.hero-banner-six .button-group .ios-button {
  width: 200px;
  height: 65px;
  padding: 0 5px 0 25px;
  border-radius: 6px;
  color: #fff;
  text-align: left;
  transition: all 0.3s ease-in-out;
  margin-right: 45px;
}
.hero-banner-six .button-group .ios-button .icon {margin-right: 15px;}
.hero-banner-six .button-group .ios-button span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-bottom: -11px;
  margin-top: -5px;
}
.hero-banner-six .button-group .ios-button strong {
  font-weight: 500;
  font-size: 18px;
  display: block;
}
.hero-banner-six .button-group .video-button span {
  font-weight: 500;
  color: #fff;
  font-size: 16px;
  padding-left: 14px;
  transition: all 0.3s ease;
}
.hero-banner-six .button-group .video-button span:hover {color: #FF275B;}
.hero-banner-six .illustration-content {
  position: absolute;
  right: 0;
  top: 52%;
  transform: translateY(-50%);
  padding-right: 2%;
  width: 45%;
  max-width: 861px;
}
.hero-banner-six .illustration-content .main-img {
  animation: rotated 10s infinite linear;
}
.hero-banner-six .illustration-content .screen-one {
  position: absolute;
  width: 83%;
  top: 10%;
  right: 17%;
  z-index: 1;
  animation: jumpTwo 10s infinite linear;
}
.hero-banner-six .illustration-content .screen-two {
  position: absolute;
  width: 35%;
  top: 16%;
  right: 0;
  z-index: 2;
  animation: jumpThree 10s infinite linear;
}
.hero-banner-six .illustration-content .screen-two:before {
  content: '';
  position: absolute;
  top: 0;
  left: 8%;
  width: 89%;
  height: 88%;
  transform: rotate(12deg);
  background: rgba(17, 14, 14, 0.8);
  filter: blur(100px);
  z-index: -1;
}
.hero-banner-six .illustration-content .shape-one {
  top: 4%;
  left: 17%;
  z-index: 1;
  animation: rotated 3s infinite linear;
}
.hero-banner-six .illustration-content .shape-two {
  top: -7%;
  left: 44%;
  z-index: 1;
}
.hero-banner-six .illustration-content .shape-three {
  top: 4%;
  right: 11%;
  z-index: 1;
}
.hero-banner-six .illustration-content .shape-four {
  bottom: -1%;
  left: 29%;
  z-index: 1;
}
.hero-banner-six .illustration-content .shape-five {
  bottom: -3%;
  left: 13%;
  z-index: 1;
}
.hero-banner-six .illustration-content .shape-six {
  bottom: 4%;
  left: 45%;
  z-index: 1;
  width: 18%;
}
.hero-banner-six .illustration-content .oval-one {
  position: absolute;
  width: 875px;
  height: 875px;
  border-radius: 50%;
  left: -34%;
  top: -21%;
  background: rgba(255, 255, 255, 0.1);
  filter: blur(50px);
  opacity: 0.32;
}
.hero-banner-six .illustration-content .oval-two {
  position: absolute;
  width: 868px;
  height: 868px;
  border-radius: 50%;
  top: -38%;
  left: 23%;
  background: rgba(89, 156, 255, 0.12);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -2;
}
.hero-banner-six .illustration-content .oval-three {
  position: absolute;
  width: 868px;
  height: 868px;
  border-radius: 50%;
  top: -5%;
  left: 2%;
  background: rgba(255, 4, 64, 0.2);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -3;
}
/*------------------ Fancy Feature Twelve --------------*/
.fancy-feature-twelve {position: relative; z-index: 1;}
.fancy-feature-twelve .shape-one {
  top: 3%;
  left: 10%;
}
.fancy-feature-twelve .row {margin: 0 -30px;}
.fancy-feature-twelve .row [class*="col-"] {padding: 0 30px;}
.block-style-seventeen {
  padding: 32px 50px 40px 44px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 17px;
  position: relative;
  z-index: 1;
}
.block-style-seventeen:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(111.21deg, #B526A6 -2.4%, #E14E66 46.51%, #E38039 93.12%);
  border-radius: 17px;
  z-index: -1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-seventeen:hover:before {opacity: 1;}
.block-style-seventeen .icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}
.block-style-seventeen h3 {
  font-weight: 500;
  font-size: 24px;
  padding: 26px 0 20px;
}
.block-style-seventeen .static-text {transition: all 0.3s ease-in-out;}
.block-style-seventeen .hover-text {
  font-size: 24px;
  line-height: 1.5em;
  color: #fff;
  position: absolute;
  left: 44px;
  top: 112px;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-seventeen:hover .static-text {opacity: 0;}
.block-style-seventeen:hover .hover-text {opacity: 1;}
.block-style-seventeen .js-tilt-glare {border-radius: 17px;}
/*------------------ Fancy Feature Thirteen --------------*/
.fancy-feature-thirteen {position: relative; z-index: 1;}
.fancy-feature-thirteen:before {
  content: '';
  position: absolute;
  width: 890px;
  height: 890px;
  border-radius: 50%;
  top: -27%;
  left: -9%;
  background: rgba(89, 156, 255, 0.1);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -1;
}
.fancy-feature-thirteen:after {
  content: '';
  position: absolute;
  width: 890px;
  height: 890px;
  border-radius: 50%;
  top: 5%;
  left: -8%;
  background: rgba(255, 4, 64, 0.13);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -2;
}
.fancy-feature-thirteen .carousel-indicators {
  position: relative;
  margin: 0;
  border-top: 5px solid rgba(255, 255, 255, 0.1);
}
.fancy-feature-thirteen .carousel-indicators li {
  width: 30%;
  margin: 0;
  padding: 0;
  height: auto;
  background: transparent;
  opacity: 1;
  border: none;
  text-indent: 0;
  position: relative;
  padding-top: 50px;
}
.fancy-feature-thirteen .carousel-indicators li:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 5px;
  background: #FFD35B;
  left: 0;
  top: -5px;
  z-index: 1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.fancy-feature-thirteen .carousel-indicators li .icon {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  margin: 6px 30px 0 0;
}
.fancy-feature-thirteen .carousel-indicators li .icon img {transition: all 0.3s ease-in-out; opacity: 0.4;}
.fancy-feature-thirteen .carousel-indicators li p {
  font-size: 24px;
  line-height: 1.41em;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease-in-out;
}
.fancy-feature-thirteen .carousel-indicators li.active:before {opacity: 1;}
.fancy-feature-thirteen .carousel-indicators li.active .icon img {opacity: 1; transform: rotate(90deg);}
.fancy-feature-thirteen .carousel-indicators li.active p {color: rgba(255, 255, 255, 1);}
.fancy-feature-thirteen .carousel-inner {
  background: rgba(243, 247, 248, 0.1);
  box-shadow: inset 0px 2px 2px rgba(255, 255, 255, 0.15);
  border-radius: 40px;
  padding: 70px 75px;
  margin-top: 85px;
  position: relative;
  overflow: visible;
}
#productScreenSlider .carousel-item {
  opacity: 0;
  transition-duration: .6s;
  transition-property: opacity;
}
#productScreenSlider .carousel-item.active,
#productScreenSlider .carousel-item-next.carousel-item-left,
#productScreenSlider .carousel-item-prev.carousel-item-right {
  opacity: 1;
}
#productScreenSlider .active.carousel-item-left,
#productScreenSlider .active.carousel-item-right {
  opacity: 0;
}
#productScreenSlider .carousel-item-next,
#productScreenSlider .carousel-item-prev,
#productScreenSlider .carousel-item.active,
#productScreenSlider .active.carousel-item-left,
#productScreenSlider .active.carousel-item-prev {
  transform: translateX(0);
  transform: translate3d(0, 0, 0);
}
.fancy-feature-thirteen .carousel-inner .shape-one {
  top: -57px;
  right: -60px;
  z-index: 1;
}
.fancy-feature-thirteen .carousel-inner .shape-two {
  bottom: -116px;
  left: -137px;
  z-index: 1;
}
/*------------------ Fancy Feature Fourteen --------------*/
.fancy-feature-fourteen .logo-meta {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  height: 170px;
  margin-bottom: 40px;
}
.fancy-feature-fourteen .logo-meta.lg {height: 248px;}
.fancy-feature-fourteen .js-tilt-glare {border-radius: 20px;}
.fancy-feature-fourteen .right-side {position: relative; z-index: 1;}
.fancy-feature-fourteen .right-side .shape-one {
  bottom: -29px;
  right: -31px;
  z-index: 1;
}
.fancy-feature-fourteen .right-side:before {
  content: '';
  position: absolute;
  width: 590px;
  height: 590px;
  border-radius: 50%;
  top: -8%;
  left: -7%;
  background: rgba(89, 156, 255, 0.13);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(75deg);
  z-index: -1;
}
.fancy-feature-fourteen .right-side:after {
  content: '';
  position: absolute;
  width: 590px;
  height: 590px;
  border-radius: 50%;
  top: 16%;
  left: 21%;
  background: rgba(255, 4, 64, 0.15);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(75deg);
  z-index: -2;
}
/*------------------ Fancy Portfolio One --------------*/
.fancy-portfolio-one {position: relative; z-index: 1;}
.fancy-portfolio-one .img-meta {
  border-radius: 20px;
  overflow: hidden;
  margin: 0 20px;
}
.fancy-portfolio-one .img-meta img {margin: 0 auto;}
.portfolio_slider_three {max-width: 1800px; margin: 0 auto;}
.fancy-portfolio-one .slider-arrows>div {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  margin-left: 15px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.fancy-portfolio-one .slider-arrows>div:hover {background: #FF2759;}
.fancy-portfolio-one .slider-arrows {
  position: absolute;
  top: 28px;
  right: 16%;
}
.fancy-portfolio-one:before {
  content: '';
  position: absolute;
  width: 890px;
  height: 890px;
  border-radius: 50%;
  top: -27%;
  left: 1%;
  background: rgba(89, 156, 255, 0.1);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -1;
}
.fancy-portfolio-one:after {
  content: '';
  position: absolute;
  width: 890px;
  height: 890px;
  border-radius: 50%;
  top: 5%;
  left: -8%;
  background: rgba(255, 4, 64, 0.15);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -2;
}
/*-------------- Client Feedback Slider Five ----------------*/
.client-feedback-slider-five {
  position: relative;
  padding: 240px 0;
  z-index: 2;
}
.client-feedback-slider-five:before {
  content: '';
  position: absolute;
  width: 1016px;
  height: 1016px;
  border-radius: 50%;
  top: 0%;
  left: 32%;
  background: rgba(89, 156, 255, 0.12);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(-180deg);
  z-index: -1;
}
.client-feedback-slider-five:after {
  content: '';
  position: absolute;
  width: 1016px;
  height: 1016px;
  border-radius: 50%;
  top:19%;
  left: 15%;
  background: rgba(255, 4, 64, 0.12);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(-180deg);
  z-index: -2;
}
.clientSliderFive {text-align: center;}
.clientSliderFive p {
  font-size: 28px;
  line-height: 1.85em;
  padding-bottom: 84px;
  color: rgba(255, 255, 255, 0.7);
}
.clientSliderFive .name {
  font-size: 24px;
  font-weight: 500;
  color: #fff;
}
.client-feedback-slider-five .slider-arrow li {
  width: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  margin: 0 7px;
  font-size: 26px;
  color: #fff;
  transition: all 0.3s ease-in-out;
  transform: scale(0.6);
}
.client-feedback-slider-five .slider-arrow li:first-child i {transform: rotate(180deg); display: inline-block;}
.client-feedback-slider-five .slider-arrow li:hover {
  opacity: 1;
  transform: scale(1);
}
.client-feedback-slider-five .circle-area {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 46%;
  left: 50%;
  transform: translate(-50% , -50%);
  z-index: -1;
}
.client-feedback-slider-five .circle-area .main-img {margin: 0 auto;}
.client-feedback-slider-five .shape-one {
  top: 10%;
  left: -22%;
}
.client-feedback-slider-five .shape-two {
  top: -26%;
  right: -23%;
}
.client-feedback-slider-five .shape-three {
  top: 97%;
  right: 12%;
}
/*------------------ Pricing Section Five ---------------*/
.pricing-section-five {position: relative; z-index: 1;}
.pricing-section-five:before {
  content: '';
  position: absolute;
  width: 860px;
  height: 860px;
  border-radius: 50%;
  top: -32%;
  left: 9%;
  background: rgba(89, 156, 255, 0.12);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -1;
}
.pricing-section-five:after {
  content: '';
  position: absolute;
  width: 860px;
  height: 860px;
  border-radius: 50%;
  bottom:-19%;
  left: 7%;
  background: rgba(255, 4, 64, 0.15);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -2;
}
.pricing-table-area-five .row {margin: 0 -20px;}
.pricing-table-area-five .row [class*="col-"] {padding: 0 20px;}
.pricing-table-area-five .pr-table-wrapper {
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  position: relative;
  padding: 58px 15px 55px 47px;
}
.pricing-table-area-five .pr-table-wrapper .pack-name {
  font-size: 32px;
  font-weight: 500;
  color: #fff;
  padding: 39px 0 37px;
}
.pricing-table-area-five .pr-table-wrapper .pr-feature li {
  line-height: 42px;
  color: rgba(255, 255, 255, 0.7);
  padding-left: 30px;
  position: relative;
}
.pricing-table-area-five .pr-table-wrapper .pr-feature li:before {
  content: url(../images/icon/87.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.pricing-table-area-five .pr-table-wrapper .price {
  color: #fff;
  font-size: 28px;
  padding: 45px 0 7px;
}
.pricing-table-area-five .pr-table-wrapper .trial-text {
  color: rgba(255, 255, 255, 0.34);
}
.pricing-table-area-five .pr-table-wrapper .trial-button {
  width: 170px;
  line-height: 50px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  margin-top: 35px;
  text-align: center;
}
.pricing-table-area-five .pr-table-wrapper .trial-button:before {border-radius: 24px;}
.pricing-table-area-five .pr-table-wrapper.active {
  background: rgba(255, 255, 255, 0.03);
  border-color: transparent;
}
.pricing-table-area-five .pr-table-wrapper.active .trial-button {background: rgba(255, 255, 255, 0.1);}
.pricing-table-area-five .shape-one {
  z-index: 1;
  right: -47px;
  top: -76px;
  animation: rotated 35s infinite linear;
}
.pricing-table-area-five .shape-two {
  z-index: 1;
  left: -107px;
  bottom: -95px;
  animation: rotated 20s infinite linear;
}
.pricing-table-area-five .pr-table-wrapper .popular-badge {
  top: -90px;
  left: -77px;
  position: absolute;
  z-index: 1;
  animation: jumpTwo 10s infinite linear;
}
.pricing-table-area-five .js-tilt-glare .js-tilt-glare-inner {border-radius: 15px; background-image: none !important;}
/*--------------- Fancy Short Banner Seven --------------*/
.fancy-short-banner-seven {text-align: center; position: relative; z-index: 2;}
.fancy-short-banner-seven .ios-button {
  width: 200px;
  height: 65px;
  padding: 0 5px 0 25px;
  border-radius: 6px;
  color: #fff;
  text-align: left;
  transition: all 0.3s ease-in-out;
  margin: 45px auto 0;
}
.fancy-short-banner-seven .ios-button:after {
  content: url(../images/shape/161.svg);
  position: absolute;
  right: -107px;
  bottom: -28px;
}
.fancy-short-banner-seven .ios-button .icon {margin-right: 15px;}
.fancy-short-banner-seven .ios-button span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-bottom: -11px;
  margin-top: -5px;
}
.fancy-short-banner-seven .ios-button strong {
  font-weight: 500;
  font-size: 18px;
  display: block;
}
.fancy-short-banner-seven .video-button span {
  font-weight: 500;
  color: #fff;
  font-size: 16px;
  padding-left: 14px;
  transition: all 0.3s ease;
}
.fancy-short-banner-seven .screen-meta {position: relative; margin-top: 95px; z-index: 1;}
.fancy-short-banner-seven .screen-meta:before {
  content: '';
  position: absolute;
  background: linear-gradient(180deg, #0F1123 0%, rgba(15, 17, 35, 0.89) 24.08%, rgba(14, 16, 31, 0) 81.75%);
  top: -50px;
  left: 0;
  right: 0;
  bottom: -100px;
  z-index: 2;
}
.fancy-short-banner-seven .shape-one {
  left: 15%;
  top: 36%;
  z-index: 3;
  animation: jumpTwo 3s infinite linear;
}
.fancy-short-banner-seven .shape-two {
  right: 17%;
  top: 36%;
  z-index: 3;
  animation: jumpThree 3s infinite linear;
}
.fancy-short-banner-seven .shape-three {
  left: 29%;
  bottom: -14%;
  z-index: 3;
  animation: jumpTwo 3s infinite linear;
}
.fancy-short-banner-seven .shape-four {
  left: 10%;
  top: -7%;
  z-index: 3;
}
.fancy-short-banner-seven .shape-five {
  right: -6%;
  bottom: -35%;
  z-index: 3;
}
.fancy-short-banner-seven .js-tilt-glare-inner {background: transparent; background-image: none !important;}
/*------------------- Footer Style Six ---------------*/
.theme-footer-six {
  background: url(../images/media/img_77.png) no-repeat center;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin-top: 200px;
}
.theme-footer-six .shape-one {
  left: 9%;
  bottom: -200px;
  animation: jumpThree 10s infinite linear;
}
.theme-footer-six .inner-container {
  background: linear-gradient(180deg, #0F1123 0%, rgba(15, 17, 35, 0.83) 48.7%, #0F1123 100%);
  padding: 100px 0 10px;
  position: relative;
  z-index: 5;
}
.theme-footer-six .inner-container:before {
  content: '';
  position: absolute;
  width: 995px;
  height: 995px;
  border-radius: 50%;
  top: -94%;
  left: 24%;
  background: rgba(89, 156, 255, 0.12);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -1;
}
.theme-footer-six .inner-container:after {
  content: '';
  position: absolute;
  width: 860px;
  height: 860px;
  border-radius: 50%;
  bottom:-153%;
  left: 18%;
  background: rgba(255, 4, 64, 0.15);
  filter: blur(50px);
  opacity: 0.32;
  transform: rotate(150deg);
  z-index: -2;
}
.theme-footer-six .inner-container [class*="col-"] {margin-bottom: 40px;}
.theme-footer-six .title {
  font-weight: 500;
  font-size: 20px;
  color: #fff;
  margin-bottom: 30px;
  text-align: center;
}
.theme-footer-six .social-icon li a {
  width: 36px;
  height: 36px;
  border: 1px solid #fff;
  border-radius: 50%;
  line-height: 34px;
  text-align: center;
  color: #fff;
  margin: 0 5px;
  transition: all 0.3s ease-in-out;
}
.theme-footer-six .social-icon li a:hover {
  background: #FF2759;
  border-color: #FF2759; 
  color: #fff;
}
.theme-footer-six .email {color: rgba(255, 255, 255, 0.8);}
.theme-footer-six .email:hover {text-decoration: underline;}
.theme-footer-six .copyright {text-align: center; font-size: 16px; padding: 45px 0 0;}
/*=======================================================================
                              Product landing / Note Taking            
=========================================================================*/
/*-------------------- Menu ---------------*/
.theme-menu-five .right-widget .signIn-action {
  font-size: 20px;
  font-weight: 500;
  line-height: 46px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.theme-menu-five .right-widget .signIn-action:hover {text-decoration: underline;}
.theme-menu-five .right-widget .signIn-action img {margin-right: 12px; margin-top: -3px;}
.theme-menu-five .right-widget .download-btn {margin-left: 40px;}
.theme-menu-five .right-widget .download-btn button {
  width: 190px;
  line-height: 60px;
  border-radius: 9px;
  font-weight: 500;
  font-size: 18px;
  color: #000;
  background: #FFEAA0;
  padding-left: 12px;
}
.theme-menu-five .right-widget .download-btn.style-two button {
  background: #fff;
  border: 2px solid #000;
  line-height: 56px;
}
.theme-menu-five .right-widget .download-btn button:after {
  content: url(../images/icon/89.svg);
  border: none;
  margin: 0 0 0 11px;
  vertical-align: 0;
  transition: all 0.2s ease-in-out;
}
.theme-menu-five .right-widget .download-btn.show button:after {transform: rotate(180deg);}
.theme-menu-five .right-widget .download-btn .dropdown-menu {
  width: 100%;
  background: #FFF5D1;
  border-radius: 0px 0px 10px 10px;
  border: none;
  margin: -5px 0 0 0;
}
.theme-menu-five .right-widget .download-btn.style-two .dropdown-menu {
  background: #fff;
  border: 2px solid #000;
}
.theme-menu-five .right-widget .download-btn .dropdown-menu a {
  font-weight: 500;
  font-size: 15px;
  color: #000;
  padding: 8px 15px;
  transition: all 0.3s ease-in-out;
}
.theme-menu-five .right-widget .download-btn .dropdown-menu a:hover,
.theme-menu-five .right-widget .download-btn .dropdown-menu a:focus {background: rgba(255, 255, 255, 0.5);}
.theme-menu-five .right-widget .download-btn .dropdown-menu a span {padding-left: 12px;}
.theme-menu-five .right-widget .signup-btn {
  color: #000;
  position: relative;
  margin-left: 38px;
}
.theme-menu-five .right-widget .signup-btn:before {
  content: '';
  position: absolute;
  height: 13px;
  width: 2px;
  background: #000;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
}
.theme-menu-five .right-widget .signup-btn span {font-weight: 500;}
.theme-menu-five .right-widget .signup-btn:hover {text-decoration: underline;}
/*----------------------- Theme Hero Banner / Seven ---------------*/
.hero-banner-seven {
  position: relative;
  z-index: 1;
  margin: 75px 0 0;
}
.hero-banner-seven .illustration-container {
  position: absolute;
  top: 22px;
  right: 70px;
  max-width: 46%;
}
.hero-banner-seven .illustration-container img {animation: jumpTwo 6s infinite linear;}
.hero-banner-seven .hero-heading {
  font-weight: 500;
  font-size: 100px;
  line-height: 1.15em;
  padding-right: 50px;
}
.hero-banner-seven .hero-heading span {position: relative;}
.hero-banner-seven .hero-heading span:before {
  content: '';
  width: 100%;
  height: 15px;
  background: #FFEAA0;
  position: absolute;
  left: 0;
  bottom: 30px;
  z-index: -1;
}
.hero-banner-seven .hero-sub-heading {
  font-size: 24px;
  line-height: 1.5em;
  color: #2F2F2F;
  padding: 36px 0 58px 0;
}
.hero-banner-seven form {
  max-width: 550px;
  height: 80px;
  position: relative;
}
.hero-banner-seven form input {
  border: none;
  background: #F4F4F4;
  border-radius: 7px;
  width: 100%;
  height: 100%;
  font-size: 16px;
  padding: 0 190px 0 30px;
}
.hero-banner-seven form button {
  position: absolute;
  right: 10px;
  top:8px;
  bottom: 8px;
  background: #242424;
  width: 170px;
  border-radius: 7px;
  text-align: center;
  color: #fff;
  font-size: 17px;
  transition: all 0.3s ease-in-out;
}
.hero-banner-seven form button:hover {background: #FFEAA0; color: #212121;}
.hero-banner-seven form ::-webkit-input-placeholder { /* Edge */color: #0B0B0B;}
.hero-banner-seven form :-ms-input-placeholder { /* Internet Explorer 10-11 */color: #0B0B0B;}
.hero-banner-seven form ::placeholder {color: #0B0B0B;}
.hero-banner-seven .term-text {font-size: 16px; color: #979797; margin-top: 22px;}
.hero-banner-seven .download-btn {width: 190px; margin-top: 25px; display: none;}
.hero-banner-seven .download-btn button {
  width: 100%;
  line-height: 60px;
  border-radius: 9px;
  font-weight: 500;
  font-size: 18px;
  color: #000;
  background: #FFEAA0;
  padding-left: 12px;
}
.hero-banner-seven .download-btn button:after {
  content: url(../images/icon/89.svg);
  border: none;
  margin: 0 0 0 11px;
  vertical-align: 0;
  transition: all 0.2s ease-in-out;
}
.hero-banner-seven .download-btn.show button:after {transform: rotate(180deg);}
.hero-banner-seven .download-btn .dropdown-menu {
  width: 100%;
  background: #FFF5D1;
  border-radius: 0px 0px 10px 10px;
  border: none;
  margin: -5px 0 0 0;
}
.hero-banner-seven .download-btn .dropdown-menu a {
  font-weight: 500;
  font-size: 15px;
  color: #000;
  padding: 8px 15px;
  transition: all 0.3s ease-in-out;
}
.hero-banner-seven .download-btn .dropdown-menu a:hover,
.hero-banner-seven .download-btn .dropdown-menu a:focus {background: rgba(255, 255, 255, 0.5);}
.hero-banner-seven .download-btn .dropdown-menu a span {padding-left: 12px;}
/*---------------------- Fancy Feature Fifteen ------------------*/
.fancy-feature-fifteen {padding: 0 15px;}
.fancy-feature-fifteen .bg-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  background: #F6F9FC;
  padding: 70px 0;
  border-radius: 20px;
  position: relative;
  z-index: 1;
}
.fancy-feature-fifteen .bg-wrapper:before {
  content: '';
  position: absolute;
  width: 118px;
  height: 118px;
  border-radius: 50%;
  background: #75F1D5;
  top: -59px;
  right: -48px;
  z-index: 1;
  animation: jumpTwo 6s infinite linear;
}
.fancy-feature-fifteen .bg-wrapper .shape-one {
  left: -33px;
  top: 22%;
  animation: jumpTwo 6s infinite linear;
}
.fancy-feature-fifteen .bg-wrapper .shape-two {
  right: -33px;
  top: 58%;
  animation: jumpThree 6s infinite linear;
}
.fancy-feature-fifteen .carousel-indicators {
  position: relative;
  margin: 0;
}
.fancy-feature-fifteen .carousel-indicators li {
  margin: 0;
  padding: 0;
  height: auto;
  background: transparent;
  opacity: 0.2;
  border: none;
  text-indent: 0;
  position: relative;
  width: auto;
}
.fancy-feature-fifteen .carousel-indicators li:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #000;
  left: 0;
  bottom: -3px;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.fancy-feature-fifteen .carousel-indicators li p {
  font-size: 18px;
  font-weight: 500;
  color: #000;
  padding-left: 10px;
  transition: all 0.3s ease-in-out;
}
.fancy-feature-fifteen .carousel-indicators li.active,
.fancy-feature-fifteen .carousel-indicators li.active:before {opacity: 1;}
#screenSlider .carousel-item {
  opacity: 0;
  transition-duration: .6s;
  transition-property: opacity;
}
#screenSlider .carousel-item.active,
#screenSlider .carousel-item-next.carousel-item-left,
#screenSlider .carousel-item-prev.carousel-item-right {
  opacity: 1;
}
#screenSlider .active.carousel-item-left,
#screenSlider .active.carousel-item-right {
  opacity: 0;
}
#screenSlider .carousel-item-next,
#screenSlider .carousel-item-prev,
#screenSlider .carousel-item.active,
#screenSlider .active.carousel-item-left,
#screenSlider .active.carousel-item-prev {
  transform: translateX(0);
  transform: translate3d(0, 0, 0);
}
/*--------------------- Fancy Feature Sixteen ------------------*/
.block-style-eighteen .text-wrapper h6 {
  font-size: 17px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: rgba(0, 0, 0, 0.3);
  padding-bottom: 15px;
}
.block-style-eighteen .text-wrapper .title {
  font-size: 68px;
  font-weight: 500;
  line-height: 1.05em;
}
.block-style-eighteen .text-wrapper .title span {position: relative; display: inline-block;}
.block-style-eighteen .text-wrapper .title span:before {
  content: '';
  width: 100%;
  height: 13px;
  background: #FFEAA0;
  position: absolute;
  left: 0;
  bottom: 10px;
  z-index: -1;
}
.block-style-eighteen .text-wrapper p {
  font-size: 20px;
  line-height:1.75em;
  color: #4F4F4F;
  padding: 45px 0 40px;
}
.block-style-eighteen .text-wrapper .learn-more {
  font-weight: 500;
  font-size: 16px;
  color: #000;
}
.block-style-eighteen .text-wrapper .learn-more img {margin-left: 5px; transition: all 0.3s ease-in-out;}
.block-style-eighteen .text-wrapper .learn-more:hover img {transform: translateX(3px);}
.block-style-eighteen .screen-holder-one {
  width: 460px;
  height: 470px;
  border-radius: 24px;
  background: #FAF7F0;
  position: relative;
  z-index: 1;
  margin-left: auto;
}
.block-style-eighteen .screen-holder-one .round-bg {
  background: #fff;
  border-radius: 50%;
}
.block-style-eighteen .screen-holder-one .shapes {z-index: 1;}
.block-style-eighteen .screen-holder-one .logo-one {
  top: -33px;
  right: 32%;
  animation: jumpTwo 6s infinite linear;
}
.block-style-eighteen .screen-holder-one .logo-two {
  top: 20%;
  left: -42px;
  animation: jumpThree 6s infinite linear;
}
.block-style-eighteen .screen-holder-one .logo-three {
  top: 74%;
  left: -37px;
  animation: jumpTwo 6s infinite linear;
}
.block-style-eighteen .screen-holder-one .logo-four {
  bottom: -54px;
  left: 52%;
  animation: jumpThree 6s infinite linear;
}
.block-style-eighteen .screen-holder-one .logo-five {
  top: 9%;
  right: -43px;
  animation: jumpThree 6s infinite linear;
}
.block-style-eighteen .screen-holder-one .shape-one {
  top: 42%;
  right: -43px;
}
.block-style-eighteen .screen-holder-two {position: relative; display: inline-block;}
.block-style-eighteen .screen-holder-two .screen-one {
  z-index: 1;
  bottom: -9%;
  right: -22%;
  width: 44.8%;
  animation: jumpThree 5s infinite linear;
}
.block-style-eighteen .screen-holder-three {
  width: 530px;
  height: 513px;
  background: #F0F6FB;
  position: relative; 
  display: inline-block;
  border-radius: 25px;
  margin-left: auto;
}
.block-style-eighteen .screen-holder-three img {animation: jumpTwo 5s infinite linear;}
/*----------------- Fancy Text block Twenty Two -------------------*/
.fancy-text-block-twentyTwo .text-wrapper {padding-left: 40px;}
.fancy-text-block-twentyTwo .text-wrapper p {
  font-size: 20px;
  line-height: 1.75em;
  color: #292929;
  padding: 40px 0 67px;
}
.fancy-text-block-twentyTwo .illustration-holder img {animation: jumpTwo 6s infinite linear;}
/*----------------- Fancy Feature Seventeen -------------------*/
.fancy-feature-seventeen {padding: 0 15px;}
.fancy-feature-seventeen .bg-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  background: #FCF7EA;
  border-radius: 25px;
  padding: 90px 0 40px;
}
.fancy-feature-seventeen .block-meta {
  margin-bottom: 70px; 
  text-align: center;
  padding: 0 30px;
}
.fancy-feature-seventeen .block-meta .icon {height: 35px;}
.fancy-feature-seventeen .block-meta .icon img {margin: 0 auto;}
.fancy-feature-seventeen .block-meta h4 {
  font-size: 24px;
  font-weight: 500;
  padding: 28px 0 14px;
}
.fancy-feature-seventeen .block-meta p {
  line-height: 32px;
  color: #292929;
}
/*------------------- Pricing Section Six ------------------*/
.pricing-nav-four {
  margin: 70px auto 95px; 
  border:2px solid #000;
  padding: 6px;
  border-radius: 10px;
  width: 310px;
}
.pricing-nav-four .nav-item {margin: 0;}
.pricing-nav-four .nav-item .nav-link {
  font-weight: 500;
  font-size: 18px;
  line-height: 60px;
  border: none;
  border-radius: 10px;
  padding: 0 15px;
  width: 147px;
  text-align: center;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.pricing-nav-four .nav-item .nav-link.active {
  background: #000;
  color: #fff;
}
.pricing-table-area-six {
  max-width: 1335px;
  padding: 0 15px;
  margin: 0 auto;
}
.pricing-table-area-six .row {margin: 0 -30px;}
.pricing-table-area-six .row [class*="col-"] {padding: 0 30px;}
.pricing-table-area-six .pr-table-wrapper {
  border: 1px solid #ECECEC;
  border-radius: 15px;
  transition: all 0.3s ease-in-out;
  padding: 35px 35px 35px;
  margin-top: 40px;
  position: relative;
  z-index: 1;
  text-align: center;
}
.pricing-table-area-six .pr-table-wrapper:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 15px;
  border: 2px solid #000;
  z-index: -1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-six .pr-table-wrapper .pack-name {
  font-weight: 500;
  font-size: 24px;
  line-height: 35px;
  padding: 0 13px;
  display: inline-block;
  color: #000;
}
.pricing-table-area-six .pr-table-wrapper .pack-details {
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #000;
  border-bottom: 1px solid #E0E0E0;
  padding: 0 0 27px;
}
.pricing-table-area-six .pr-table-wrapper .price {
  font-size:58px;
  font-weight: 500;
  color: #000;
  line-height: initial;
  padding: 13px 0 6px;
}
.pricing-table-area-six .pr-table-wrapper .pr-feature {
  text-align: left;
  padding: 28px 0 42px 8px;
}
.pricing-table-area-six .pr-table-wrapper .pr-feature li {
  position: relative;
  line-height: 42px;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 30px;
}
.pricing-table-area-six .pr-table-wrapper .pr-feature li:before {
  content: url(../images/icon/76.svg);
  position: absolute;
  top: 0;
  left: 0;
}
.pricing-table-area-six .pr-table-wrapper .trial-button {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #000;
  line-height: 52px;
  border: 2px solid #000;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-six .pr-table-wrapper .trial-text {font-size: 16px; padding-top: 20px;}
.pricing-table-area-six .pr-table-wrapper:hover:before,
.pricing-table-area-six .pr-table-wrapper.active:before {opacity: 1;}
.pricing-table-area-six .pr-table-wrapper .trial-button:hover,
.pricing-table-area-six .pr-table-wrapper.active .trial-button {background: #FFEAA0; border-color: #FFEAA0;}
/*-------------- Client Feedback Slider Six ----------------*/
.clientSliderSix {
  max-width: 1740px;
  margin: 30px auto 0;
}
.clientSliderSix .item {margin: 80px 25px;}
.clientSliderSix .feedback-wrapper {
  border: 1px solid #E8EDF0;
  border-radius: 10px;
  background: #fff;
  padding: 42px 44px 48px 57px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.clientSliderSix .feedback-wrapper .ribbon {
  height: 8px;
  position: absolute;
  left: -1px;
  right: -1px;
  top: -1px;
}
.clientSliderSix .slick-center .feedback-wrapper {
  border-color: #fff;
  box-shadow: 0px 35px 60px rgba(14, 29, 45, 0.04);
}
.clientSliderSix .feedback-wrapper:before {
  content: url(../images/icon/101.svg);
  position: absolute;
  bottom: 41px;
  right: 57px;
}
.clientSliderSix .feedback-wrapper p {
  font-size: 20px;
  line-height: 38px;
  color: #000;
  padding-bottom: 45px;
}
.clientSliderSix .feedback-wrapper .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 15px;
}
.clientSliderSix .feedback-wrapper .name {
  font-weight: 500;
  font-size: 18px;
}
.clientSliderSix .feedback-wrapper .name span {font-weight: normal;font-size: 16px; color: #C1C1C1;}
.clientSliderSix .slick-dots {text-align: center;}
.clientSliderSix .slick-dots li {display: inline-block;}
.clientSliderSix .slick-dots li button {
  text-indent: -50000px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid #000;
  margin: 0 5px;
  transition: all 0.3s ease-in-out;
}
.clientSliderSix .slick-dots li.slick-active button {
  background: #000;
  border-color: #000;
}
.clientSliderSix.style-two .feedback-wrapper .ribbon {
  top: auto;
  bottom: -1px;
  height: 5px;
  transform: scale(0 , 1);
  border-radius:0 0 10px 10px;
  transition: all 0.3s ease-in-out;
}
.clientSliderSix.style-two .slick-center .feedback-wrapper .ribbon {transform: scale(1 , 1);}
/*----------------- Fancy Short Banner Eight ---------------*/
.fancy-short-banner-eight {
  background: #FCF7EA;
  padding: 75px 0 100px;
  position: relative;
  z-index: 1;
}
.fancy-short-banner-eight .download-btn {
  width: 280px;
  margin: 80px auto 0;
}
.fancy-short-banner-eight .download-btn button {
  width: 100%;
  line-height: 70px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 18px;
  color: #fff;
  background: #000;
  text-align: center;
}
.fancy-short-banner-eight .download-btn button:after {
  content: url(../images/icon/102.svg);
  border: none;
  position: absolute;
  right: 22px;
  vertical-align: 0;
  transition: all 0.2s ease-in-out;
}
.fancy-short-banner-eight .download-btn.show button:after {transform: rotate(180deg);}
.fancy-short-banner-eight .download-btn .dropdown-menu {
  width: 100%;
  background: #FFF5D1;
  border-radius: 0px 0px 10px 10px;
  border: none;
  margin: -5px 0 0 0;
  padding: 5px 0 0px;
}
.fancy-short-banner-eight .download-btn .dropdown-menu a {
  font-weight: 500;
  font-size: 15px;
  color: #000;
  padding: 8px 15px;
  background: transparent;
}
.fancy-short-banner-eight .download-btn .dropdown-menu a:hover,
.fancy-short-banner-eight .download-btn .dropdown-menu a:focus {background: rgba(255, 255, 255, 0.5);}
.fancy-short-banner-eight .download-btn .dropdown-menu a span {padding-left: 12px;}
.fancy-short-banner-eight .shape-one {
  top: 18%;
  left: 10%;
  animation: jumpThree 4s infinite linear;
}
.fancy-short-banner-eight .shape-two {
  top: 54%;
  right: 9%;
  animation: jumpTwo 4s infinite linear;
}
/*-------------- Footer Style Seven -------------*/
.theme-footer-seven {font-family: 'Inter';}
.theme-footer-seven .title {
  font-size: 16px;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.8);
  letter-spacing: 3px;
  padding-bottom: 25px;
}
.theme-footer-seven .footer-list a {
  line-height: 40px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.theme-footer-seven .footer-list a:hover {text-decoration: underline;}
.theme-footer-seven .newsletter p {font-size: 19px; color: #000;padding: 10px 0 40px;}
.theme-footer-seven .newsletter p span {text-decoration: underline;}
.theme-footer-seven .newsletter form {
  height: 70px;
  position: relative;
  margin-bottom: 12px;
}
.theme-footer-seven .newsletter form input {
  width: 100%;
  height: 100%;
  border: 2px solid #000;
  border-radius: 8px;
  padding: 0 125px 0 20px;
  font-size: 16px;
  color: #000;
}
.theme-footer-seven .newsletter form ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #000;
  opacity: 1; /* Firefox */
}
.theme-footer-seven .newsletter form :-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: #000;
}
.theme-footer-seven .newsletter form ::-ms-input-placeholder { /* Microsoft Edge */
  color: #000;
}
.theme-footer-seven .newsletter form button {
  position: absolute;
  font-size: 16px;
  font-weight: 500;
  top: 8px;
  right: 8px;
  bottom: 8px;
  width: 115px;
  border-radius: 8px;
  background: #FFEAA0;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.theme-footer-seven .newsletter form button.dark-btn {background: #262626; color: #fff;}
.theme-footer-seven .newsletter form button.dark-btn:hover {background: #EB5E2A;}
.theme-footer-seven .newsletter .info {
  font-size: 14px;
  color: #979797;
}
.theme-footer-seven .bottom-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 35px 0 15px;
  margin-top: 45px;
}
.theme-footer-seven .bottom-footer .footer-nav a {
  font-size: 16px;
  color: #000;
  margin-right: 25px;
}
.theme-footer-seven .bottom-footer .footer-nav a:hover {text-decoration: underline;}
.theme-footer-seven .bottom-footer .copyright {
  font-size: 16px;
  color: #373737;
}
.theme-footer-seven .bottom-footer .social-icon a {
  font-size: 20px;
  margin-left: 15px;
  color: #000;
}
/*---- Dark Style ----*/
.theme-footer-seven.dark-bg {background: #262626; color: #fff;}
.theme-footer-seven.dark-bg .title {color: rgba(255, 255, 255, 0.4);}
.theme-footer-seven.dark-bg .footer-list a,
.theme-footer-seven.dark-bg .newsletter p,
.theme-footer-seven.dark-bg .bottom-footer .footer-nav a,
.theme-footer-seven.dark-bg .bottom-footer .copyright,
.theme-footer-seven.dark-bg .bottom-footer .social-icon a {color: #fff;}
.theme-footer-seven.dark-bg .bottom-footer {border-top: 1px solid rgba(255, 255, 255, 0.08);}
/*=======================================================================
                    Product landing / Online Video Editor           
=========================================================================*/
/*---------------- Theme Hero Banner ---------------*/
.hero-banner-eight {
  position: relative; 
  z-index: 5;
  text-align: center;
  margin-top: 62px;
}
.hero-banner-eight .hero-heading {
  font-size: 90px;
  font-weight: 500;
  line-height: 1.11em;
}
.hero-banner-eight .hero-sub-heading {
  font-size: 23px;
  color: #000;
  padding: 35px 0 55px;
}
.hero-banner-eight .video-wrapper {
  max-width: 1050px;
  height: 600px;
  border-radius: 30px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.04);
  margin: 75px auto 0;
}
.hero-banner-eight .video-wrapper video {
  display: block;
}
.hero-banner-eight .shape-right {
  top: -1%;
  right: 0;
  width: 25.5%;
  animation: fade-in 1.5s infinite linear;
}
.hero-banner-eight .shape-left {
  top: 0;
  left: 0;
  width: 29.4%;
  animation: fade-in-rev 1.5s infinite linear;
}
/*----------------- Fancy Feature Eighteen -------------------*/
.fancy-feature-eighteen {
  background: #F3F8FF;
  padding: 120px 15px 150px;
  position: relative;
  z-index: 1;
}
.fancy-feature-eighteen .nav-tabs {margin: 90px 0 70px; border: none;}
.fancy-feature-eighteen .nav-item .nav-link {
  position: relative;
  width: 90px;
  height: 90px;
  border-radius: 15px;
  padding: 10px;
  border: none;
  background: transparent;
  margin: 10px 22px;
}
.fancy-feature-eighteen .nav-item .hover {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: scale(0) translate(-50% , -50%);
}
.fancy-feature-eighteen .nav-item .active .current {transform: scale(0);}
.fancy-feature-eighteen .nav-item .active .hover {transform: scale(1) translate(-50% , -50%);}
.fancy-feature-eighteen .nav-item .nav-link.active {
  background: #fff;
  box-shadow: 0 15px 30px rgba(9, 16, 29, 0.03);
}
.fancy-feature-eighteen .tab-content {
  max-width: 1450px;
  margin: 0 auto;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 40px 100px rgba(14, 33, 44, 0.05);
  overflow: hidden;
}
.fancy-feature-eighteen .tab-content .text-wrapper {padding: 40px 80px;}
.fancy-feature-eighteen .tab-content .text-wrapper h4 {
  font-size: 36px;
  font-weight: 700;
  padding-bottom: 40px;
}
.fancy-feature-eighteen .tab-content .text-wrapper p {
  font-size: 24px;
  line-height: 1.83em;
  color: #000;
}
.fancy-feature-eighteen .shape-right {
  bottom: 4%;
  right: 0;
  width: 13.35%;
  animation: fade-in 1.5s infinite linear;
}
.fancy-feature-eighteen .shape-left {
  bottom: 8%;
  left: 0;
  width: 13%;
  animation: fade-in-rev 1.5s infinite linear;
}
.fancy-feature-eighteen .tab-content .img-holder {position: relative;}
.fancy-feature-eighteen .tab-content .img-holder .ripple-box {
  width: 385px;
  height: 160px;
  border: 3px solid #fff;
  color: #fff;
  font-size: 28px;
  font-weight: 500;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50% , -50%);
}
.fancy-feature-eighteen .tab-content .img-holder .ripple-box:before {
  content: '';
  position: absolute;
  width: 3px;
  height: 40px;
  background: #fff;
  top: -40px;
  left: -3px;
}
.fancy-feature-eighteen .tab-content .img-holder .ripple-box:after {
  content: '';
  position: absolute;
  width: 40px;
  height: 3px;
  background: #fff;
  top: -3px;
  left: -40px;
}
/*----------------- Fancy Feature Nineteen -------------------*/
.block-style-nineteen {
  background: #fff;
  border-radius: 10px;
  padding: 37px 50px;
  margin-top: 35px;
  border-left-width: 5px;
  border-left-style: solid;
  box-shadow: 0 20px 60px rgba(10, 30, 81, 0.04);
}
.block-style-nineteen .text {padding-left: 30px;}
.block-style-nineteen .text h4 {
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 17px;
}
.block-style-nineteen .text p {
  font-size: 16px;
  line-height: 1.875em;
}
/*----------------- Fancy Feature Twenty -------------------*/
.block-style-twenty .text-wrapper h6 {
  font-size: 17px;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding-bottom: 35px;
  color: rgba(0, 0, 0, 0.3);
}
.block-style-twenty .text-wrapper .title {
  font-size: 60px;
  line-height: 1.2em;
  font-weight: 700;
}
.block-style-twenty .text-wrapper p {
  font-size: 20px;
  line-height: 1.8em;
  color: #4F4F4F;
  padding: 44px 0 44px;
}
.block-style-twenty .text-wrapper .video-button {
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  color: #6B6DF6;
}
.block-style-twenty .text-wrapper .video-button .fa {
  display: inline-block;
  font-size: 26px;
  margin-right: 15px;
}
.block-style-twenty .text-wrapper .video-button:hover span {text-decoration: underline;}
/*----------------- Fancy Feature Twenty One -------------------*/
.fancy-feature-twentyOne {
  background: #FCF6EE;
  padding: 140px 0 110px;
}
.block-style-twentyOne {
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  margin-top: 35px;
  transition: all 0.3s ease-in-out;
}
.block-style-twentyOne:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(147, 134, 118, 0.2);
}
.block-style-twentyOne .video-button {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 15px;
  color: #fff;
  font-size: 24px;
  padding: 15px 20px;
  text-align: center;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-twentyOne .video-button .icon {
  width: 38px;
  height: 38px;
  background: #fff;
  border-radius: 50%;
  margin-top: 15px;
}
.block-style-twentyOne:hover .video-button {opacity: 1;}
/*----------------- Fancy Short Banner Nine ---------------*/
.fancy-short-banner-nine {
  background: #4B0E55;
  padding: 110px 0;
  position: relative;
  z-index: 1;
}
.fancy-short-banner-nine .title-style-seven h2,
.fancy-short-banner-nine .title-style-seven p {color: #fff;}
.fancy-short-banner-nine .download-btn {
  width: 250px;
  margin: 50px auto 0;
}
.fancy-short-banner-nine .download-btn button {
  width: 100%;
  line-height: 65px;
  border-radius: 40px;
  font-weight: 500;
  font-size: 18px;
  color: #000;
  background: #fff;
  text-align: center;
}
.fancy-short-banner-nine .download-btn button:after {
  content: url(../images/icon/89.svg);
  border: none;
  position: absolute;
  right: 30px;
  vertical-align: 0;
  transition: all 0.2s ease-in-out;
}
.fancy-short-banner-nine .download-btn.show button:after {transform: rotate(180deg);}
.fancy-short-banner-nine .download-btn .dropdown-menu {
  width: 100%;
  background: #fff;
  border-radius: 0px 0px 10px 10px;
  border: none;
  margin: -5px 0 0 0;
  padding: 5px 0 0px;
}
.fancy-short-banner-nine .download-btn .dropdown-menu a {
  font-weight: 500;
  font-size: 15px;
  color: #000;
  padding: 8px 15px;
  background: transparent;
}

.fancy-short-banner-nine .download-btn .dropdown-menu a span {padding-left: 12px;}
.fancy-short-banner-nine .shape-one {
  bottom: 0;
  right: 0;
  width: 18%;
}
.fancy-short-banner-nine .shape-two {
  bottom: 0;
  left: 0;
  width: 17.4%
}
/*----------------- Coming Soon ---------------*/
.full-height-layout {
  min-height: 100vh;
  padding: 40px 15px;
  position: relative;
  z-index: 5;
}
.coming-soon-content {max-width: 860px; margin: 0 auto; text-align: center;}
.coming-soon-content h6 {
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 6px;
  color: rgba(0, 0, 0, 0.43);
}
.coming-soon-content h1 {
  font-size: 72px;
  line-height: 1.18em;
  font-weight: 700;
  color: #000;
  padding: 35px 0 60px;
}
.coming-soon-content p {
  font-size: 24px;
  line-height: 1.79em;
  color: #000;
  padding-bottom: 55px;
}
.coming-soon-content form {
  max-width: 670px;
  height: 75px;
  position: relative;
  margin: 0 auto;
}
.coming-soon-content form input {
  display: block;
  width: 100%;
  border: 2px solid #000;
  border-radius: 10px;
  padding: 0 220px 0 20px;
  height: 100%;
}
.coming-soon-content form button {
  width: 200px;
  background: #151515;
  color: #fff;
  font-weight: 500;
  border-radius: 10px;
  position: absolute;
  top: 6px;
  right: 6px;
  bottom: 6px;
  transition: all 0.3s ease-in-out;
}
.coming-soon-content form button:hover {background: var(--blue-dark);}
.coming-soon-content .social-icon a {
  width: 34px;
  height: 34px;
  line-height: 32px;
  text-align: center;
  border: 1px solid #E7E7E7;
  border-radius: 7px;
  color: #333333;
  margin: 0 5px;
  transition: all 0.3s ease-in-out;
}
.coming-soon-content .social-icon a:hover {background: #232323;color: #fff;}
.coming-soon-content .social-icon {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 40px;
}
.coming-soon-content .shape-one {
  left: 0;
  top: 5%;
  animation: fade-in-rev 1.5s infinite linear;
}
.coming-soon-content .shape-two {
  left: 20%;
  top: 9%;
  animation: jumpThree 3.5s infinite linear;
}
.coming-soon-content .shape-three {
  left: 11%;
  top: 35%;
  animation: jumpTwo 3.5s infinite linear;
}
.coming-soon-content .shape-four {
  left: 10%;
  top: 70%;
  animation: fade-in 1.5s infinite linear;
}
.coming-soon-content .shape-five {
  left: 22%;
  top: 90%;
  animation: jumpThree 3.5s infinite linear;
}
.coming-soon-content .shape-six {
  right: 22%;
  top: 9%;
  animation: jumpThree 3.5s infinite linear;
}
.coming-soon-content .shape-seven {
  right: 12%;
  top: 28%;
  animation: fade-in-rev 1.5s infinite linear;
}
.coming-soon-content .shape-eight {
  right: 12%;
  top: 60%;
  animation: jumpTwo 3.5s infinite linear;
}
.coming-soon-content .shape-nine {
  right: 25%;
  top: 85%;
  animation: jumpThree 3.5s infinite linear;
}
.coming-soon-content .shape-ten {
  right: 5%;
  bottom: 0;
  animation: fade-in 1.5s infinite linear;
}
/*=======================================================================
                              Product landing / Schedule Appointment           
=========================================================================*/
/*-------------------- Menu ---------------*/
.theme-menu-six .navbar-nav .nav-item .nav-link {
  margin: 0 25px;
  color: #fff;
}
.theme-menu-six .navbar-nav .nav-item .nav-link.active { color: #a9b1e1 !important; font-weight: bold;}
.theme-menu-six .right-widget .signIn-action {
  line-height: 46px;
  color: #fff;
  margin: 0 40px 0 10px;
  transition: all 0.3s ease-in-out;
}
.theme-menu-six .right-widget .signIn-action:hover {text-decoration: underline;}
.theme-menu-six .right-widget .signIn-action img {margin-right: 12px; margin-top: -3px; filter: invert(70%) !important; width: 18px !important;}
.theme-menu-six .right-widget .signup-btn {
  width: 120px;
  line-height: 40px;
  background: #fff;
  border-radius: 15px;
  font-weight: bold;
  color: #000;
  text-align: center;
  transition: all 0.3s ease-in-out;
}
a.signIn-action.d-flex.align-items-center {
    font-weight: bold;
}
.theme-menu-six .right-widget .signup-btn:hover {background: #C751FF; color: #fff;}
.theme-menu-six.fixed .navbar-nav .nav-item .nav-link,
.theme-menu-six.fixed .right-widget .signIn-action {color: #000;}
.theme-menu-six.fixed .right-widget .signup-btn {background: #572FF6; color: #fff;}
.theme-menu-six .right-widget .demo-button {
  width: 200px;
  font-size: 17px;
  line-height: 55px;
  border: 2px solid #fff;
  text-align: center;
  border-radius: 8px;
  font-weight: 500;
  color: #fff;
  position: relative;
  transition: all 0.3s ease-in-out;
}
.theme-menu-six .right-widget .demo-button:hover {background: #fff;color: #000;}
.theme-menu-six.fixed .right-widget .demo-button {background: #212121;color: #fff;}
/*---------------------Theme Hero Banner/Nine ---------------*/
.hero-banner-nine {position: relative; z-index: 1; padding-top: 270px;}
.hero-banner-nine .hero-heading {
    font-weight: 700;
    font-size: 68px;
    line-height: 1em;
    letter-spacing: -3px;
}
.hero-banner-nine .hero-heading span {display: inline-block; position: relative;}
.hero-banner-nine .hero-heading span img {
  position: absolute;
  max-height: 100%;
  z-index: -1;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.hero-banner-nine .hero-sub-heading {
  font-size: 24px;
  line-height: 1.5em;
  color: #2F2F2F;
  padding: 36px 0 58px 0;
}
.hero-banner-nine form {
  max-width: 550px;
  height: 70px;
  position: relative;
}
.hero-banner-nine form input {
  border: none;
  background: #F4F4F4;
  border-radius: 54px;
  width: calc(100% - 100px);
  height: 100%;
  font-size: 16px;
  padding: 0 50px;
}
.hero-banner-nine form button {
  position: absolute;
  right: 0;
  top:0;
  background: #572FF6;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  text-align: center;
  color: #fff;
  font-size: 17px;
  transition: all 0.3s ease-in-out;
}
.hero-banner-nine form button:hover {background: #FF4A8B;}
.hero-banner-nine form ::-webkit-input-placeholder { /* Edge */color: #0B0B0B;}
.hero-banner-nine form :-ms-input-placeholder { /* Internet Explorer 10-11 */color: #0B0B0B;}
.hero-banner-nine form ::placeholder {color: #0B0B0B;}
.hero-banner-nine .term-text {font-size: 14px; color: #A5A5A5; margin-top: 22px; line-height: 1em;}
.hero-banner-nine .term-text a {color:#572FF6; }
.hero-banner-nine .bg-shape {top: 0;right: 0;}
.hero-banner-nine .illustration-container {
  position: absolute;
  right: 0;
  top: 22%;
  z-index: 1;
  width: 44%;
}
.hero-banner-nine .illustration-container:before {
  content: '';
  position: absolute;
  top: 0;
  left: 70px;
  right: -30px;
  bottom: 50px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  z-index: -1;
}
.hero-banner-nine .illustration-container:after {
  content: '';
  position: absolute;
  top: -25px;
  left: 100px;
  right: -30px;
  bottom: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  z-index: -2;
}
/*----------------- Fancy Feature Twenty Two -------------------*/
.block-style-twentyTwo {
  text-align: center;
  background: #fff;
  padding: 50px 35px;
  margin-top: 45px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.block-style-twentyTwo:hover {box-shadow: 0px 30px 80px rgba(0, 57, 110, 0.05); transform: translateY(-5px);}
.block-style-twentyTwo .icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin: 0 auto;
}
.block-style-twentyTwo h4 {
  font-size: 24px;
  font-weight: 500;
  padding: 26px 0;
}
.block-style-twentyTwo .arrow-icon {
  margin-top: 30px;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.block-style-twentyTwo:hover .arrow-icon {opacity: 1;}
/*----------------- Fancy Feature Twenty Three -------------------*/
.block-style-twentyThree {margin-bottom: 190px;}
.block-style-twentyThree .text-wrapper h6 {
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #ADB0B7;
}
.block-style-twentyThree .text-wrapper .title {
  font-size: 42px;
  line-height: 1.30em;
  font-weight: 700;
  padding: 28px 0 24px;
}
.block-style-twentyThree .text-wrapper p {
  font-size: 20px;
  line-height: 1.85em;
}
.block-style-twentyThree .screen-container {
  position: relative; 
  max-width: 508px;
  height: 415px;
}
.block-style-twentyThree .screen-container .oval-shape {
  width: 415px;
  height: 415px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}
.block-style-twentyThree .screen-container .oval-shape:nth-child(2) {
  left: auto;
  right: 0;
  z-index: -1;
}
.block-style-twentyThree .screen-container .oval-shape:nth-child(1) {
  mix-blend-mode: multiply;
  transform: rotate(30deg);
}
.block-style-twentyThree .screen-container .shape-one {
  z-index: 2;
  left: -15%;
  bottom: -18px;
  animation: jumpTwo 5s infinite linear;
}
.block-style-twentyThree .screen-container .shape-two {
  z-index: 2;
  right: -17%;
  bottom: -28px;
  animation: jumpThree 5s infinite linear;
}
.block-style-twentyThree .screen-container .shape-three {
  z-index: 2;
  left: -25%;
  bottom: -21%;
  max-width: 583px;
  animation: jumpTwo 5s infinite linear;
}
/*----------------- Fancy Feature Twenty Four -------------------*/
.fancy-feature-twentyFour .bg-wrapper {
  background: #F5FBFF;
  position: relative;
  z-index: 1;
  padding: 170px 0 130px;
}
.fancy-feature-twentyFour .click-scroll-button {
  position: absolute;
  width: 52px;
  height: 52px;
  border-radius: 50%;
  background: #fff;
  z-index: 1;
  top: -25px;
  left: 50%;
  margin-left: -26px;
}
.fancy-feature-twentyFour .click-scroll-button img {width: 20px;}
.fancy-feature-twentyFour .shape-one {
  top: -25px;
  right: 25%;
  animation: rotated 12s infinite linear;
}
.fancy-feature-twentyFour .shape-two {
  top: 47%;
  right: 8%;
  animation: rotated 12s infinite linear;
}
.fancy-feature-twentyFour .shape-three {
  bottom: -20px;
  right: 13%;
  animation: jumpTwo 3s infinite linear;
}
.fancy-feature-twentyFour .shape-four {
  bottom: 31%;
  left: 10%;
  animation: rotated 12s infinite linear;
}
.fancy-feature-twentyFour .shape-five {
  top: 13%;
  left: 10%;
  animation: jumpTwo 3s infinite linear;
}
.block-style-twentyFour {
  background: #fff;
  padding: 40px 43px 50px;
  box-shadow: 0 4px 0 #E9F3FA;
  border-radius: 10px;
  width: 100%;
  height: 100%;
}
.block-style-twentyFour .icon {
  width: 70px;
  height: 70px;
  border-radius: 20px;
}
.block-style-twentyFour .text {
  width: calc(100% - 70px);
  padding-left: 32px;
}
.block-style-twentyFour .text h4 {
  font-size: 22px;
  font-weight: 500;
}
.block-style-twentyFour .text p {
  font-size: 16px;
  line-height: 28px;
  padding-top: 15px;
}
/*----------------- Partner Section One -------------------*/
.partner-section-one {position: relative; z-index: 1;}
.partner-section-one .img-box a {display: block;height: 100%;}
.partner-section-one .img-box img {
  position: relative;
  top:50%;
  transform: translateY(-50%);
  margin: 0 auto;
}
.partner-section-one .img-box {
  background: #fff;
  border-radius: 50%;
  box-shadow: 15px 30px 50px rgba(26, 34, 72, 0.08);
  width: 175px;
  height: 175px;
  margin: 0 auto 35px;
  transition: all 0.3s ease-in-out;
}
.partner-section-one.bg-transparent .img-box {box-shadow: 15px 30px 50px rgba(23,32,90,0.06);}
.partner-section-one .img-box:hover {transform: scale(1.1);}
.partner-section-one .img-box.bx-b {width: 121px;height: 121px; margin-top: 45px;}
.partner-section-one .img-box.bx-d {width: 151px;height: 151px; margin-top: 25px;}
.partner-section-one .img-box.bx-f {width: 135px;height: 135px; margin-top: 20px;}
.partner-section-one .img-box.bx-g {width: 197px;height: 197px;}
.partner-section-one .img-box.bx-h {width: 138px;height: 138px; margin-top: 20px;}

.partner-section-one .shape-one {
  top: 9%;
  right: 14%;
  animation: rotated 12s infinite linear;
}
.partner-section-one .shape-two {
  top: 44%;
  right: 9%;
  animation: rotatedTwo 12s infinite linear;
}
.partner-section-one .shape-three {
  bottom: 19%;
  right: 12%;
  animation: jumpTwo 3s infinite linear;
}
.partner-section-one .shape-four {
  bottom: 0;
  right: 36%;
  animation: rotated 12s infinite linear;
}
.partner-section-one .shape-five {
  bottom: 0;
  left: 36%;
  animation: rotatedTwo 12s infinite linear;
}
.partner-section-one .shape-six {
  bottom: 17%;
  left: 18%;
  animation: rotated 12s infinite linear;
}
.partner-section-one .shape-seven {
  top: 43%;
  left: 9%;
  animation: jumpTwo 3s infinite linear;
}
.partner-section-one .shape-eight {
  top: 10%;
  left: 19%;
  animation: rotatedTwo 12s infinite linear;
}
/*----------------- Fancy Short Banner Ten -------------------*/
.fancy-short-banner-ten {
  background: #F5FBFF;
  padding: 110px 0 80px;
  position: relative;
  z-index: 1;
}
.fancy-short-banner-ten h2 {
  font-size: 60px;
  font-weight: 700;
}
.fancy-short-banner-ten p {
  font-size: 32px;
  line-height: 1.53em;
  color: #040404;
  padding: 30px 0 62px;
}
.fancy-short-banner-ten .download-btn {
  width: 300px;
  margin: 0 auto;
}
.fancy-short-banner-ten .download-btn button {
  width: 100%;
  line-height: 65px;
  border-radius: 40px;
  font-weight: 500;
  font-size: 18px;
  color: #000;
  background: transparent;
  border: 2px solid #000;
  text-align: center;
}
.fancy-short-banner-ten .download-btn button:after {
  content: url(../images/icon/89.svg);
  border: none;
  position: absolute;
  right: 30px;
  vertical-align: 0;
  transition: all 0.2s ease-in-out;
}
.fancy-short-banner-ten .download-btn.show button:after {transform: rotate(180deg);}
.fancy-short-banner-ten .download-btn .dropdown-menu {
  width: 100%;
  background: #fff;
  border-radius: 0px 0px 10px 10px;
  border: none;
  margin: 0;
  padding: 5px 0 0px;
}
.fancy-short-banner-ten .download-btn .dropdown-menu a {
  font-weight: 500;
  font-size: 15px;
  color: #000;
  padding: 8px 15px;
  background: transparent;
}

.fancy-short-banner-ten .download-btn .dropdown-menu a span {padding-left: 12px;}
.fancy-short-banner-ten .info-text {
  text-align: center;
  font-size: 15px;
  color: #6B6B6B;
  padding-top: 15px;
}
.fancy-short-banner-ten .shape-one {
  bottom: 0;
  right: 0;
  width: 16.8%;
}
.fancy-short-banner-ten .shape-two {
  top: 0;
  left: 0;
  width: 16.25%;
}
/*-------------------- Footer Style Eight --------------------*/
.theme-footer-eight .footer-about-widget ul li a {
  font-size: 18px; 
  color:#343434;
  margin-bottom: 5px;
  transition: all 0.2s ease-in-out;
}
.theme-footer-eight .footer-about-widget ul li a.phone {font-size: 20px;}
.theme-footer-eight .footer-about-widget ul li a:hover {text-decoration: underline; }
.theme-footer-eight .footer-title {
  font-size: 24px;
  font-weight: 500;
  color: #0E0E0E;
  position: relative;
  margin: 5px 0 21px;
}
.theme-footer-eight .footer-list ul li a {
  color:#343434;
  line-height: 40px;
  transition: all 0.2s ease-in-out;
}
.theme-footer-eight .footer-list ul li a:hover {text-decoration: underline;}
.theme-footer-eight .top-footer [class*="col-"] {margin-bottom: 35px;}
.theme-footer-eight .bottom-footer p {
  font-size: 16px;
  color:#373737;
}
.theme-footer-eight .bottom-footer .social-icon a {
  font-size: 20px;
  margin-left: 15px;
  color: #000;
}
/*----------------- Team Section Five --------------*/
.team-section-five {
  background: #F2FBFD;
  padding: 180px 0 150px;
}
/*----------------- Fancy Short Banner Eleven ---------------*/
.fancy-short-banner-eleven {position: relative; z-index: 1; text-align: center;}
.fancy-short-banner-eleven h2 {
  font-size: 68px;
  line-height: 1.23em;
}
.fancy-short-banner-eleven.bg-color {
  padding: 95px 0 130px;
  background: linear-gradient(45deg, #FFFBF2 0%, #EDFFFD 100%);
}
.fancy-short-banner-eleven [class*="bubble"] {position: absolute; z-index: -1; border-radius: 50%;}
.fancy-short-banner-eleven .bubble-one {
  width: 17px;
  height: 17px;
  background: #FF6CC4;
  top:-8px;
  right: 15%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-short-banner-eleven .bubble-two {
  width: 7px;
  height: 7px;
  background: #51FCFF;
  bottom:26%;
  right: 26%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-eleven .bubble-three {
  width: 25px;
  height: 25px;
  background: #B183FF;
  bottom:-12px;
  left: 12%;
  animation: scale-up-one 4s infinite linear;
}
.fancy-short-banner-eleven .bubble-four {
  width: 8px;
  height: 8px;
  background: #FFCD8B;
  top:32%;
  left: 14%;
  animation: scale-up-three 4s infinite linear;
}
.fancy-short-banner-eleven .shape-one {
  left: 0;
  top: -127px;
  animation: jumpTwo 4s infinite linear;
}
.fancy-short-banner-eleven .shape-two {
  right: 0;
  bottom: -127px;
  animation: jumpThree 4s infinite linear;
}
/*----------------- Team Details ---------------*/
.team-details {
  background: #F2FBFD;
  padding: 160px 0;
  position: relative;
  z-index: 1;
}
.team-details .main-bg {
  background: #fff;
  box-shadow: 0px 40px 80px rgba(0, 29, 56, 0.03);
}
.team-details .main-bg .img-meta {width: 48%;}
.team-details .main-bg .text-wrapper {
  width: 52%;
  padding: 40px 50px 40px 115px;
}
.team-details .main-bg .text-wrapper .name {font-size: 33px; color: #000;}
.team-details .main-bg .text-wrapper .position {
  color: rgba(0, 0, 0, 0.4);
  padding: 10px 0 50px;
}
.team-details .main-bg .text-wrapper h6 {
  font-size: 18px;
  padding-bottom: 15px;
  text-transform: uppercase;
}
.team-details .main-bg .text-wrapper .social-icon a {
  width: 42px;
  height: 42px;
  line-height: 42px;
  border-radius: 50%;
  text-align: center;
  font-size: 20px;
  color: rgba(0, 0, 0, 0.33);
  position: relative;
  z-index: 1;
  margin-right: 10px;
  transition: all 0.3s ease-in-out;
}
.team-details .main-bg .text-wrapper .social-icon a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.team-details .main-bg .text-wrapper .social-icon a:hover {color: #fff;}
.team-details .main-bg .text-wrapper .social-icon a:hover:before {opacity: 1;}
.team-details .main-bg .text-wrapper .social-icon li:nth-child(1) a:before {background: #6F55FF;}
.team-details .main-bg .text-wrapper .social-icon li:nth-child(2) a:before {background: #1DA1F2;}
.team-details .main-bg .text-wrapper .social-icon li:nth-child(3) a:before {background: #ea4c89 ;}
.team-details .main-bg .text-wrapper .social-icon li:nth-child(4) a:before {background: #D44638;}
.team-details .shape-one {
  left: 0;
  top: -127px;
  animation: jumpTwo 4s infinite linear;
}
.team-details .shape-two {
  right: 0;
  bottom: -127px;
  animation: jumpThree 4s infinite linear;
}
.team-details .pager {
  position: absolute;
  width: calc(100% + 180px);
  left: -90px;
  top: 50%;
  margin-top: -27px;
}
.team-details .pager a {
  width: 55px;
  height: 55px;
  line-height: 55px;
  text-align: center;
  background: #fff;
  border-radius: 50%;
  color: #000;
  font-size: 22px;
  transition: all 0.3s ease-in-out;
}
.team-details .pager a:hover {background: #353535; color: #fff;}
.team-details .pager a:first-child i {
  display: inline-block;
  transform: rotate(180deg);
}
/*----------------- Terms and Condition ---------------*/
.terms_and_policy {
  padding: 120px 0; 
  border-bottom: 1px solid #ECECEC;
}
.terms_and_policy .nav-tabs {
  border: none;
  background: #F2FBFD;
  padding: 30px 10px 30px 45px;
}
.terms_and_policy .nav-item {
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
  margin: 0;
}
.terms_and_policy .nav-item .nav-link {
  font-family: 'Inter', serif ;
  font-size: 20px;
  line-height: 60px;
  padding: 0;
  background: transparent;
  border:none;
  color: #ADADAD;
  transition: all 0.2s ease-in-out;
}
.terms_and_policy .nav-item .nav-link.active {color: #000;}
.terms_and_policy .tab-content {padding: 0 0 0 50px;}
.terms_and_policy .tab-content h2 {font-size: 55px; padding-bottom: 10px;}
.terms_and_policy .tab-content .update-date {
  font-size: 16px; 
  color: rgba(0, 0, 0, 0.4);
  margin-bottom: 20px;
}
.terms_and_policy .tab-content h3 {
  font-family: 'gilroy-semibold'; 
  font-size: 28px; 
  padding: 50px 0 25px;
} 
.terms_and_policy .tab-content p {
  line-height: 34px;
  margin-bottom: 15px;
}
.terms_and_policy .tab-content p a {
  text-decoration: underline;
  color: #000;
}
.terms_and_policy .tab-content ul {list-style-type: disc; padding-left: 18px;}
.terms_and_policy .tab-content ul li {color: #000;}
.terms_and_policy .tab-content ul li a {text-decoration: underline;}
.terms_and_policy .tab-content ul li {padding-bottom: 10px;}
/*----------------- 404 ---------------*/
.error-page {
  min-height: 100vh;
}
.error-page .img-holder {
  width: 50%; 
  height: 100vh; 
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.error-page .img-holder .illustration {
  min-height: 100vh;
  object-fit: cover;
  object-position: top center;
}
.error-page .img-holder .qus {
  top: 34%;
  left: 15%;
  width: 13%;
  z-index: 1;
  animation: fade-in 1s infinite linear;
}
.error-page .text-wrapper {
  width: 50%;
  padding: 50px 20px 50px 5%;
}
.error-page .text-wrapper h1 {
  font-size: 82px;
  line-height: 1.21em;
}
.error-page .text-wrapper p {
  font-size: 24px;
  line-height: 1.66em;
  font-weight: 300;
  color: #000;
  padding: 55px 15% 60px 0;
}
.error-page .text-wrapper .back-home {
  font-size: 17px;
  text-align: center;
  line-height: 55px;
  width: 200px;
  transition: all 0.3s ease-in-out;
  background: #000;
  padding: 0 20px;
  color: #fff;
}
.error-page .text-wrapper .back-home img {margin-left: 12px;}
.error-page .text-wrapper .back-home:hover {
  background: var(--purple-blue);
  color: #fff;
}
.error-page .text-wrapper .logo {
  position: absolute;
  top: 50px;
  left: 5%;
}
/*=======================================================================
                              Product landing / Mobile App           
=========================================================================*/
/*---------------------Theme Hero Banner/Ten ---------------*/
.hero-banner-ten {
  position: relative;
  z-index: 1;
  margin: 70px 0 0;
  text-align: center;
}
.hero-banner-ten .hero-heading {
  font-size: 82px;
  line-height: 1.21em;
  font-weight: 500;
}
.hero-banner-ten .hero-sub-heading {
  font-size: 24px;
  padding: 45px 0 55px;
  color: #000;
}
.hero-banner-ten .button-group a {
  width: 200px;
  height: 60px;
  padding: 0 5px 0 25px;
  margin: 10px 12px;
  background: #F0F0F0;
  border-radius: 6px;
  color: #000;
  text-align: left;
  transition: all 0.3s ease-in-out;
}
.hero-banner-ten .button-group a:hover {
  transform: translateY(-5px);
  box-shadow: -5px 10px 30px rgba(0, 0, 0, 0.1);
}
.hero-banner-ten .button-group a .icon {margin-right: 15px;}
.hero-banner-ten .button-group a span {
  font-size: 11px;
  color: #737373;
  display: block;
  margin-bottom: -11px;
  margin-top: -5px;
}
.hero-banner-ten .button-group a strong {
  font-weight: 500;
  font-size: 18px;
  display: block;
}
.hero-banner-ten .button-group a.ios-button {background: #303030; color: #fff;}
.hero-banner-ten .button-group a.ios-button span {color: rgba(255, 255, 255, 0.7);}
.hero-banner-ten [class*="icon-box"] {
  position: absolute; 
  z-index: -1;
  border-radius: 10px;
}
.hero-banner-ten [class*="icon-box"] img {
  margin: 0 auto;
  position: relative;
  top:50%;
  transform: translateY(-50%);
}
.hero-banner-ten .icon-box-one {
  width: 60px;
  height: 60px;
  background: #FFF7DB;
  top:-7%;
  left: 14%;
  animation: jumpTwo 4.5s infinite linear;
}
.hero-banner-ten .icon-box-two {
  width: 85px;
  height: 85px;
  background: #E7FFE9;
  top:28%;
  left: 4%;
  animation: jumpTwo 4s infinite linear;
}
.hero-banner-ten .icon-box-three {
  width: 70px;
  height: 70px;
  background: #E8F7FF;
  top:48%;
  left: 18%;
  animation: jumpThree 4.5s infinite linear;
}
.hero-banner-ten .icon-box-four {
  width: 70px;
  height: 70px;
  background: #F8E9FF;
  bottom:-10%;
  left: 9%;
  animation: jumpThree 4s infinite linear;
}
.hero-banner-ten .icon-box-five {
  width: 60px;
  height: 60px;
  background: #EAF0FF;
  top:-7%;
  right: 13%;
  animation: jumpTwo 4.5s infinite linear;
}
.hero-banner-ten .icon-box-six {
  width: 90px;
  height: 90px;
  background: #E1FFF3;
  top:29%;
  right: 4%;
  animation: jumpTwo 4s infinite linear;
}
.hero-banner-ten .icon-box-seven {
  width: 65px;
  height: 65px;
  background: #FFF8EA;
  top:48%;
  right: 18%;
  animation: jumpThree 4.5s infinite linear;
}
.hero-banner-ten .icon-box-eight {
  width: 75px;
  height: 75px;
  background: #FFEBEF;
  bottom:-12%;
  right: 10%;
  animation: jumpThree 4s infinite linear;
}
/*--------------------- App Screen Preview ---------------*/
.app-screen-preview-one {
  position: relative;
  z-index: 1;
  margin-top: 200px;
}
.app-preview-slider-one .img-holder img {
  transform: scale(0.8);
  margin: 0 auto;
  border-radius: 40px;
  transition: all 0.3s ease-in-out;
}
.app-preview-slider-one .slick-center .img-holder img {transform: scale(1);}
.app-screen-preview-one .round-bg {
  top: 50%;
  left: 50%;
  transform: translate(-50% , -50%);
}
.app-preview-slider-one {margin: 0 -55px;}
.app-screen-preview-one .shape-one {
  top: 9%;
  right: 9%;
}
.app-screen-preview-one .shape-two {
  bottom: 37%;
  right: 9%;
}
.app-screen-preview-one .shape-three {
  bottom: 10%;
  left: 9%;
}
.app-screen-preview-one .shape-four {
  top: 42%;
  left: 9%;
}
/*----------------- Fancy Feature Twenty Five -------------------*/
.block-style-twentyFive .text-wrapper h6 {
  font-size: 24px;
  color: #BABABA;
  padding-bottom: 30px;
}
.block-style-twentyFive .text-wrapper span {text-decoration: underline;}
.block-style-twentyFive .title {
  font-size: 58px;
  line-height: 1.29em;
  font-weight: 500;
  padding-bottom: 38px;
}
.block-style-twentyFive p {
  font-size: 24px;
  line-height: 1.91em;
  color: #000;
}
.block-style-twentyFive ul li {
  line-height: 42px;
  color: #000;
  position: relative;
  padding-left: 30px;
}
.block-style-twentyFive ul li:before {
  content: url(../images/icon/76.svg);
  position: absolute;
  top: 0;
  left: 0;
}
.block-style-twentyFive ul {padding-top: 50px;}
.block-style-twentyFive .screen-container {
  position: relative;
  display: inline-block;
}
.block-style-twentyFive .screen-container .block-content {
  position: absolute;
  top: 50%;
  left: -70px;
  width: 100%;
  transform: translateY(-50%);
  z-index: 1;
}
.block-style-twentyFive .screen-container .feature-meta {
  padding: 75px 50px 65px;
  background: #FFFFFF;
  box-shadow: 0px 30px 60px rgba(6, 24, 67, 0.05);
  border-radius: 10px;
  text-align: left;
  margin: 40px 0;
}
.block-style-twentyFive .screen-container .feature-meta .icon {height: 42px;}
.block-style-twentyFive .screen-container .feature-meta h4 {
  font-size: 26px;
  line-height: 1.35em;
  font-weight: 500;
  padding-top: 35px;
}
/*--------------------- App Screen Preview ---------------*/
.app-preview-slider-two {
  max-width: 1830px;
  padding: 0 15px;
  margin: 0 auto;
}
.app-preview-slider-two .img-holder img {
  transform: scale(0.8);
  border-radius: 20px;
  margin: 0 auto;
  transition: all 0.3s ease-in-out;
}
.app-preview-slider-two .slick-center .img-holder img {transform: scale(1);}
.app-screen-preview-one .round-bg {
  top: 50%;
  left: 50%;
  transform: translate(-50% , -50%);
}
/*--------------------- Pricing Section Seven ------------------*/
.pricing-table-area-seven .pr-table-wrapper {
  background: #FFFFFF;
  border: 1px solid #F5F5F5;
  box-shadow: 0px 3px 4px rgba(8, 20, 32, 0.04);
  border-radius: 15px;
  position: relative;
  padding: 60px 15px 50px 58px;
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-seven .pr-table-wrapper .icon {height: 25px;}
.pricing-table-area-seven .pr-table-wrapper .pack-name {
  font-size: 32px;
  font-weight: 500;
  color: #000;
  padding: 37px 0 30px;
}
.pricing-table-area-seven .pr-table-wrapper .pr-feature li {
  line-height: 42px;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 26px;
  position: relative;
}
.pricing-table-area-seven .pr-table-wrapper .pr-feature li:before {
  content: url(../images/icon/139.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.pricing-table-area-seven .pr-table-wrapper .price {
  color: #000;
  font-size: 28px;
  padding: 50px 0 3px;
}
.pricing-table-area-seven .pr-table-wrapper .price span {font-size: 18px;}
.pricing-table-area-seven .pr-table-wrapper .trial-text {color: rgba(0, 0, 0, 0.34);}
.pricing-table-area-seven .pr-table-wrapper .trial-button {
  width: 154px;
  line-height: 43px;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 500;
  color: #000;
  border: 1px solid #000;
  margin-top: 28px;
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.pricing-table-area-seven .pr-table-wrapper .trial-button:hover,
.pricing-table-area-seven .pr-table-wrapper.active .trial-button {
  background: #373737;
  color: #fff;
}
.pricing-table-area-seven .pr-table-wrapper.active,
.pricing-table-area-seven .pr-table-wrapper:hover {
  box-shadow: 0px 25px 60px rgba(0, 13, 48, 0.0480786);
  border-radius: 16px;
  border-color: #fff;
}
/*-------------- Client Feedback Slider Seven ----------------*/
.client-feedback-slider-seven {
  position: relative;
  padding: 180px 0;
  z-index: 1;
}
.client-feedback-slider-seven .bg-image {
  position: absolute;
  max-height: 100%;
  min-height: 100%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  object-fit: cover;
  object-position: top center;
  z-index: -1;
}
.client-feedback-slider-seven .clientSliderFive p {color: #4F4F4F;}
.client-feedback-slider-seven .clientSliderFive .name {color: #000;}
.client-feedback-slider-seven .slider-arrow li {
  width: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  margin: 0 7px;
  font-size: 26px;
  color: #5D5D5D;
  transition: all 0.3s ease-in-out;
  transform: scale(0.6);
}
.client-feedback-slider-seven .slider-arrow li:first-child i {transform: rotate(180deg); display: inline-block;}
.client-feedback-slider-seven .slider-arrow li:hover {
  opacity: 1;
  transform: scale(1);
}
/*----------------- Fancy Short Banner Twelve ---------------*/
.fancy-short-banner-twelve {
  background: #FFF4E9;
  padding: 135px 0 115px;
  position: relative;
  z-index: 1;
}
.fancy-short-banner-twelve .button-group a {
  width: 200px;
  height: 60px;
  padding: 0 5px 0 25px;
  margin: 10px 12px;
  background: transparent;
  border-radius: 6px;
  border: 2px solid #000;
  color: #000;
  text-align: left;
  transition: all 0.3s ease-in-out;
}
.fancy-short-banner-twelve .button-group a:hover {
  transform: translateY(-5px);
  box-shadow: -5px 10px 30px rgba(0, 0, 0, 0.05);
}
.fancy-short-banner-twelve .button-group a .icon {margin-right: 15px;}
.fancy-short-banner-twelve .button-group a span {
  font-size: 11px;
  color: #737373;
  display: block;
  margin-bottom: -11px;
  margin-top: -5px;
}
.fancy-short-banner-twelve .button-group a strong {
  font-weight: 500;
  font-size: 18px;
  display: block;
}
.fancy-short-banner-twelve .button-group a.ios-button {background: #fff; border-color: #fff;}
.fancy-short-banner-twelve .button-group a.ios-button span {color: #808080;}
.fancy-short-banner-twelve .shape-one {
  bottom: 0;
  right: 0;
  height: 100%;
}
.fancy-short-banner-twelve .shape-two {
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
/*=======================================================================
                              Product landing / Doc Signature        
=========================================================================*/
/*---------------------Theme Hero Banner/Eleven ---------------*/
.hero-banner-eleven {
  position: relative;
  z-index: 1;
  margin: 90px 0 0;
}
.hero-banner-eleven .hero-text-wrapper h1 {
  font-family: 'Inter';
  font-weight: 700; 
  font-size: 82px;
  line-height: 1.30em;
}
.hero-banner-eleven .hero-text-wrapper h1 span {position: relative; display: inline-block;}
.hero-banner-eleven .hero-text-wrapper h1 span img {
  left: 0;
  bottom: -10px;
  width: 100%;
}
.hero-banner-eleven .hero-text-wrapper .hero-sub-heading {
  font-size: 28px;
  line-height: 1.5em;
  color: #000;
  padding: 40px 0 68px 0;
}
.hero-banner-eleven form {
  max-width: 600px;
  height: 80px;
  position: relative;
}
.hero-banner-eleven form input {
  border: none;
  background: #F4F4F4;
  border-radius: 7px;
  width: 100%;
  height: 100%;
  font-size: 16px;
  padding: 0 190px 0 30px;
}
.hero-banner-eleven form button {
  position: absolute;
  right: 10px;
  top:8px;
  bottom: 8px;
  font-weight: 500;
  background: #242424;
  width: 170px;
  border-radius: 7px;
  text-align: center;
  color: #fff;
  font-size: 17px;
  transition: all 0.3s ease-in-out;
}
.hero-banner-eleven form button:hover {background: #FFB840; }
.hero-banner-eleven .term-text {
  font-size: 16px; 
  color: #979797; 
  margin-top: 22px;
  letter-spacing: -0.5px;
}
.hero-banner-eleven .illustration-container {
  position: absolute;
  top: -14%;
  right: 5%;
  max-width: 46%;
}
/*----------------- Fancy Feature Twenty Six -------------------*/
.fancy-feature-twentySix .row {margin: 0 -20px;}
.fancy-feature-twentySix .row [class*="col-"] {padding: 0 20px;}
.block-style-twentySix {
  padding: 65px 45px 52px;
  position: relative;
}
.block-style-twentySix .shape-one {
  top: 0;
  left: 0;
  z-index: 1;
  height: 100%;
}
.block-style-twentySix .shape-two {
  top: 0;
  right: 0;
  z-index: 1;
  height: 100%;
}
.block-style-twentySix h6 {
  font-weight: 500;
  font-size: 20px;
  margin-bottom: 22px;
}
.block-style-twentySix h4 {
  font-weight: 700;
  font-size: 28px;
  line-height: 1.5em;
  margin-bottom: 35px;
}
.block-style-twentySix .try-btn {
  width: 145px;
  line-height: 41px;
  border-radius: 5px;
  border-style: solid;
  border-width: 2px;
  font-weight: 500;
  font-size: 16px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.block-style-twentySix .try-btn:hover {
  background: #212121;
  color: #fff;
}
/*----------------- Fancy Feature Twenty Seven -------------------*/
.block-style-twentySeven {
  border: 1px solid #000;
  border-radius: 20px;
  padding: 55px 12px 40px;
  text-align: center;
  margin-top: 35px;
  transition: all 0.3s ease-in-out;
}
.block-style-twentySeven:hover {
  border-color: #fff;
  background: #fff;
  box-shadow: 0 15px 40px rgba(21, 21, 21, 0.09);
}
.block-style-twentySeven .icon {height: 50px;}
.block-style-twentySeven .icon img {max-height: 100%;}
.block-style-twentySeven h4 {
  font-weight: 500;
  font-size: 24px;
  letter-spacing: -0.5px;
  margin: 42px 0 20px;
}
.block-style-twentySeven p {font-size: 17px; line-height: 28px;}
/*----------------- Video Box Two -------------------*/
.video-box-two .video-icon {
  width: 170px;
  height: 110px;
  background: #000;
  position: absolute;
  top: 50%;
  left: 50%;
  transition: all 0.3s ease-in-out;
  transform: translate(-50% , -50%);
}
.video-box-two .video-icon img {margin-left: -9px;}
.video-box-two .video-icon:hover {background: #FFB840;}
/*----------------- Fancy Text block Twenty Three -------------------*/
.fancy-text-block-twentyThree .text-wrapper .cl-text {font-size: 24px;color: #000; margin-bottom: 25px;}
.fancy-text-block-twentyThree .text-wrapper .cl-text span {color: #FFB840;}
.fancy-text-block-twentyThree .img-container {padding: 0 10% 12% 0;}
.fancy-text-block-twentyThree .img-container .screen-one {
  position: absolute;
  z-index: 1;
  right: 0;
  bottom: 0;
  width: 51.8%;
  animation: jumpTwo 5s infinite linear;
}
.block-style-twentyEight .icon {height: 37px;}
.block-style-twentyEight h4 {
  font-weight: 500;
  font-size: 24px;
  margin: 33px 0 20px;
}
/*-------------------- Useable Tools Four --------------------*/
.useable-tools-section-four {padding: 44px 0 35px; overflow: hidden;}
.useable-tools-section-four .bg-wrapper {
  max-width: 1700px;
  margin: 0 auto;
  position: relative;
  background: #EBF3F8;
}
.useable-tools-section-four .bg-wrapper:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 44px;
  background: url(../images/shape/226.jpg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -40px;
}
.useable-tools-section-four .bg-wrapper:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 35px;
  background: url(../images/shape/227.jpg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -30px;
}
.useable-tools-section-four .text-wrapper p {
  font-size: 24px;
  line-height: 1.91em;
  color: #000;
  padding: 40px 0 50px;
}
.useable-tools-section-four .text-wrapper {padding: 120px 0 115px;}
.useable-tools-section-four .logo-container {position: relative; z-index: 1;}
.useable-tools-section-four .logo-container .inner-wrapper {
  position: absolute;
  width: 100%;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.useable-tools-section-four .logo-container .icon {
  height: 235px; 
  background: #fff;
  margin: 35px 0;
}
.useable-tools-section-four .logo-container .line-two {transform: translateY(30px);}
/*-------------------- Fancy Feature Twenty Eight --------------------*/
.block-style-twentyNine {
  padding: 30px 50px 55px 55px;
  height: 100%;
}
.block-style-twentyNine .logo {height: 65px;}
.block-style-twentyNine .video-icon {
  display: block;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  margin: 85px 0 25px;
  padding: 10px 0 0 13px;
}
.block-style-twentyNine .video-icon:hover {background: #000;}
.block-style-twentyNine h3 a {
  font-size: 36px;
  font-weight: 500;
  line-height: 1.38em;
  color: #fff;
  margin-bottom: 50px;
}
.block-style-twentyNine .read-more-btn span {
  font-size: 16px;
  color: #fff;
  font-weight: 500;
}
.block-style-twentyNine h3:hover a,
.block-style-twentyNine .read-more-btn:hover span {text-decoration: underline;}
/*-------------------- Pricing Section Eight --------------------*/
.pricing-section-eight .pr-table-wrapper {
  border: 1px solid #ededed;
  border-radius: 15px;
  text-align: center;
}
.pricing-section-eight .pr-column {border-right: 1px solid #ededed;}
.pricing-section-eight .pr-column:last-child {border-right: none;}
.pricing-section-eight .pr-column .plan {
  display: inline-block;
  border: 1px solid #CFCFCF;
  border-radius: 17px;
  line-height: 28px;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 1.5px;
  margin: 29px 0 22px;
  padding: 0 15px;
}
.pricing-section-eight .pr-column .price {
  font-size: 52px;
  font-weight: 500;
  letter-spacing: -1px;
  color: #000;
  line-height: initial;
  margin-bottom: 3px;
}
.pricing-section-eight .pr-column .pr-header {
  padding-bottom: 35px;
  border-bottom: 1px solid #ededed;
}
.pricing-section-eight .pr-body .pr-text {
  font-size: 16px;
  display: inline-block;
  margin-bottom: 5px;
}
.pricing-section-eight .pr-body li img {height: 14px;}
.pricing-section-eight .pr-body li {
  border-bottom: 1px solid #ededed;
  padding: 32px 0;
}
.pricing-section-eight .trial-button {
  width: 160px;
  line-height: 46px;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 500;
  color: #000;
  border: 1px solid #000;
  text-align: center;
  transition: all 0.3s ease-in-out;
}
.pricing-section-eight .trial-button:hover {
  background: #373737;
  color: #fff;
}
.pricing-section-eight .trial-text { 
  font-size: 16px;
  margin-top: 20px;
}
.pricing-section-eight .pr-footer {padding: 38px 0 25px;}
.pricing-section-eight .pr-list-wrapper {
  border: 1px solid #ededed;
  border-radius: 15px 0 0 15px;
  border-right: none;
  top: 229px;
  left: 0;
  height: 100%;
}
.pricing-section-eight .pr-list-wrapper li {
  font-size: 17px;
  font-weight: 500;
  color: #000;
  line-height: 14px;
  border-bottom: 1px solid #ededed;
  padding: 32px 0 32px 25px;
}
.pricing-section-eight .pr-list-wrapper li:last-child {border-bottom: none;}
/*-------------------- Faq Section Five --------------------*/
.accordion-style-five .card {
  background: transparent;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #E5E5E5;
}
.accordion-style-five .card .card-header {
  background: transparent;
  border-radius: 0;
  padding: 0;
  border:none;
}
.accordion-style-five .card .card-header button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 30px 35px 30px 0;
  font-size: 21px;
  font-weight: 500;
  border:none;
  border-radius: 0;
  margin: 0;
  color: var(--heading);
  text-decoration: none;
  position: relative;
  border-bottom: 1px solid transparent;
}
.accordion-style-five .card:first-child .card-header button {border-top: 1px solid #E5E5E5;}
.accordion-style-five .card .card-header button:before {  
  content: "\f107";
  font-family: 'font-awesome';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.accordion-style-five .card .card-body {padding: 0 50px 20px 0;}
.accordion-style-five .card .card-body p {padding-bottom: 15px;}
.accordion-style-five .card .card-body p a {
  text-decoration: underline;
  color: var(--p-color);
}
.accordion-style-five .card:last-child .card-body {border:none; padding-bottom: 0;}
/*-------------------- Fancy Short Banner Thirteen --------------------*/
.fancy-short-banner-thirteen .bg-wrapper {
  max-width: 1700px;
  margin: 0 auto;
  position: relative;
  background: #FBF5ED;
  padding: 75px 0 72px;
  text-align: center;
  z-index: 1;
}
.fancy-short-banner-thirteen .bg-wrapper:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 32px;
  background: url(../images/shape/228.jpg) no-repeat center top;
  background-size: cover;
  left: 0;
  top: -30px;
}
.fancy-short-banner-thirteen .bg-wrapper:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 30px;
  background: url(../images/shape/229.jpg) no-repeat center bottom;
  background-size: cover;
  left: 0;
  bottom: -28px;
}
/*-------------------- Fancy Hero Six ---------------------*/
.fancy-hero-six {
  padding: 48px 0 95px;
  text-align: center;
}
.fancy-hero-six .heading {
  font-family: 'Inter';
  font-size: 68px;
  font-weight: 500;
  line-height: initial;
  color: var(--heading);
}
.fancy-hero-six .sub-heading {
  font-size: 24px; 
  margin-top: 22px;
  color: #000;
}
/*-------------------- Fancy Portfolio Two ---------------------*/
.mixitUp-container {overflow: hidden;}
.po-control-one button {
  display: inline-block;
  line-height: 50px;
  border-radius: 8px;
  padding: 0 33px;
  margin: 0 0.9%;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.po-control-one button.mixitup-control-active {background: #000;color: #fff;}
.mixitUp-container.gutter-space-one {margin: 0 -25px;}
.mixitUp-container.gutter-space-one .mix {padding: 0 25px; margin-bottom: 50px;}
.mixitUp-container.gutter-space-two {margin: 0 -20px;}
.mixitUp-container.gutter-space-two .mix {padding: 0 20px; margin-bottom: 40px;}
.fancy-portfolio-two .mix {width: 33.33%;}
.fancy-portfolio-two .mix.mixL {width: 66.66%;}
.fancy-portfolio-two .portfolio-block-two {border-radius: 20px;}
.portfolio-block-two {
  overflow: hidden;
  height: 100%;
  background: #191928;
}
.portfolio-block-two .hover-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #141222;
  padding: 15px 5px 20px 30px;
  transition: all 0.4s ease-in-out;
  opacity: 0;
  transform: translateY(100%);
}
.portfolio-block-two .hover-content h3 a {
  font-family: 'Inter';
  font-size: 22px;
  color: #fff;
  position: relative;
}
.portfolio-block-two .hover-content h3 a:before {
  content: '';
  position: absolute;
  left: 104%;
  bottom: 6px;
  width: 22px;
  height: 2px;
  background: #fff;
}
.portfolio-block-two .hover-content h3 a:hover {text-decoration: underline;}
.portfolio-block-two .hover-content .tag {
  font-size: 15px;
  margin-top: -5px;
  color: rgba(255, 255, 255, 0.7);
}
.portfolio-block-two .fancybox {
  width: 45px;
  height: 45px;
  font-size: 17px;
  border-radius: 50%;
  background: #000;
  color: #fff;
  position: absolute;
  top: 20px;
  right: 20px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease-in-out;
}
.portfolio-block-two .fancybox:hover {background: var(--blue-dark);}
.portfolio-block-two:hover .fancybox {opacity: 1; transform: translateX(0);}
.portfolio-block-two:hover .hover-content {opacity: 1; transform: translateY(0);}
.portfolio-block-two:hover .img-meta {opacity: 0.85;}
/*-------------------- Fancy Portfolio Three ---------------------*/
.fancy-portfolio-three .mix {width: 50%;}
/*-------------------- Fancy Portfolio Four ---------------------*/
.fancy-portfolio-four .mix {width: 33.33333%;}
.fancy-portfolio-four .contact-title {
  font-weight: 500;
  line-height: 1.34em;
  font-size: 55px;
  letter-spacing: -1px;
}
/*-------------------- Fancy Portfolio Five ---------------------*/
.fancy-portfolio-five {overflow: hidden;}
.fancy-portfolio-five .slider-wrapper {margin: 0 -20vw;}
.portfolio_slider_one {margin: 0 -25px;}
.portfolio_slider_one .item {padding: 0 25px;}
.portfolio-block-three .img-holder {
  display: block;
  border-radius: 32px; 
  overflow: hidden; 
  position: relative;
  margin-bottom: 36px;
}
.portfolio-block-three .img-holder:before {
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.7);
  position: absolute;
  z-index: 1;
  transition: all 0.3s ease-in-out;
}
.slick-center .portfolio-block-three .img-holder:hover img {transform: scale3d(1.1, 1.1, 1.0);}
.portfolio-block-three .text {
  opacity: 0; 
  transition: all 0.3s ease-in-out;
  text-align: center;
}
.portfolio-block-three .text .tag {
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 4px;
  color: #B2B2B2;
  margin-bottom: 12px;
}
.portfolio-block-three .text a {
  font-family: 'gilroy-semibold';
  font-size: 44px;
  line-height: 1.27em;
  color: #000;
  letter-spacing: -1px;
}
.portfolio-block-three .text a:hover {text-decoration: underline;}
.slick-center .portfolio-block-three .img-holder:before {opacity: 0;}
.slick-center .portfolio-block-three .text {opacity: 1;}
.fancy-portfolio-five .slider-arrows li {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  color: #000;
  font-size: 25px;
  margin-left: 8px;
  cursor: pointer;
  transform: scale(0.8);
  transition: all 0.3s ease-in-out;
}
.fancy-portfolio-five .slider-arrows li:first-child i {transform: rotate(-180deg); display: inline-block;}
.fancy-portfolio-five .slider-arrows li:hover {
  background: #353535;
  color: #fff; 
  transform: scale(1);
}
/*-------------------- Fancy Portfolio Six ---------------------*/
.fancy-portfolio-six .mixitUp-container {
  max-width: 1100px;
  margin: 0 auto;
  overflow: hidden;
}
.fancy-portfolio-six .mix {margin-bottom: 50px;}
.po-control-two button {
  font-size: 20px;
  display: inline-block;
  color: #868686;
  padding: 0 3%;
  margin: 8px 0;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease-in-out;
}
.po-control-two button.mixitup-control-active {color: #000; text-decoration: underline;}
.po-control-two button:before {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: #868686;
  border-radius: 50%;
  right: -2px;
  bottom: 7px;
}
.po-control-two button:last-child:before {display: none;}
.fancy-portfolio-six .scroll-target {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border: 1px solid #DEDEDE;
  border-radius: 50%;
  transition: all 0.3s ease-in-out;
}
.fancy-portfolio-six .scroll-target:hover {
  box-shadow: 0 0 35px rgba(0, 0, 0, 0.05);
  border-color: transparent;
}
/*-------------------- Portfolio Details One ---------------------*/
.portfolio-details-one .header .tag {
  font-size: 16px;
  color: #BDBDBD;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  margin-bottom: 12px;
}
.portfolio-details-one .header h2 {letter-spacing: -1px;}
.portfolio-details-one .header .social-icon a {
  width: 44px;
  height: 44px;
  border: 1px solid #E7E7E7;
  border-radius: 50%;
  line-height: 42px;
  color: #000;
  font-size: 20px;
  margin: 0 6px;
  transition: all 0.3s ease-in-out;
}
.portfolio-details-one .header .social-icon a:hover {
  background: #000;
  color: #fff;
}
.portfolio-details-one .main-content .project-info strong {
  display: block;
  font-size: 16px;
  color: #000;
  letter-spacing: 1px;
  text-transform: uppercase;
}
.portfolio-details-one .main-content .project-info span {
  display: block;
  text-transform: uppercase;
  color: #A1A1A1;
  font-size: 17px;
  letter-spacing: 1px;
  margin: -2px 0 50px;
}
.portfolio-details-one .main-content h4 {
  font-family: 'Inter';
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 25px;
  letter-spacing: -1px;
}
.portfolio-details-one .main-content p {margin-bottom: 50px;}
.portfolio-details-one .portfolio-pagination .tp1 {
  font-size: 14px;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 3px;
  color: rgba(0, 0, 0, 0.3);
}
.portfolio-details-one .portfolio-pagination .tp2 {
  font-family: 'Inter';
  font-weight: 500;
  font-size: 25px;
  letter-spacing: -1px;
  color: #000;
}
.portfolio-details-one .portfolio-pagination a:hover .tp2 {text-decoration: underline;}
/*=======================================================================
                              Product landing / Website Builder      
=========================================================================*/
/*-------------------- Menu ---------------*/
.theme-main-menu.sticky-menu.fixed.theme-menu-seven {background: #242424;}
.theme-menu-seven .navbar-nav .nav-item .nav-link {
  font-size: 18px;
  margin: 0 25px;
  color: #fff;
}
.theme-menu-seven .navbar-nav .nav-item .nav-link.active {text-decoration: underline;}
.theme-menu-seven .right-widget .signIn-action {
  font-size: 18px;
  font-weight: 500;
  line-height: 46px;
  color: #fff;
  margin-right: 40px;
  transition: all 0.3s ease-in-out;
}
.theme-menu-seven .right-widget .signIn-action:hover {text-decoration: underline;}
.theme-menu-seven .right-widget .signIn-action img {margin-right: 12px; margin-top: -3px;}
/*---------------------Theme Hero Banner / Twelve ---------------*/
.hero-banner-twelve {
  background: #1F1F1F;
  position: relative;
  z-index: 1;
  padding: 205px 0 0;
}
.hero-banner-twelve:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 11%;
  left: 0;
  bottom: 0;
  background: #fff;
  z-index: -1;
}
.hero-banner-twelve .hero-heading {
  font-size: 100px;
  font-weight: 700;
  color: #fff;
  line-height: 1.1em;
  letter-spacing: -1px;
}
.hero-banner-twelve .hero-sub-heading {
  font-size: 23px;
  color: #fff;
  padding: 45px 0 55px;
}
.hero-banner-twelve .get-start-btn {
  line-height: 56px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  border: 2px solid #fff;
  border-radius: 50px;
  padding: 0 40px;
  transition: all 0.3s ease-in-out;
}
.hero-banner-twelve .get-start-btn:hover {
  background: #FF006B;
  border-color: #FF006B;
}
.hero-banner-twelve .screen-holder {
  background: rgba(255, 255, 255, 0.19);
  border-radius: 30px;
  padding: 40px;
  position: relative;
  margin-top: 80px;
}
.hero-banner-twelve .screen-holder .img-meta {
  width: 100%;
  border-radius: 20px;
}
.hero-banner-twelve .screen-holder .shape-one {
  left: -9%;
  bottom: -13%;
  width: 23.55%;
  animation: jumpThree 6s infinite linear;
}
.hero-banner-twelve .screen-holder .shape-two {
  right: -6%;
  top: -8%;
  z-index: 1;
  width: 13%;
  animation: jumpTwo 6s infinite linear;
}
.hero-banner-twelve .screen-holder .screen-one {
  z-index: 1;
  border-radius: 15px;
  top: 23%;
  left: -9%;
  width: 21.96%;
  box-shadow: 0px 30px 60px rgba(0, 0, 0, 0.1);
  animation: jumpTwo 6s infinite linear;
}
.hero-banner-twelve .screen-holder .screen-two {
  z-index: 1;
  border-radius: 10px;
  bottom: -10%;
  right: -10%;
  width: 20.76%;
  animation: jumpThree 6s infinite linear;
  box-shadow: 15px 25px 50px rgba(16, 44, 78, 0.05);
}
.hero-banner-twelve .shape-three {
  top: 15%;
  left: 15%;
}
.hero-banner-twelve .shape-four {
  top: 30%;
  left: 8%;
}
.hero-banner-twelve .shape-five {
  top: 44%;
  left: 16%;
}
.hero-banner-twelve .shape-six {
  top: 16%;
  right: 20%;
}
.hero-banner-twelve .shape-seven {
  top: 30%;
  right: 9%;
}
.hero-banner-twelve .shape-eight {
  top: 44%;
  right: 20%;
}
/*-------------------- Counter Style Three --------------*/
.counter-box-five .number {
  font-size: 58px;
  font-weight: 700;
  line-height: initial;
}
.counter-box-five p {color: #000;}
.counter-box-five .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto 15px;
}
/*-------------------- Fancy Feature Twenty Nine --------------------*/
.block-style-thirty .feature {
  display: inline-block;
  text-transform: uppercase;
  font-weight: 500;
  font-size: 15px;
  color: #000;
  letter-spacing: 2px;
  position: relative;
  padding-bottom: 4px;
  margin-bottom: 25px;
}
.block-style-thirty .feature:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #000;
  left: 0;
  bottom: 0;
}
.block-style-thirty .feature-list-one li {
  position: relative;
  padding-left: 60px;
  margin-bottom: 45px;
}
.block-style-thirty .feature-list-one li:last-child {margin: 0;}
.block-style-thirty .feature-list-one .icon {
  position: absolute;
  left: 0;
  top: 2px;
}
.block-style-thirty .feature-list-one strong {
  font-weight: 500;
  font-size: 24px;
  color: #000;
  display: block;
  padding-bottom: 13px;
}
.block-style-thirty .feature-list-one span {
  display: block;
}
.block-style-thirty .feature-list-two li {
  line-height: 33px;
  color: #474747;
  position: relative;
  padding-left: 30px;
  margin-bottom: 22px;
}
.block-style-thirty .feature-list-two li span {
  font-weight: 500;
  color: #000;
  position: relative;
}
.block-style-thirty .feature-list-two li span:before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: #FFB840;
}
.block-style-thirty .feature-list-two li:before {
  content: url(../images/icon/76.svg);
  position: absolute;
  top: 0;
  left: 0;
}
.block-style-thirty .text-block p {
  font-size: 22px;
  line-height:1.68em;
  color: #000;
  margin-bottom: 35px;
}
.block-style-thirtyOne {
  border: 1px solid #E4E4E4;
  border-radius: 10px;
  padding: 35px 35px 52px;
}
.block-style-thirtyOne .icon {height: 32px;}
.block-style-thirtyOne h4 {
  font-weight: 500;
  font-size: 24px;
  padding: 20px 0;
}
.block-style-thirtyOne p {font-size: 17px; color: #000;}
/*-------------------- Fancy Feature Thirty --------------------*/
.fancy-feature-thirty {padding: 0 10px;}
.fancy-feature-thirty .wrapper {
  max-width: 1450px;
  margin: 0 auto;
  padding: 85px 10px 105px;
  position: relative;
  background: #F5FAFD;
  border-radius: 30px;
}
.fancy-feature-thirty .wrapper .shape-one {
  right: -55px;
  top: -65px;
  animation: jumpThree 6s infinite linear;
}
.fancy-feature-thirty .wrapper .shape-two {
  left: -45px;
  bottom: -70px;
  animation: jumpTwo 6s infinite linear;
}
.block-style-thirtyTwo {
  padding: 45px 55px 35px 30px;
  background: #fff;
  border-radius: 10px;
  margin-top: 40px;
  transition: all 0.3s ease-in-out;
}
.block-style-thirtyTwo:hover {transform: translateY(-5px);}
.block-style-thirtyTwo .icon {
  width: 55px;
  height: 55px;
  border-radius: 50%;
}
.block-style-thirtyTwo .text {
  width: calc(100% - 55px);
  padding-left: 32px;
}
.block-style-thirtyTwo .text h4 {
  font-weight: 500;
  font-size: 24px;
}
.block-style-thirtyTwo .text p {
  font-size: 17px;
  line-height: 1.6em;
  padding: 22px 0;
}
.block-style-thirtyTwo .text .theme-btn-ten {font-size: 15px; padding-bottom: 0;}
.block-style-thirtyTwo .text .theme-btn-ten .fa {font-size: 10px; margin-left: 2px;}
/*----------------- Fancy Short Banner Fourteen -------------------*/
.fancy-short-banner-fourteen {
  background: #1F1F1F;
  padding: 110px 0 120px;
  position: relative;
  z-index: 1;
  text-align: center;
}
.fancy-short-banner-fourteen .title-style-thirteen h2 {
  color: #fff;
  line-height: 1.19em;
  letter-spacing: 0;
}
.fancy-short-banner-fourteen p {
  font-size: 24px;
  color: #fff;
  padding: 50px 0;
}
.fancy-short-banner-fourteen .shape-one {top: 0; right: 0;}
.fancy-short-banner-fourteen .shape-two {bottom: 0; left: 0;}
.fancy-short-banner-fourteen .shape-three {
  top: 18%;
  left: 14%;
}
.fancy-short-banner-fourteen .shape-four {
  top: 52%;
  left: 6%;
}
.fancy-short-banner-fourteen .shape-five {
  top: 79%;
  left: 16%;
}
.fancy-short-banner-fourteen .shape-six {
  top: 18%;
  right: 20%;
}
.fancy-short-banner-fourteen .shape-seven {
  top: 49%;
  right: 10%;
}
.fancy-short-banner-fourteen .shape-eight {
  top: 77%;
  right: 20%;
}
/*----------------- Block Style Thirty Three -------------------*/
.block-style-thirtyThree .icon {height: 40px}
.block-style-thirtyThree .title {
  font-family: 'Inter';
  font-size: 24px;
  margin: 25px 0;
  display: inline-block;
  position: relative;
}
.block-style-thirtyThree .title:before {
  content: '';
  width: 100%;
  height: 3px;
  background: var(--yellow-deep);
  border-radius: 2px;
  position: absolute;
  left: 0;
  bottom: -2px;
}
.block-style-thirtyThree p {
  font-size: 17px;
  line-height: 30px;
  color: #515151;
}
/*----------------- Video Box Two -------------------*/
.fancy-video-box-two .main-img {width: 100%; border-radius: 10px;}
.fancy-video-box-two {position: relative;}
.fancy-video-box-two .video-button {
  position: absolute;
  width: 85px;
  height: 85px;
  background: var(--blue-dark);
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50% , -50%);
  z-index: 1;
  transition: all 0.3s ease-in-out;
}
.fancy-video-box-two .video-button:hover {background: #FFBA12;}
/*-------------------- Fancy Feature Thirty One --------------------*/
.fancy-feature-thirtyOne {background: #FCF7EF;}
.fancy-feature-thirtyOne .block-style-thirtyTwo {
  box-shadow: 4.66667px 15px 30px rgba(171, 110, 12, 0.05);
}
/*-------------------- Fancy Feature Thirty Two --------------------*/
.fancy-feature-thirtyTwo {background: #FAFCFF;}
.fancy-feature-thirtyTwo .block-style-twentyTwo {
  border-radius: 10px;
  box-shadow: 0px 25px 60px rgba(13, 32, 96, 0.04);
}
.fancy-feature-thirtyTwo .block-style-twentyTwo .icon {width: 60px; height: 60px;}
/*-------------------- Fancy Hero Seven ---------------------*/
.fancy-hero-seven {
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  text-align: center;
  padding: 200px 0 170px;
}
.fancy-hero-seven:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}
.fancy-hero-seven h2 {
  font-size: 72px;
  font-weight: 500;
  color: #fff;
  letter-spacing: -1px;
}
.fancy-hero-seven p {
  font-size: 22px;
  color: #fff;
  line-height: 1.75em;
  padding-top: 25px;
}
/*-------------------- Theme Menu One/Center White ---------------------*/
.theme-menu-one.center-white {background: transparent;}
.theme-menu-one.center-white.sticky-menu.fixed {background: #161616;}
.theme-menu-one.center-white .navbar-nav .nav-item .nav-link {
  font-size: 18px;
  margin: 0 25px;
  color: #fff;
}
.theme-menu-one.center-white .logo {
  position: absolute;
  left: 0;
  top:50%;
  transform: translateY(-50%);
}
.theme-menu-one.center-white .right-button-group {
  position: absolute;
  right: 0;
  top:50%;
  margin: 0;
  transform: translateY(-50%);
}
.theme-menu-one.center-white .navbar {position: static;}
.theme-menu-one.center-white .right-button-group a {color: #fff;}
/*-------------------- Fancy Feature Thirty Three --------------------*/
.fancy-feature-thirtyThree .contact-title {
  font-size: 58px;
  letter-spacing: -1px;
  font-weight: 500;
}
.block-style-thirtyFour {
  position: relative;
  overflow: hidden;
  margin-top: 35px;
}
.block-style-thirtyFour .hover-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 90px 35px 30px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
  z-index: 1;
}
.block-style-thirtyFour .hover-content h4 a {
  font-size: 24px;
  font-weight: 500;
  color: #fff;
  line-height: 1.45em;
  letter-spacing: -0.5px;
}
.block-style-thirtyFour .hover-content .arrow {
  font-size: 38px;
  color: #fff;
  margin-top: 20px;
}
.block-style-thirtyFour:hover .hover-content h4 a {text-decoration: underline;}
.block-style-thirtyFour:hover>img {transform: scale3d(1.1, 1.1, 1.0);}
/*-------------------- Service Details One --------------------*/
.service-details-one .details-wrapper h2 {
  font-size: 58px;
  line-height: 1.29em;
  font-weight: 500;
  letter-spacing: -0.5px;
}
.service-details-one .details-wrapper .sub-heading {
  font-size: 24px;
  line-height: 1.91em;
  color: #000;
}
.service-details-one .details-wrapper .img-meta {border-radius: 15px;}
.service-details-one .details-wrapper p {line-height: 34px;}
.service-details-one .details-wrapper h4 {
  font-size: 24px;
  font-weight: 500;
  padding-bottom: 22px;
}
.service-details-one .details-wrapper .list-item-one li {
  margin-bottom: 12px;
  padding-left: 32px;
  position: relative;
}
.service-details-one .details-wrapper .list-item-one li:before {
  content: url(../images/icon/189.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.service-details-one .details-wrapper h3 {
  font-size: 36px;
  font-weight: 700;
  letter-spacing: -0.5px;
}
.service-details-one .accordion-style-three .card .card-header button {
  font-family: 'Inter';
  font-weight: 500;
  font-size: 22px;
  padding: 30px 35px 30px 0;
}
.service-details-one .accordion-style-three .card:first-child .card-header button {border-top: 1px solid #E5E5E5;}
.service-details-one .accordion-style-three .card {border-bottom: 1px solid #E5E5E5;}
.service-details-one .accordion-style-three .card .card-body {border-bottom: 0;}
.service-details-one .sidebar .category-list {
  border: 1px solid #EEEEEE;
  border-radius: 10px;
  padding: 32px 5px 30px 35px;
}
.service-details-one .sidebar h4 {
  font-size: 26px;
  font-weight: 500;
  padding-bottom: 25px;
}
.service-details-one .sidebar .category-list a {
  font-family: 'Inter';
  line-height: 42px;
  color: #000000;
  transition: all 0.3s ease-in-out;
}
.service-details-one .sidebar .category-list a:hover,
.service-details-one .sidebar .category-list a.active {color: var(--blue-dark);}
.service-details-one .sidebar .sidenote {
  background: var(--blue-dark);
  border-radius: 10px;
  padding: 60px 25px 25px;
  margin: 50px 0 48px;
  text-align: center;
}
.service-details-one .sidebar .sidenote p {
  color: #fff;
  position: relative;
  margin-bottom: 5px;
}
.service-details-one .sidebar .sidenote p:before {
  content: '“';
  font-family: 'Inter';
  font-size: 80px;
  color: #fff;
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}
.service-details-one .sidebar .sidenote span {color: #fff; font-size: 17px;}
.service-details-one .sidebar .social-icon li a {
  font-size: 20px;
  color: #BCBCBC;
  margin-right: 20px;
  transition: all 0.3s ease-in-out;
}
.service-details-one .sidebar .social-icon li a:hover {color: #000;}
/*=======================================================================
                              Form Survey      
=========================================================================*/
/*---------------------Theme Hero Banner / Thirteen ---------------*/
.hero-banner-thirteen {
  position: relative;
  padding: 250px 0 0;
  z-index: 2;
}
.hero-banner-thirteen:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 67%;
  top: 0;
  left: 0;
  background: url(../images/shape/bg8.svg) no-repeat center bottom;
  background-size: cover;
  z-index: -1;
}
.hero-banner-thirteen .slogan {
  display: inline-block;
  line-height: initial;
  border-radius: 20px;
  padding: 4px 18px 4px 15px;
  border: 1px solid #000;
  font-size: 16px;
  color: #000;
}
.hero-banner-thirteen .slogan strong {font-weight: 500;}
.hero-banner-thirteen .slogan i {
  font-size: 12px;
  display: inline-block;
}
.hero-banner-thirteen .slogan:hover {background: #000; color: #fff;}
.hero-banner-thirteen .hero-heading {
  font-weight: 500;
  font-size: 90px;
  line-height:1.11em;
  letter-spacing: -2px;
  margin: 45px 0 50px;
}
.hero-banner-thirteen .hero-sub-heading {
  font-size: 24px;
  line-height: 1.83em;
  color: #000;
  margin-bottom: 50px;
}
.hero-banner-thirteen .info {
  text-align: center;
  font-size: 15px;
  color: #858585;
  margin-top: 15px;
  width: 235px;
}
.hero-banner-thirteen .screen-holder {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 41%;
}
.hero-banner-thirteen .screen-holder .shape-one {
  top: -13%;
  left: -17.5%;
  width: 41.02%;
  z-index: 1;
  animation: jump10pRsv 3s infinite linear;
}
.hero-banner-thirteen .screen-holder .shape-two {
  top: -1%;
  right: -1.5%;
  width: 74.4%;
  z-index: 1;
  animation: jump10p 3s infinite linear;
}
/*-------------------- Fancy Feature Thirty Four --------------------*/
.block-style-thirtyFive .icon {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  margin: 0 auto;
}
.block-style-thirtyFive h4 {
  font-size: 22px;
  font-weight: 500;
  margin: 25px 0 15px;
}
.block-style-thirtyFive p {line-height: 28px;}
/*-------------------- Block Style Thirty Six --------------------*/
.block-style-thirtySix .tag-line {
  font-size: 22px;
  line-height: 1.72em;
  color: #000;
  margin: 30px 0 40px;
}
.block-style-thirtySix .list-item li {
  color: #474747;
  position: relative;
  padding-left: 30px;
  margin-bottom: 20px;
}
.block-style-thirtySix .list-item li:before {
  content: url(../images/icon/76.svg);
  position: absolute;
  top: 0;
  left: 0;
}
.block-style-thirtySix .illustration-holder {box-shadow: 0px 20px 40px rgba(0, 61, 132, 0.03); z-index: -1;}
.block-style-thirtySix .illustration-holder .shape-one {
  top: -10%;
  right: -24%;
  width: 72.2%;
  z-index: 1;
  animation: jump10pRsv 3s infinite linear;
}
.block-style-thirtySix .illustration-holder .shape-two {
  bottom: 6.5%;
  left: -20%;
  width: 77.8%;
  z-index: 1;
  animation: jump10p 3s infinite linear;
}
.block-style-thirtySix .illustration-holder .shape-three {
  bottom: -10%;
  left: -2%;
  width: 73.4%;
  z-index: 1;
}
.block-style-thirtySix .illustration-holder-two {z-index: -1;}
.block-style-thirtySix .illustration-holder-two .shape-one {
  top: -12.5%;
  left: -5%;
  width: 86.2%;
  z-index: 1;
  animation: jump10p 3s infinite linear;
}
/*-------------------- Fancy Feature Thirty Five --------------------*/
.fancy-feature-thirtyFive .slider-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -11vw;
  width: 72vw;
  margin: 0 -24px;
}
.portfolio_slider_two {margin: 0 -20px;}
.portfolio_slider_two .item {margin: 0 20px;}
.fancy-feature-thirtyFive .card-block {
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}
.fancy-feature-thirtyFive .card-block .hover {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.92);
  opacity: 0;
  visibility: hidden;
}
.fancy-feature-thirtyFive .card-block:hover .hover {opacity: 1; visibility: visible;}
.fancy-feature-thirtyFive .card-block a {
  margin: 5px 0;
  width: 120px;
  font-weight: 500;
  font-size: 16px;
  border-radius: 30px;
  text-align: center;
}
.fancy-feature-thirtyFive .card-block .edit {
  background: #000;
  line-height: 45px;
  color: #fff;
}
.fancy-feature-thirtyFive .card-block .view {
  background: #fff;
  line-height: 38px;
  color: #000;
  border: 2px solid #000;
}
.fancy-feature-thirtyFive .card-block .edit:hover {background: #6F6CFF;}
.fancy-feature-thirtyFive .card-block .view:hover {background: #6F6CFF; border-color: #6F6CFF; color: #fff;}
.fancy-feature-thirtyFive .slider-arrows li {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  color: #000;
  font-size: 25px;
  margin-right: 8px;
  cursor: pointer;
  transform: scale(0.8);
  transition: all 0.3s ease-in-out;
}
.fancy-feature-thirtyFive .slider-arrows li:first-child i {transform: rotate(-180deg); display: inline-block;}
.fancy-feature-thirtyFive .slider-arrows li:hover {
  background: #353535;
  color: #fff; 
  transform: scale(1);
}
/*-------------- Client Feedback Slider Eight ----------------*/
.client-feedback-slider-eight .feedback-wrapper {padding: 50px 0;}
.client-feedback-slider-eight {background: #7241FF;}
.client-feedback-slider-eight .tag {
  display: inline-block;
  font-size: 13px;
  color: #fff;
  letter-spacing: 2px;
  text-transform: uppercase;
  line-height: 33px;
  padding: 0 15px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.13);
}
.client-feedback-slider-eight p {
  font-size: 42px;
  line-height: 1.54em;
  color: #fff;
  letter-spacing: -1px;
  margin: 35px 0 50px;
}
.client-feedback-slider-eight .name {
  font-size: 26px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 5px;
}
.client-feedback-slider-eight .cr-position {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.6);
}
.client-feedback-slider-eight .slider-arrow {
  width: 160px;
  height: 65px;
  border-radius: 10px;
  background: #FFD88C;
  position: absolute;
  bottom: 16%;
  left: calc(50% - 62px);
  z-index: 1;
}
.client-feedback-slider-eight .slider-arrow li {
  width: 50%;
  line-height: 65px;
  text-align: center;
  cursor: pointer;
  font-size: 35px;
  color: #000;
}
.client-feedback-slider-eight .slider-arrow li i {transition: all 0.3s ease-in-out;transform: scale(0.8); display: inline-block;}
.client-feedback-slider-eight .slider-arrow li:first-child {transform: rotate(180deg); border-left: 1px solid #EFC36F;}
.client-feedback-slider-eight .slider-arrow li:hover i {transform: scale(1);}
/*-------------- Pricing Section Nine ----------------*/
.pricing-section-nine .pr-table-wrapper {
  border: 1px solid #ECECEC;
  border-radius: 20px;
  padding: 35px 45px;
}
.pricing-section-nine .pr-table-wrapper .pack-name {
  font-size: 28px;
  font-weight: 500;
  color: #000;
}
.pricing-section-nine .pr-table-wrapper .pack-details {
  font-size: 17px;
  line-height: 28px;
  color: #878787;
  padding: 9px 50px 30px 0;
}
.pricing-section-nine .pr-table-wrapper .price {
  font-size: 42px;
  letter-spacing: -1px;
  padding-right: 25px;
  font-weight: 500;
  color: #000;
}
.pricing-section-nine .pr-table-wrapper .top-banner span {
  font-size: 22px;
  color: #000;
  display: block;
  margin-bottom: -5px;
}
.pricing-section-nine .pr-table-wrapper .top-banner em {
  font-size: 14px;
  color: #9B9B9B;
  display: block;
  font-style: normal;
}
.pricing-section-nine .pr-table-wrapper .trial-button {
  font-size: 16px;
  color: #fff;
  display: block;
  text-align: center;
  line-height: 55px;
  border-radius: 5px;
  background: #6F6CFF;
  margin: 35px 0 40px;
}
.pricing-section-nine .pr-table-wrapper .trial-button:hover {background: #212121;}
.pricing-section-nine .pr-table-wrapper .pr-feature li {
  position: relative;
  line-height: 42px;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 30px;
  margin-left: 10px;
}
.pricing-section-nine .pr-table-wrapper .pr-feature li:before {
  content: url(../images/icon/76.svg);
  position: absolute;
  top: 0;
  left: 0;
}
.pricing-section-nine .pr-table-wrapper .trial-text {
  font-size: 16px; 
  padding-top: 30px;
  color: rgba(0, 0, 0, 0.4);
}
/*-------------------- Feature Blog Five -----------------*/
.feature-blog-five .post-meta {width: 100%;}
.feature-blog-five .post-data {padding: 25px 35px 0 0;}
.feature-blog-five .post-meta .post-img {
  border-radius: 10px; 
  overflow: hidden; 
  background: #212121;
  position: relative;
}
.feature-blog-five .post-meta .blog-title h5 {
  font-size: 24px;
  font-weight: 500;
  color: var(--heading);
  line-height: 1.44em;
  letter-spacing: -0.5px;
  margin: 15px 0 10px;
  transition: all 0.3s ease-in-out;
}
.feature-blog-five .post-meta .blog-title:hover h5 {color: #6F6CFF;}
.feature-blog-five .post-meta .read-btn {font-size: 17px;color: rgba(0, 0, 0, 0.5);}
.feature-blog-five .post-meta .read-btn:hover {text-decoration: underline; color: #000;}
/*------------------ Fancy Short Banner Fifteen ---------------*/
.fancy-short-banner-fifteen {
  background: #FFFCF6;
  padding: 75px 0 100px;
}
/*^^^^^^^^^^^^^^^^^^^^^ Footer Style Nine ^^^^^^^^^^^^^^^^^^^^^^^^*/
.theme-footer-nine {padding: 45px 0;}
.theme-footer-nine .footer-nav a {
  color: #151515;
  font-size: 18px;
  margin: 3px 1.6vw;
  transition: all 0.3s ease-in-out;
}
.theme-footer-nine .social-icon a {
  font-size: 22px;
  margin-left: 25px;
  color: #000;
  transition: all 0.3s ease-in-out;
}
.theme-footer-nine .footer-nav a:hover,
.theme-footer-nine .social-icon a:hover {color: #6F6CFF;}
/*=======================================================================
                              VR Landing     
=========================================================================*/
/*---------------------Theme Hero Banner / Fourteen ---------------*/
.hero-banner-fourteen {
  position: relative;
  padding: 200px 0 130px;
  z-index: 2;
}
.hero-banner-fourteen .hero-heading {
  font-size: 80px;
  line-height: 1.25em;
  text-transform: uppercase;
  font-weight: 700;
}
.hero-banner-fourteen .hero-sub-heading {
  font-size: 24px;
  line-height: 1.83em;
  color: #000;
  margin: 25px 0 38px;
}
.hero-banner-fourteen .info {
  font-size: 24px;
  font-weight: 500;
  color: #000;
}
.hero-banner-fourteen .info>span {
  font-size: 0.83em;
  font-weight: normal;
  margin-top: 12px;
}
.hero-banner-fourteen .info>span span {opacity: 0.35;}
.hero-banner-fourteen .price {
  font-size: 100px;
  font-weight: 500;
  color: #000;
  line-height: initial;
}
.hero-banner-fourteen .explore-btn {
  font-size: 18px;
  font-weight: 500;
  color: #FF57C5;
  position: relative;
}
.hero-banner-fourteen .explore-btn:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  left: 0;
  bottom: 0;
  background: #FF57C5;
}
.hero-banner-fourteen .screen-holder {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 49%;
}
.hero-banner-fourteen .screen-holder .vr-image {
  bottom: 0;
  right: 8%;
  z-index: 1;
}
.hero-banner-fourteen .screen-holder .shape-one {
  top: -48%;
  right: 17%;
}
.hero-banner-fourteen .screen-holder .shape-two {
  top: -32%;
  right: 13%;
}
.hero-banner-fourteen .screen-holder .shape-three {
  top: -58%;
  right: 8%;
}
.hero-banner-fourteen .screen-holder .rating-box {
  position: absolute;
  z-index: 1;
  left: -10%;
  bottom: -20%;
  text-align: center;
  width: 18.76%;
}
.hero-banner-fourteen .screen-holder .rating-box img {animation: rotated 60s infinite linear;}
.hero-banner-fourteen .screen-holder .rating-box .rate {
  color: #000;
  font-size: 24px;
  font-weight: 500;
  line-height: 1em;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50% , -50%);
}
.hero-banner-fourteen .screen-holder .rating-box .rate span {font-size: 0.6em; text-transform: uppercase;}
/*-------------------- Fancy Feature Thirty Six --------------------*/
.fancy-feature-thirtySix {
  background: #2B0937;
}
.block-style-thirtySeven {
  background: rgba(255, 255, 255, 0.08);
  text-align: center;
  padding: 30px 15px 25px;
}
.block-style-thirtySeven .img-meta {height: 120px;}
.block-style-thirtySeven .p-title a,
.block-style-thirtySeven .price {
  font-weight: 500;
  font-size: 24px;
  color: #fff;
}
.block-style-thirtySeven .rating li {
  color: #FFB941;
  font-size: 16px;
  margin: 0 5px;
}
.block-style-thirtySeven:hover .p-title a {text-decoration: underline;}
.product_slider_one .item {margin: 0 15px;}
.fancy-feature-thirtySix .slider-arrows {
  position: absolute;
  left: 13%;
  bottom: 0;
}
.fancy-feature-thirtySix .slider-arrows li {
  color: #fff;
  font-size: 25px;
  margin-left: 12px;
  cursor: pointer;
  transform: scale(0.8);
  transition: all 0.3s ease-in-out;
}
.fancy-feature-thirtySix .slider-arrows li:first-child i {transform: rotate(-180deg); display: inline-block;}
.fancy-feature-thirtySix .slider-arrows li:hover {
  color: #FF57C5; 
  transform: scale(1);
}
/*-------------------- Fancy Feature Thirty Seven --------------------*/
.fancy-feature-thirtySeven .rating li {
  font-size: 17px;
  color: #000000;
  margin: 0 2px;
}
.fancy-feature-thirtySeven .rating-count {
  font-weight: 500;
  color: #000;
  text-transform: uppercase;
  margin-left: 8px;
}
.fancy-feature-thirtySeven .text-lg {
  font-size: 24px;
  line-height: 45px;
  color: #000;
}
/*-------------------- Fancy Feature Thirty Eight -------------------*/
.counter-box-six .icon {height: 45px;}
.counter-box-six .number {font-weight: 500; font-size: 48px; margin: 26px 0 6px;}
.counter-box-six .number small {font-size: 0.41em; letter-spacing: -0.5px;}
/*-------------------- Fancy Feature Thirty Nine -------------------*/
.fancy-feature-thirtyNine {
  background: #2B0937;
  z-index: 5;
  padding: 100px 0 165px;
  overflow: hidden;
}
.fancy-feature-thirtyNine .title {
  font-size: 90px;
  line-height: 1.16em;
  letter-spacing: -1px;
  font-weight: bold;
  color: #fff;
  text-transform: uppercase;
}
.fancy-feature-thirtyNine .title span {color: #FFC765;}
@keyframes ripple {
  0% {
    box-shadow: 0 0 0 .1rem rgba(255,255,255, 0.06);
  }
  100% {
    box-shadow: 0 0 0 3rem rgba(255,255,255, 0);
  }
}
.fancy-feature-thirtyNine .video-icon .icon {
  display: block; 
  position: relative;
  border-radius: 50%;
  animation: ripple 3s linear infinite;
}
.fancy-feature-thirtyNine .video-icon .icon::before,
.fancy-feature-thirtyNine .video-icon .icon::after{
  content:"";
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;
  border-radius: 50%;
  animation:inherit;
  animation-delay:1s;
}
.fancy-feature-thirtyNine .video-icon .icon::after {
  animation-delay:3s;
}
.fancy-feature-thirtyNine .video-icon i {
  width: 90px;
  height: 90px;
  color: #2B0937;
  text-align: center;
  line-height: 90px;
  border-radius: 50%;
  font-size: 35px;
  padding-left: 5px;
  transition: all 0.3s ease-in-out;
  background: #FFC765;
}
.fancy-feature-thirtyNine .video-icon .ps-text {
  font-size: 13px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #fff;
  margin-bottom: 18px;
}
.fancy-feature-thirtyNine .video-icon strong {
  font-size: 24px;
  color: #fff;
  font-weight: normal;
}
.fancy-feature-thirtyNine .video-icon:hover i {
  background: #fff;
  color: #2B0937;
}
.fancy-feature-thirtyNine .img-meta {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 69%;
  z-index: -1;
}
.fancy-feature-thirtyNine .img-meta:before {
  content: '';
  position: absolute;
  width: 910px;
  height: 910px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.04);
  top: 50%;
  left: -7%;
  transform: translateY(-50%);
  z-index: -1;
}
.fancy-feature-thirtyNine .img-meta:after {
  content: '';
  position: absolute;
  width: 655px;
  height: 655px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.07);
  top: 50%;
  left: 2%;
  transform: translateY(-50%);
  z-index: -1;
}
/*-------------------- Fancy Feature Forty -------------------*/
.block-style-thirtyEight p {font-size: 20px; line-height: 38px;}
.block-style-thirtyEight .img-meta .shape-one {
  top: -15%;
  right: 8%;
  z-index: 1;
  width: 25%;
}
.block-style-thirtyEight .img-meta .shape-two {
  bottom: -16%;
  left: -21%;
  z-index: 1;
  width: 50%;
}
.block-style-thirtyEight .img-meta .shape-three {
  bottom: -16%;
  right: -23%;
  z-index: 1;
  width: 43.1%;
}
/*-------------------- Client Feedback Slider Nine -------------------*/
.client-feedback-slider-nine {
  background: #2B0937;
  z-index: 5;
  padding: 95px 0 60px;
  overflow: hidden;
}
.client-feedback-slider-nine .shape-one {
  left: 4%;
  top: 9%;
}
.clientSliderEight .bg-wrapper {
  background: #fff;
  padding: 35px 40px 25px;
}
.clientSliderEight .bg-wrapper .avatar {width: 50px; height: 50px;}
.clientSliderEight .bg-wrapper .name {
  font-size: 20px;
  color: #000;
  font-weight: 500;
}
.clientSliderEight .bg-wrapper .region {color: #acacac;}
.clientSliderEight .bg-wrapper p {line-height: 32px; color: #000;}
.clientSliderEight .bg-wrapper .rating li {
  font-size: 17px;
  color: #FFC12B;
  margin-right: 5px;
}
.clientSliderEight .item {margin: 0 15px;}
.clientSliderEight .slick-dots {text-align: center;}
.clientSliderEight .slick-dots li {
  display: inline-block;
  margin: 70px 5px 0;
}
.clientSliderEight .slick-dots button {
  text-indent: -550000px;
  width: 9px;
  height: 9px;
  background: rgba(255,255,255,0.15);
  border-radius: 50%;
}
.clientSliderEight .slick-dots li.slick-active button {background: #fff;}
/*-------------------- Fancy Feature Forty One -------------------*/
.fancy-feature-fortyOne p {font-size: 20px; color: #000; line-height: 2em;}
.fancy-feature-fortyOne .buy-btn {
  line-height: 53px;
  background: #FFE06B;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 1px;
  color: #000;
  padding: 0 42px;
}
.fancy-feature-fortyOne .buy-btn:hover {background: #2B0937; color: #fff;}
/*=======================================================================
                              E-Commerce     
=========================================================================*/
/*-------------------- Menu ---------------*/
.theme-main-menu.sticky-menu.fixed.theme-menu-eight {padding-top: 10px; padding-bottom: 10px;}
.theme-menu-eight .signIn-action {
  font-family: 'Inter';
  font-weight: 500;
  color: #000;
}
.theme-menu-eight .cart-group-wrapper .dropdown-toggle:after {display: none;}
.theme-menu-eight .cart-group-wrapper .dropdown-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin: 0 28px 0 32px;
  position: relative;
  outline: none;
  box-shadow: none;
  transition: all 0.3s ease-in-out;
}
.theme-menu-eight .cart-group-wrapper .dropdown-toggle:hover {background: rgba(0, 0, 0, 0.04);}
.theme-menu-eight .cart-group-wrapper .item-count {
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #000;
  line-height: 16px;
  text-align: center;
  font-size: 9px;
  font-weight: 500;
  color: #fff;
  right: 2px;
  top:0px;
}
.theme-main-menu .cart-group-wrapper .dropdown-menu {
  width: 280px;
  background: #fff;
  border: none;
  border-radius: 0;
  padding: 25px 20px 30px;
  box-shadow: 0px 25px 50px rgba(213, 216, 223, 0.5);
}
.theme-menu-eight .cart-group-wrapper .item-img {
  width: 70px;
  height: 75px;
  padding: 5px;
  background: #F5F5F5;
}
.theme-menu-eight .cart-group-wrapper .item-info {
  width: calc(100% - 70px);
  padding-left: 20px;
  position: relative;
}
.theme-menu-eight .cart-group-wrapper .item-info .name {
  font-size: 16px;
  color: #1f1f1f;
  font-weight: 500;
}
.theme-menu-eight .cart-group-wrapper .item-info .name:hover {text-decoration: underline;}
.theme-menu-eight .cart-group-wrapper .item-info .price {
  font-weight: 500;
  font-size: 16px;
  color: #000;
  letter-spacing: -0.5px;
}
.theme-menu-eight .cart-group-wrapper .item-info .price .quantity {
  font-size: 16px;
  color: rgba(31,31,31,0.5);
  margin-left: 7px;
}
.theme-menu-eight .cart-group-wrapper .selected-item {
  border-bottom: 1px solid #EEEEEE;
  padding-bottom: 22px;
  margin-bottom: 18px;
}
.theme-menu-eight .cart-group-wrapper .subtotal .title {font-size: 16px; font-weight: 500; color: #1f1f1f}
.theme-menu-eight .cart-group-wrapper .subtotal .total-price {
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0px;
  color: #000;
}
.theme-menu-eight .cart-group-wrapper .subtotal {padding-bottom: 7px;}
.theme-menu-eight .cart-group-wrapper .button-group a {
  font-weight: 500;
  display: block;
  line-height: 43px;
  border: 1px solid #dbdbdb;
  text-align: center;
  font-size: 14px;
  text-transform: uppercase;
  color: #1f1f1f;
  margin-top: 15px;
  transition: all 0.3s ease-in-out;
}
.theme-menu-eight .cart-group-wrapper .button-group a:hover {color: #fff; background: #212121;}
/*------------------- Sidebar Nav ---------------*/
.main-sidebar-nav {
  width: 400px;
  border: none;
  height: 100%;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99990;
  background: #FBF3EC;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
}
.main-sidebar-nav.show {transform: translateX(0);}
.main-sidebar-nav .offcanvas-header {padding: 30px 20px 60px;}
.main-sidebar-nav .offcanvas-header .close-btn {font-size: 24px;}
.main-sidebar-nav .offcanvas-header .close-btn:hover {color: var(--blue-dark);}
.main-sidebar-nav .navbar-nav .nav-item .nav-link {
  margin: 0;
  padding: 10px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.07);
}
.main-sidebar-nav .navbar-nav .nav-item .nav-link:hover,
.main-sidebar-nav .navbar-nav .nav-item.show .nav-link {color: var(--blue-dark);}
.main-sidebar-nav .navbar-nav .nav-item .nav-link::after {
  content: "+";
  border:none !important;
  font-size: 20px;
  position: absolute;
  right: 10px;
  top:0;
  color: inherit;
  line-height: 51px;
  transition: all 0.3s ease-in-out;
}
.main-sidebar-nav .show.dropdown .dropdown-toggle::after {content: "-"; transform: rotate(180deg);}
.main-sidebar-nav .dropdown-menu .dropdown-item {
  font-size: 17px;
  line-height: 45px;
  text-transform: capitalize;
  color: var(--heading);
  padding: 0 5px 0 25px;
  position: relative;
  z-index: 1;
  transition: all 0.2s ease-in-out;
  white-space: inherit;
  background: transparent;
}
.main-sidebar-nav .dropdown-menu .dropdown-item:hover {color: var(--blue-dark);}
.main-sidebar-nav .navbar-nav .dropdown-menu {
  position: static !important;
  z-index: 5;
  background-color: #fff;
  border-radius: 0;
  display: none;
  right: 0;
  left: 0;
  padding: 0;
  top: 100%;
  transform: none !important;
  width: 100%;
  min-width: 250px;
  box-shadow: 0px 30px 70px 0px rgba(137,139,142,0.15);
  margin: 0;
  border: none;
  transform-origin: 0 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.main-sidebar-nav .navbar-nav .dropdown-menu.show {display: block;}
.main-sidebar-nav .mega-menu {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0 5px;
}
.main-sidebar-nav .mega-menu li {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
  padding: 10px 10px 0;
}
.main-sidebar-nav .mega-menu li .img-box {
  padding: 0;
  background: #fff;
  position: relative;
}
.main-sidebar-nav .mega-menu li span {
  display: block;
  text-align: center;
  font-size: 16px;
  color: #000;
  margin: 15px 0 20px;
  line-height: initial;
}
.main-sidebar-nav .mega-menu li .img-box:hover span {text-decoration: underline;}
.main-sidebar-nav .mega-menu li .img-box img {border: 1px solid rgba(0, 0, 0, 0.05);}
.main-sidebar-nav .mega-menu li .img-box .hover {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  top: 0;
  left: 0;
  font-size: 24px;
  color: #fff;
}
/*---------------------Theme Hero Banner / Fifteen ---------------*/
.hero-banner-fifteen {
  padding: 200px 0 170px;
  position: relative;
  background: radial-gradient(53.3% 53.3% at 50% 46.7%, #FFFFFF 0%, #FFF8EA 99.81%);
}
.hero-banner-fifteen .hero-heading {
  font-size: 140px;
  line-height: 0.92em;
}
.hero-banner-fifteen .hero-sub-heading {
  font-size: 24px;
  line-height: 1.75em;
  color: #000;
  padding: 30px 0 45px;
}
.hero-banner-fifteen .shop-btn {
  font-size: 15px;
  font-weight: 500;
  line-height: 52px;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #fff;
  background: #161616;
  padding: 0 40px;
}
.hero-banner-fifteen .shop-btn:hover {background: var(--purple-blue);}
.hero-banner-fifteen .cart-btn {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  border: 1px solid #161616;
  line-height: 50px;
  text-align: center;
  font-size: 40px;
  color: #161616;
}
.hero-banner-fifteen .cart-btn:hover {background: #161616; color: #fff;}
.hero-banner-fifteen .product-img-holder {
  width: 680px;
  height: 680px;
  border-radius: 50%;
  background: #FFD583;
  position: absolute;
  top: -2%;
  right: -2%;
}
.hero-banner-fifteen .product-img-holder .product-img {
  max-width: initial;
  position: relative;
  top: 45%;
  left: 50%;
  transform: translate(-50% , -50%);
}
.hero-banner-fifteen .offer-sticker {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  right: 15px;
  bottom: 15px;
  color: #000;
  font-style: italic;
  z-index: 1;
}
.hero-banner-fifteen .offer-sticker:before {
  content: '';
  position: absolute;
  top: 10px;
  right: 10px;
  bottom: 10px;
  left: 10px;
  border-radius: 50%;
  border: 1px solid #000;
  z-index: -1;
}
.hero-banner-fifteen .offer-sticker .sn1 {font-size: 32px;}
.hero-banner-fifteen .offer-sticker .sn2 {font-size: 22px; padding-top: 8px;}
/*---------------------Fancy Feature Forty Two ---------------*/
.xl-container {
  max-width: 1720px;
  padding-left: 15px;
  padding-right: 15px;
  margin-left: auto;
  margin-right: auto;
}
.SC_block_one {
  width: 370px;
  height: 370px;
  border-radius: 50%;
  position: relative;
  z-index: 1;
  margin: 40px auto 0;
  overflow: hidden;
  background-size: cover;
  background-position: center;
}
.SC_block_one:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 50%;
  background: rgba(11, 10, 10, 0.25);
  z-index: -1;
}
.SC_block_one h3 {
  font-size: 36px;
  color: #fff;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 20px;
  transform: translateY(43px);
}
.SC_block_one .sp-now-btn {
  width: 135px;
  line-height: 43px;
  border: 1px solid #fff;
  border-radius: 5px;
  font-weight: 500;
  font-size: 15px;
  color: #fff;
  text-align: center;
  opacity: 0;
  transform: translateY(45px);
}
.SC_block_one .sp-now-btn:hover {background: var(--purple-blue); border-color: var(--purple-blue);}
.SC_block_one:hover h3 {transform: translateY(0);}
.SC_block_one:hover .sp-now-btn {transform: translateY(0); opacity: 1;}
.product_slider_space {margin: 0 -20px;}
.product_slider_space .item {margin: 0 20px;}
.product-block-one .img-holder {
  position: relative;
  background: #F5F5F5;
  height: 470px;
  z-index: 5;
  margin-bottom: 22px;
}
.product-block-one .img-holder.style-two {height: auto;}
.product-block-one .img-holder .tag-one {
  text-transform: uppercase;
  color: #000;
  font-size: 12px;
  letter-spacing: 0px;
  padding: 0 14px;
  line-height: 25px;
  border-radius: 3px;
  background: #fff;
  position: absolute;
  right: 20px;
  bottom: 20px;
}
.product-block-one .img-holder .cart-icon {
  display: block;
  width: 35px;
  line-height: 35px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.09);
  border-radius: 50%;
  background: #fff;
  color: var(--purple-blue);
  text-align: center;
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 18px;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: scale(0.5);
}
.product-block-one .img-holder .cart-icon:hover {color: #fff; background: var(--purple-blue);}
.product-block-one .img-holder .cart-button {
  font-size: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
  line-height: 48px;
  background: #1F1F1F;
  text-align: center;
  color: #fff;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.4s ease-in-out;
}
.product-block-one:hover .img-holder .cart-button {opacity: 1; transform: translateY(0);}
.product-block-one .product-title {
  letter-spacing: 0px;
  color: #9F9F9F;
  font-size: 18px;
  transition: all 0.3s ease-in-out;
}
.product-block-one .rating li {
  font-size: 14px; 
  margin-left: 5px;
  color: #B3B3B3;
}
.product-block-one .rating li .fa-star {color: #FFCB65;}
.product-block-one .price {font-size: 24px; color: #000; margin-top: 10px;}
.product-block-one:hover .product-img {transform: scale(0.95);}
.product-block-one:hover .cart-icon {opacity: 1; transform: scale(1);}
.product-block-one:hover .product-title {color: #000; text-decoration: underline;}
/*---------------------Fancy Feature Forty Two ---------------*/
.flash-sale-banner .banner-bg {
  background: #FFF9EB;
  text-align: center;
}
.flash-sale-banner .right-half {padding: 160px 15px; z-index: 1;}
.flash-sale-banner .right-half:before {
  content: '';
  position: absolute;
  top: 22px;
  right: 22px;
  bottom: 22px;
  left: 0;
  border: 1px solid #000;
  border-left: none;
  z-index: -1;
}
.flash-sale-banner .right-half .fancy-text {
  font-size: 50px;
  color: #FF0000;
}
.flash-sale-banner .right-half .sale-text {
  font-size: 18px;
  font-weight: 500;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 1.62px;
  margin: 15px 0 16px;
}
.flash-sale-banner .right-half h3 {font-size: 42px; line-height: 1.166em; font-weight: 500;}
.flash-sale-banner .left-half {
  background: url(../images/media/img_125.jpg) no-repeat center;
  background-size: cover;
  position: relative;
  z-index: 1;
}
.flash-sale-banner .left-half:before {
  content: '';
  position: absolute;
  top: 22px;
  right: 0;
  bottom: 22px;
  left: 22px;
  border: 1px solid #fff;
  border-right: none;
}
/*---------------------Fancy Feature Forty Three ---------------*/
.fancy-feature-fortyThree {background: #F5F5F5;}
.product-block-two {
  background: #fff;
  padding: 30px 15px 40px;
  text-align: center;
}
.product-block-two .img-holder {
  position: relative;
  height: 348px;
  z-index: 5;
  margin-bottom: 26px;
}
.product-block-two .img-holder img {max-height: 100%;}
.product-block-two .img-holder .cart-icon {
  display: block;
  width: 35px;
  line-height: 35px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.09);
  border-radius: 50%;
  background: #000;
  color: #fff;
  text-align: center;
  position: absolute;
  right: 20px;
  top: 0;
  font-size: 18px;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: scale(0.5);
}
.product-block-two .img-holder .cart-icon:hover {color: #fff; background: var(--purple-blue);}
.product-block-two .product-title {
  letter-spacing: 0px;
  color: #9F9F9F;
  font-size: 17px;
  margin: 5px 0 10px;
  transition: all 0.3s ease-in-out;
}
.product-block-two .rating li {
  font-size: 14px; 
  margin: 0 3px;
  color: #B3B3B3;
}
.product-block-two .rating li .fa-star {color: #FFCB65;}
.product-block-two .price {font-size: 24px; color: #000;}
.product-block-two:hover .product-img {transform: scale(0.95);}
.product-block-two:hover .cart-icon {opacity: 1; transform: scale(1);}
.product-block-two:hover .product-title {color: #000; text-decoration: underline;}
/*--------------------- Shop Discount Subscription ---------------*/
.shop-discount-subscription .form-wrapper {max-width: 550px; margin-left: auto;}
.shop-discount-subscription .form-wrapper form {position: relative; height: 75px;}
.shop-discount-subscription .form-wrapper input {
  width: 100%;
  height: 100%;
  border: none;
  color: #000;
  border-bottom: 1px solid #000;
}
.shop-discount-subscription .form-wrapper button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #151515;
  position: absolute;
  right: 0;
  bottom: 15px;
}
.shop-discount-subscription .form-wrapper button:hover {background: var(--purple-blue);}
.shop-discount-subscription .form-wrapper p {font-size: 16px;}
.shop-discount-subscription .form-wrapper p a {font-weight: 500;}
.shop-discount-subscription .form-wrapper::-webkit-input-placeholder {color: #000;}
.shop-discount-subscription .form-wrapper :-ms-input-placeholder {color: #000;}
.shop-discount-subscription .form-wrapper ::placeholder {color: #000;}
.shop-discount-subscription h4 {font-size: 25px; font-weight: 500; letter-spacing: -1px;}
/*--------------------- Fancy Feature Forty Four ---------------*/
.block-style-thirtyNine .icon {
  width: 75px;
  height: 75px;
  border: 1px solid #E0E0E0;
}
.block-style-thirtyNine h3 {
  font-size: 24px; 
  font-weight: 500; 
  letter-spacing: -0.5px;
  margin: 30px 0 16px;
}
.block-style-thirtyNine p {color: #525252; line-height: 28px;}
/*^^^^^^^^^^^^^^^^^^^^^ Product Details One ^^^^^^^^^^^^^^^^^^^^^^^^*/
.product-details-one .product-img-tab {border: none;}
.product-details-one .product-img-tab .nav-link {
  width: 100%;
  background: #F6F6F6;
  border: 1px solid transparent;
  border-radius: 0;
  padding: 2px;
  height: 82px;
  margin-bottom: 12px;
}
.product-details-one .product-img-tab .nav-link img {max-height: 100%;}
.product-details-one .product-img-tab .nav-link.active {
  background: #fff;
  border-color: #000;
}
.product-details-one .product-img-tab-content {background: #F6F6F6;}
.product-details-one .product-info .stock-tag {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: #EAEAEA;
  color: #000;
  border-radius: 3px;
  line-height: 28px;
  padding: 0 13px;
}
.product-details-one .product-info .product-name {
  font-weight: 500;
  font-size: 38px;
  margin: 30px 0 10px;
}
.product-details-one .product-info .rating li {
  font-size: 15px; 
  margin-right: 6px;
  color: #B3B3B3;
}
.product-details-one .product-info .rating li .fa-star {color: #FFCB65;}
.product-details-one .product-info .rating li a {
  font-size: 18px;
  color: #000;
  margin-left: 12px;
}
.product-details-one .product-info .rating li a:hover {text-decoration: underline;}
.product-details-one .product-info .price {
  font-weight: 500;
  font-size: 28px;
  letter-spacing: -1px;
  color: #000;
  padding: 35px 0 5px;
}
.product-details-one .product-info .availability {color: #989CA2; letter-spacing: -0.5px; font-size: 15px;}
.product-details-one .product-info .description-text {
  color: #000;
  line-height: 35px;
  padding: 25px 0 25px;
}
.product-details-one .product-info .product-feature {margin-bottom: 20px;}
.product-details-one .product-info .product-feature li {
  font-size: 20px;
  color: #000;
  margin-bottom: 10px;
  position: relative;
  padding-left: 30px;
}
.product-details-one .product-info .product-feature li:before {
  content: url(../images/icon/207.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.product-details-one .product-info .customize-order h6 {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 25px;
}
.product-details-one .product-info .quantity .button-group {
  border: 1px solid #e3e3e3;
  display: inline-block;
}
.product-details-one .product-info .quantity ul li {
  line-height: 40px;
  max-height: 40px;
}
.product-details-one .product-info .quantity ul li button {
  font-size: 25px;
  color: #C9C9C9;
  background: transparent;
  width: 32px;
}
.product-details-one .product-info .quantity ul .product-value {
  font-size: 18px;
  height: 40px;
  color: #313131;
  max-width: 45px;
  background: transparent;
  border: none;
  text-align: center;
  padding-left: 5px;
}
.product-details-one .product-info .color-custome-input li {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  position: relative;
  margin: 10px 10px 0 0;
}
.product-details-one .product-info .color-custome-input li:last-child {margin-right: 0;}
.product-details-one .product-info .color-custome-input li input[type="radio"] {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 2;
}
.product-details-one .product-info .color-custome-input li label {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  top: 0;
  left: 0;
}
.product-details-one .product-info .color-custome-input li label:before {
  content: '';
  position: absolute;
  top: 4px;
  right: 4px;
  bottom: 4px;
  left: 4px;
  background: #fff;
  z-index: 1;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.25s ease-in-out;
}
.product-details-one .product-info .color-custome-input li input[type="radio"]:checked + label:before {opacity: 1;}
.product-details-one .product-info .size-custome-input li label {
  position: relative;
  width: 40px;
  height: 40px;
  line-height: 38px;
  border: 1px solid #E3E3E3;
  text-align: center;
  font-size: 16px;
  color: #B1B1B1;
  transition: all 0.25s ease-in-out;
}
.product-details-one .product-info .size-custome-input li {position: relative; margin-right: 12px;}
.product-details-one .product-info .size-custome-input li:last-child {margin-right: 0;}
.product-details-one .product-info .size-custome-input li input[type="radio"] {
  position: absolute;
  opacity: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  left: 0;
  cursor: pointer;
}
.product-details-one .product-info .size-custome-input li input[type="radio"]:checked + label {border-color:#000;color: #000;}
.product-details-one .product-info .cart-btn {
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #fff;
  padding: 0 38px;
  text-align: center;
  line-height: 55px;
  background: #161616;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}
.product-details-one .product-info .cart-btn:hover {background: var(--purple-blue);}
.product-details-one .product-info .wishlist-btn {
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  color: var(--heading);
  padding: 0 35px;
  text-align: center;
  letter-spacing: 1px;
  line-height: 55px;
  border: 1px solid #000;
  transition: all 0.3s ease-in-out;
}
.product-details-one .product-info .wishlist-btn:hover {background: var(--purple-blue); border-color:var(--purple-blue); color: #fff;}
.product-details-one .product-review-tab .nav-tabs {border-bottom: 1px solid #EBEBEB;}
.product-details-one .product-review-tab .nav-tabs .nav-link {
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 1px;
  padding: 0 0 20px 0;
  position: relative;
  margin: 0 50px 0 0;
  border: none;
  border-radius: 0;
}
.product-details-one .product-review-tab .nav-tabs .nav-link:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  left: 0;
  bottom: -1px;
  z-index: 1;
  background: #000;
  transform: scale(0 , 1);
  transition: all 0.3s ease;
}
.product-details-one .product-review-tab .nav-tabs .nav-link.active:before {transform: scale(1);}
.product-details-one .product-review-tab .nav-tabs .nav-item:last-child .nav-link {margin-right: 0;}
.product-details-one .product-review-tab .tab-content h5 {
  font-weight: 500;
  font-size: 20px;
  letter-spacing: -1px;
  margin-bottom: 18px;
}
.product-details-one .product-review-tab .tab-content p {color: rgba(0, 0, 0, 0.7); line-height: 37px;}
.product-details-one .product-review-tab .tab-content .product-feature li {
  color: rgba(0, 0, 0, 0.7);
  position: relative;
  padding-left: 30px;
  margin-bottom: 20px;
}
.product-details-one .product-review-tab .tab-content .product-feature li:before {
  content: url(../images/icon/207.svg);
  position: absolute;
  left: 0;
  top: 0;
}
.product-details-one .user-comment-area {padding-right: 200px;}
.product-details-one .user-comment-area .single-comment {padding-bottom: 40px;}
.product-details-one .user-comment-area .single-comment:last-child {padding-bottom: 0;}
.product-details-one .user-comment-area .user-img {width: 60px;height: 60px;border-radius: 50%;}
.product-details-one .user-comment-area .user-comment-data {padding-left: 30px;position: relative;}
.product-details-one .user-comment-area .user-comment-data .name {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 4px;
}
.product-details-one .user-comment-area .rating li {
  font-size: 15px; 
  margin: 0 3px;
  color: #B3B3B3;
}
.product-details-one .user-comment-area .rating li .fa-star {color: #FFCB65;}
.product-details-one .user-comment-area .user-comment-data p {padding-top: 8px; line-height: 33px;}
.breadcrumb-style-one .breadcrumb-item a,
.breadcrumb-style-one .breadcrumb-item {
  font-size: 17px;
  color: #949494;
}
.breadcrumb-style-one .breadcrumb-item.active {font-weight: 500; color: #000;}
.breadcrumb-style-one .breadcrumb-item+.breadcrumb-item::before {
  content: url(../images/icon/208.svg);
  margin-top: 1px;
}
.breadcrumb-style-one .breadcrumb-item a:hover {text-decoration: underline;}
.product-details-one .share-dropdown .dropdown-toggle {
  font-weight: 500;
  font-size: 17px;
  color: #000;
  padding: 0;
  box-shadow: none;
}
.product-details-one .share-dropdown .dropdown-toggle::after {
  content: '+';
  border: none;
  vertical-align: baseline;
  transition: all 0.2s ease;
}
.product-details-one .share-dropdown .dropdown-toggle.show::after {transform: rotate(45deg);}
.product-details-one .share-dropdown .dropdown-menu {
  min-width: 120px;
  background: #fff;
  border: none;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0px 25px 50px rgba(213, 216, 223, 0.5);
}
.product-details-one .share-dropdown .dropdown-menu .social-icon a {
  width: 26px;
  height: 26px;
  border-radius: 5px;
  background: #212121;
  color: #fff;
  line-height: 26px;
  text-align: center;
  font-size: 15px;
  transition: all 0.2s ease;
}
.product-details-one .share-dropdown .dropdown-menu .social-icon a:hover {background: var(--purple-blue);}
/*^^^^^^^^^^^^^^^^^^^^^ Cart Page ^^^^^^^^^^^^^^^^^^^^^^^^*/
.cart-list-form {position: relative;}
.cart-list-form table {margin: 0;}
.cart-list-form .table th {
  font-weight: 500;
  font-size: 15px;
  letter-spacing: 2px;
  border: none;
  border-bottom: 2px solid #000;
  padding: 0 0 15px;
  text-align: center;
  text-transform: uppercase;
  color: #1d1d1d;
}
.cart-list-form .table th:first-child {text-align: left;}
.cart-list-form .table tbody td {
  padding: 0 0 60px; 
  border: none; 
  vertical-align: middle;
  text-align: center;
}
.cart-list-form .table tbody tr:first-child td {padding-top: 40px;}
.cart-list-form .table tbody tr:last-child td {padding-bottom: 20px;}
.cart-list-form .table .product-thumbnails {width: 85px;}
.cart-list-form .table .product-thumbnails img {max-height: 100%; max-width: none;}
.cart-list-form .table .product-img {display: block; max-width: 85px; max-height: 85px; overflow: hidden;}
.cart-list-form .table .product-info {padding-left: 30px;text-align: left;}
.cart-list-form .table .product-info .product-name {
  font-weight: 500;
  letter-spacing: -0.5px;
  font-size: 20px;
  color: #1f1f1f;
}
.cart-list-form .table .product-info .serial {
  font-size: 15px;
  color: rgba(31,31,31,0.33);
  padding-bottom: 6px;
}
.cart-list-form .table .product-info ul li {
  font-weight: 500;
  display: inline-block;
  font-size: 15px;
  color: #404040;
  padding-right: 15px;
}
.cart-list-form .table .price {font-size: 18px;color: #1d1d1d; font-weight: 500;}
.cart-list-form .table .quantity li {
  display: inline-block;
  line-height: 40px;
  max-height: 40px;
}
.cart-list-form .table .quantity li .btn {
  font-size: 24px;
  padding: 0;
  border: none;
  vertical-align: inherit;
  color: #1d1d1d;
  background: transparent;
}
.cart-list-form .table .quantity .product-value {
  font-weight: 500;
  font-size: 18px;
  color: #1d1d1d;
  max-width: 55px;
  background: transparent;
  border: none;
  text-align: center;
  padding-left: 12px;
}
.cart-list-form .table .remove-product {color: #d6d6d6; font-size: 22px;}
.cart-list-form .table .remove-product:hover {color: #000;}
.cart-section .cart-footer {
  border-top: 2px solid #545454;
  margin-top: 28px;
  padding-top: 40px;
}
.cart-section .cart-footer .coupon-form input {
  width: 240px;
  height: 50px;
  border:none;
  border-bottom: 2px solid #545454;
  font-size: 16px;
  margin-right: 30px;
}
.cart-section .cart-footer .coupon-form ::placeholder {color: #c4c4c4;opacity: 1; /* Firefox */}
.cart-section .cart-footer .coupon-form :-ms-input-placeholder {color: #c4c4c4;}
.cart-section .cart-footer .coupon-form ::-ms-input-placeholder {color: #c4c4c4;}
.cart-section .cart-footer .cart-total-section {text-align: right;}
.cart-section .cart-footer .cart-total-table tr th {
  font-size: 18px;
  color: rgba(29,29,29,0.5);
  font-weight: normal;
  padding-right: 26px;
}
.cart-section .cart-footer .cart-total-table tr td {font-size:18px;color: #1d1d1d; font-weight: 500;}
.cart-section .cart-footer .cart-total-table tr th,
.cart-section .cart-footer .cart-total-table tr td {padding-bottom: 16px;}
.cart-section .cart-footer .theme-btn-seven {
  line-height: 50px; 
  padding: 0 35px;
  border-radius: 0;
  font-size: 16px;
}
.cart-section .cart-footer .theme-btn-seven:hover {background: var(--purple-blue);}
/*^^^^^^^^^^^^^^^^^^^^^ CheckOut Page ^^^^^^^^^^^^^^^^^^^^^^^^*/
.checkout-toggle-area .card {border: none; background: transparent;}
.checkout-toggle-area p {
  margin-bottom: 8px; 
  color: rgba(0, 0, 0, 0.8);
  background: transparent;
  padding: 0;
  border: none;
}
.checkout-toggle-area p button {
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0px;
  color: #1a1a1a;
  background: transparent;
  display: inline-block;
  text-decoration: underline;
  transition: all 0.3s ease-in-out;
}
.checkout-toggle-area p button:hover {color: var(--red-one);}
.checkout-toggle-area form input {
  width: 100%;
  height: 60px;
  font-size: 16px;
  border: 1px solid #d5d5d5;
  border-radius: 5px;
  padding: 0 30px;
  margin-bottom: 20px;
}
.checkout-toggle-area form input:focus {border-color: #777;}
.checkout-toggle-area form .lost-passw {
  color: #636067;
  font-size: 14px;
  margin: 12px 0 35px;
}
.checkout-toggle-area form .lost-passw:hover {color: var(--red-one); text-decoration: underline;}
.checkout-toggle-area form button {line-height: 50px;}
.checkout-toggle-area form p {padding-top: 30px;}
.checkout-form .main-title {
  font-weight: 500;
  font-size:24px;
  letter-spacing: -1px;
  padding-bottom: 55px;
}
.checkout-form .single-input-wrapper {
  display: block;
  width: 100%;
  height: 46px;
  font-size: 16px;
  color: #1a1a1a;
  border: none;
  letter-spacing: 0px;
  border-bottom: 2px solid #E5E5E5;
  margin-bottom: 55px;
}
.checkout-form ::placeholder {color: #1a1a1a;opacity: 1; font-weight: 500;}
.checkout-form :-ms-input-placeholder {color: #1a1a1a; font-weight: 500;}
.checkout-form ::-ms-input-placeholder {color: #1a1a1a; font-weight: 500;}
.checkout-form .single-input-wrapper:focus {border-bottom-color:#545454; }
.checkout-form .theme-select-menu {
  font-weight: 500;
  font-size: 16px;
  width: 100%;
  color: #1a1a1a;
  line-height: 46px;
  border-radius: 0;
  border: none;
  border-bottom: 2px solid #e5e5e5;
  box-shadow: none;
  outline: none;
  padding: 0 15px 5px 0;
  margin-bottom: 60px;
}
.checkout-form .theme-select-menu option {font-size: 14px;color: #1a1a1a;line-height: 22px;}
.checkout-form .theme-select-menu option:hover,
.checkout-form .selectize-dropdown .active {color: #fff; background:#1a1a1a; }
.checkout-form .checkbox-list li label {
  position: relative;
  font-weight: 500;
  font-size: 15px;
  letter-spacing: 0px;
  line-height: 15px;
  padding-left: 28px;
  color: #1a1a1a;
  cursor: pointer;
  margin: 0 0 24px;
}
.checkout-form .checkbox-list li input[type="checkbox"] {display: none;}
.checkout-form .checkbox-list li label:before {
  content: '';
  width: 15px;
  height: 15px;
  line-height: 12px;
  border-radius: 2px;
  border: 1px solid #d5d5d5;
  font-size: 12px;
  text-align: center;
  position: absolute;
  left:0;
  top:-1px;
}
.checkout-form .checkbox-list li input[type="checkbox"]:checked + label:before {
  content: "\f00c";
  font-size: 10px;
  font-family: 'FontAwesome';
  background: #373737;
  color: #fff;
  border-color:  #373737;
}
.checkout-form .checkbox-list {padding-bottom: 44px; margin-top: -24px;}
.checkout-form .other-note-area p {
  font-weight: 500;
  font-size: 15px;
  color: #1a1a1a;
  margin-bottom: 6px;
}
.checkout-form .other-note-area textarea {
  width: 100%;
  border: 1px solid #e5e5e5;
  padding: 15px;
  resize: none;
  height: 145px;
}
.checkout-form .order-confirm-sheet .order-review {background: #F5F5F5;padding: 50px 40px;}
.checkout-form .order-confirm-sheet .order-review .product-review {width: 100%;}
.checkout-form .order-confirm-sheet .order-review .product-review tbody th span {
  font-weight: 500;
  font-size: 16px;
  color: #242424;
}
.checkout-form .order-confirm-sheet .order-review .product-review tbody tr:first-child th span {font-size: 17px;}
.checkout-form .order-confirm-sheet .order-review .product-review tbody td {
  font-size: 16px;
  color: #1d1d1d;
  text-align: right;
  font-weight: 500;
}
.checkout-form .order-confirm-sheet .order-review .product-review tbody th,
.checkout-form .order-confirm-sheet .order-review .product-review tbody td {padding-bottom: 15px;}
.checkout-form .order-confirm-sheet .order-review .product-review tfoot th {
  font-size: 15px;
  text-transform: uppercase;
  color: #242424;
  font-weight: 500;
}
.checkout-form .order-confirm-sheet .order-review .product-review tfoot td {
  text-align: right;
  font-size: 18px;
  color: #222222;
  font-weight: 500;
}
.checkout-form .order-confirm-sheet .order-review .product-review tfoot td,
.checkout-form .order-confirm-sheet .order-review .product-review tfoot th {
  border-top: 1px solid #e9e9e9;
  padding-top: 15px;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li {padding: 0 0 12px 30px; position: relative;}
.checkout-form .order-confirm-sheet .order-review .payment-list li p {font-size: 15px;line-height: 22px;margin-bottom: 12px;}
.checkout-form .order-confirm-sheet .order-review .payment-list li label {
  position: relative;
  font-weight: 500;
  font-size: 16px;
  line-height: 15px;
  color: #1a1a1a;
  cursor: pointer;
  margin: 0 0 13px;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li input[type="radio"] {
  position: absolute;
  opacity: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li label:before {
  content: '';
  width: 15px;
  height: 15px;
  line-height: 12px;
  border-radius: 50%;
  border: 1px solid #d5d5d5;
  font-size: 10px;
  text-align: center;
  position: absolute;
  left:-30px;
  top:-1px;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li input:checked + label:before {
  content: "\f00c";
  font-size: 10px;
  font-family: 'FontAwesome';
  background: #373737;
  color: #fff;
  border-color:  #373737;
}
.checkout-form .order-confirm-sheet .order-review .payment-list {padding: 30px 0 15px;border-bottom: 1px solid #e9e9e9;}
.checkout-form .credit-card-form {margin-top: 5px; display: none;}
.checkout-form .credit-card-form h6 {font-size: 15px;margin-bottom: 5px;}
.checkout-form .credit-card-form input {
  width: 100%;
  height: 40px;
  font-size: 14px;
  border: 1px solid rgba(0,0,0,0.07);
  padding: 0 10px;
  border-radius: 3px;
  margin-bottom: 18px;
}
.checkout-form .credit-card-form span {padding: 0 5px;margin-bottom: 18px;}
.checkout-form .order-confirm-sheet .policy-text {
  font-size: 15px;
  line-height: 24px;
  color: #7a7a7a;
  padding: 23px 0 15px;
}
.checkout-form .order-confirm-sheet .agreement-checkbox label {
  position: relative;
  font-size: 15px;
  line-height: 22px;
  color: #1a1a1a;
  cursor: pointer;
  padding-left: 33px;
  margin-bottom: 35px;
}
.checkout-form .order-confirm-sheet .agreement-checkbox input[type="checkbox"] {display: none;}
.checkout-form .order-confirm-sheet .agreement-checkbox label:before {
  content: '';
  width: 15px;
  height: 15px;
  line-height: 12px;
  border-radius: 2px;
  border: 1px solid #d5d5d5;
  font-size: 10px;
  font-weight: 700;
  text-align: center;
  position: absolute;
  left:0;
  top:7px;
}
.checkout-form .order-confirm-sheet .agreement-checkbox input[type="checkbox"]:checked + label:before {
  content: "\f00c";
  font-size: 10px;
  font-family: 'FontAwesome';
  background: #373737;
  color: #fff;
  border-color:  #373737;
}
.checkout-section .theme-btn-seven {
  line-height: 50px; 
  padding: 0 35px;
  border-radius: 0;
  font-size: 16px;
}
.checkout-section .theme-btn-seven:hover {background: var(--purple-blue);}