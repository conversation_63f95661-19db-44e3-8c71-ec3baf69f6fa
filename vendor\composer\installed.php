<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '7f2cce05ea8794ee5b476b33a996c0b1a30f67a0',
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '7f2cce05ea8794ee5b476b33a996c0b1a30f67a0',
            'dev_requirement' => false,
        ),
        'athlon1600/php-proxy' => array(
            'pretty_version' => 'v5.2.0',
            'version' => '5.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../athlon1600/php-proxy',
            'aliases' => array(),
            'reference' => 'dc8266db597c5b5e88a3ea5a8dbec00d20abe60a',
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => '4dd1e007f22a927ac77da5a3fbb067b42d3bc224',
            'dev_requirement' => false,
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '2.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'reference' => 'b717f1d2f4ef7992ec0c127747ed8b7e170c2f49',
            'dev_requirement' => false,
        ),
    ),
);
