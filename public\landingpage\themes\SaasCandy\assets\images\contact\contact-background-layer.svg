<svg width="1920" height="1146" viewBox="0 0 1920 1146" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_1_3" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1920" height="1146">
<path d="M0 0.779297H1920V1145.78H0V0.779297Z" fill="#1642B9"/>
</mask>
<g mask="url(#mask0_1_3)">
<g opacity="0.3" filter="url(#filter0_f_1_3)">
<circle cx="81.0165" cy="955.938" r="125.154" fill="url(#paint0_linear_1_3)"/>
</g>
<g opacity="0.3" filter="url(#filter1_f_1_3)">
<circle cx="1679.6" cy="960.948" r="421.1" fill="url(#paint1_linear_1_3)"/>
</g>
<g opacity="0.3" filter="url(#filter2_f_1_3)">
<circle cx="1261.7" cy="90.3893" r="222.9" transform="rotate(180 1261.7 90.3893)" fill="url(#paint2_linear_1_3)"/>
</g>
<g opacity="0.3" filter="url(#filter3_f_1_3)">
<circle cx="135.271" cy="0.788559" r="103.164" transform="rotate(180 135.271 0.788559)" fill="url(#paint3_linear_1_3)"/>
</g>
</g>
<circle cx="123.899" cy="474.032" r="8" fill="#FFE073"/>
<circle cx="80.4118" cy="491.683" r="2.73603" fill="#EE7B11"/>
<circle cx="131.899" cy="479.296" r="2.73603" fill="#EE7B11"/>
<circle cx="98.3384" cy="447.711" r="1.92193" fill="#165DF5"/>
<circle cx="1907.46" cy="142.561" r="8" transform="rotate(-180 1907.46 142.561)" fill="#FFE073"/>
<circle cx="1887.36" cy="394.282" r="2.73603" transform="rotate(-180 1887.36 394.282)" fill="#EE7B11"/>
<circle cx="1839.48" cy="256.825" r="2.73603" transform="rotate(-180 1839.48 256.825)" fill="#31FFF3"/>
<circle cx="1731.92" cy="165.914" r="1.92193" transform="rotate(-180 1731.92 165.914)" fill="#165DF5"/>
<defs>
<filter id="filter0_f_1_3" x="-54.1379" y="820.783" width="270.309" height="270.309" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_1_3"/>
</filter>
<filter id="filter1_f_1_3" x="1248.5" y="529.848" width="862.201" height="862.201" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_1_3"/>
</filter>
<filter id="filter2_f_1_3" x="1028.8" y="-142.511" width="465.8" height="465.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_1_3"/>
</filter>
<filter id="filter3_f_1_3" x="22.1071" y="-112.376" width="226.329" height="226.329" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_1_3"/>
</filter>
<linearGradient id="paint0_linear_1_3" x1="30.6149" y1="841.601" x2="93.7202" y2="998.178" gradientUnits="userSpaceOnUse">
<stop stop-color="#165DF5"/>
<stop offset="1" stop-color="#165DF5" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1_3" x1="1510.02" y1="576.246" x2="1722.35" y2="1103.07" gradientUnits="userSpaceOnUse">
<stop stop-color="#165DF5"/>
<stop offset="1" stop-color="#165DF5" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1_3" x1="1171.94" y1="-113.245" x2="1284.33" y2="165.619" gradientUnits="userSpaceOnUse">
<stop stop-color="#165DF5"/>
<stop offset="1" stop-color="#165DF5" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1_3" x1="93.7255" y1="-93.4589" x2="145.743" y2="35.607" gradientUnits="userSpaceOnUse">
<stop stop-color="#165DF5"/>
<stop offset="1" stop-color="#165DF5" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
