<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="keywords" content="Saas, Perfex, CRM">
    <meta name="description" content="Perfex SaaS Landing Page">
    <meta property="og:site_name" content="perfexsaas">
    <meta property="og:url" content="localhost">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Perfex SaaS Landing Page">
    <meta name='og:image' content='images/assets/ogg.png'>
    <!-- For IE -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- For Resposive Device -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- For Window Tab Color -->
    <!-- Chrome, Firefox OS and Opera -->
    <meta name="theme-color" content="#2a2a2a">
    <!-- Windows Phone -->
    <meta name="msapplication-navbutton-color" content="#2a2a2a">
    <!-- iOS Safari -->
    <meta name="apple-mobile-web-app-status-bar-style" content="#2a2a2a">
    <title>Perfex SaaS Landing Page</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="56x56" href="images/fav-icon/icon.png">
	<link rel="preconnect" href="https://rsms.me/">
	<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <!-- Main style sheet -->
    <?php
    echo '<link href="'.module_dir_url(SUPERADMIN_MODULE, 'assets/landing/css/style.css').'"  rel="stylesheet" type="text/css" />';
    echo '<link href="'.module_dir_url(SUPERADMIN_MODULE, 'assets/landing/css/responsive.css').'"  rel="stylesheet" type="text/css" />';
    echo '<script src="'.module_dir_url(SUPERADMIN_MODULE, 'assets/landing/vendor/jquery.min.js').'"></script>';
    ?>

    <!-- Fix Internet Explorer ______________________________________-->
    <!--[if lt IE 9]>
			<script src="https://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
			<script src="vendor/html5shiv.js"></script>
			<script src="vendor/respond.js"></script>
		<![endif]-->
</head>

<body data-spy="scroll" data-target="#one-page-nav" data-offset="120">
    <div class="main-page-wrapper p0">
        <!-- ===================================================
				Loading Transition
			==================================================== -->
        <section>
            <div id="preloader">
                <div id="ctn-preloader" class="ctn-preloader">
                    <div class="animation-preloader">
                        <div class="spinner"></div>
                        <div class="txt-loading">
                            <span data-text-preloader="P" class="letters-loading">
                                P
                            </span>
                            <span data-text-preloader="e" class="letters-loading">
                                r
                            </span>
                            <span data-text-preloader="r" class="letters-loading">
                                r
                            </span>
                            <span data-text-preloader="f" class="letters-loading">
                                f
                            </span>
                            <span data-text-preloader="e" class="letters-loading">
                                e
                            </span>
                            <span data-text-preloader="x" class="letters-loading">
                                x
                            </span>
                            <span data-text-preloader=" " class="letters-loading">

                            </span>
                            <span data-text-preloader="S" class="letters-loading">
                                S
                            </span>
                            <span data-text-preloader="a" class="letters-loading">
                                a
                            </span>
                            <span data-text-preloader="a" class="letters-loading">
                                a
                            </span>
                            <span data-text-preloader="S" class="letters-loading">
                                S
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>