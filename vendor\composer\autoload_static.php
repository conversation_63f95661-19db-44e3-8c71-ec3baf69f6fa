<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInite030a4c9fdf283e8600e5c68107e3693
{
    public static $files = array (
        'fe17454461a24db888b8da8720edd309' => __DIR__ . '/..' . '/athlon1600/php-proxy/src/helpers.php',
        '941748b3c8cae4466c827dfb5ca9602a' => __DIR__ . '/..' . '/rmccue/requests/library/Deprecated.php',
        '4ea1bc7628a69f4030eed01c83b33443' => __DIR__ . '/..' . '/composer/files_autoload.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WpOrg\\Requests\\' => 15,
        ),
        'P' => 
        array (
            'Proxy\\' => 6,
        ),
        'F' => 
        array (
            'Firebase\\JWT\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WpOrg\\Requests\\' => 
        array (
            0 => __DIR__ . '/..' . '/rmccue/requests/src',
        ),
        'Proxy\\' => 
        array (
            0 => __DIR__ . '/..' . '/athlon1600/php-proxy/src',
        ),
        'Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/..' . '/firebase/php-jwt/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Requests' => __DIR__ . '/..' . '/rmccue/requests/library/Requests.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInite030a4c9fdf283e8600e5c68107e3693::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInite030a4c9fdf283e8600e5c68107e3693::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInite030a4c9fdf283e8600e5c68107e3693::$classMap;

        }, null, ClassLoader::class);
    }
}
