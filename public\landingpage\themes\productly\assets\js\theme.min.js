"use strict";function ownKeys(t,e){var o,n=Object.keys(t);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(t),e&&(o=o.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,o)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(o),!0).forEach(function(e){_defineProperty(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function _defineProperty(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}var docReady=function(e){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):setTimeout(e,1)},resize=function(e){return window.addEventListener("resize",e)},isIterableArray=function(e){return Array.isArray(e)&&!!e.length},camelize=function(e){e=e.replace(/[-_\s.]+(.)?/g,function(e,t){return t?t.toUpperCase():""});return"".concat(e.substr(0,1).toLowerCase()).concat(e.substr(1))},getData=function(t,o){try{return JSON.parse(t.dataset[camelize(o)])}catch(e){return t.dataset[camelize(o)]}},hexToRgb=function(e){e=0===e.indexOf("#")?e.substring(1):e,e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(e,t,o,n){return t+t+o+o+n+n}));return e?[parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)]:null},rgbaColor=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"#fff",t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:.5;return"rgba(".concat(hexToRgb(e),", ").concat(t,")")},colors={primary:"#0091e9",secondary:"#002147",success:"#00d27a",info:"#27bcfd",warning:"#FFC928",danger:"#EE4D47",light:"#F9FAFD",dark:"#000"},grays={white:"#fff",100:"#f9fafd",200:"#edf2f9",300:"#d8e2ef",400:"#b6c1d2",500:"#9da9bb",600:"#748194",700:"#5e6e82",800:"#4d5969",900:"#344050",1e3:"#232e3c",1100:"#0b1727",black:"#000"},hasClass=function(e,t){return e.classList.value.includes(t)},addClass=function(e,t){e.classList.add(t)},getOffset=function(e){var t=e.getBoundingClientRect(),o=window.pageXOffset||document.documentElement.scrollLeft,e=window.pageYOffset||document.documentElement.scrollTop;return{top:t.top+e,left:t.left+o}},isScrolledIntoView=function(e){for(var t=e.offsetTop,o=e.offsetLeft,n=e.offsetWidth,r=e.offsetHeight;e.offsetParent;)t+=(e=e.offsetParent).offsetTop,o+=e.offsetLeft;return{all:t>=window.pageYOffset&&o>=window.pageXOffset&&t+r<=window.pageYOffset+window.innerHeight&&o+n<=window.pageXOffset+window.innerWidth,partial:t<window.pageYOffset+window.innerHeight&&o<window.pageXOffset+window.innerWidth&&t+r>window.pageYOffset&&o+n>window.pageXOffset}},breakpoints={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1540},getBreakpoint=function(e){var t,e=e&&e.classList.value;return t=e?breakpoints[e.split(" ").filter(function(e){return e.includes("navbar-expand-")}).pop().split("-").pop()]:t},setCookie=function(e,t,o){var n=new Date;n.setTime(n.getTime()+o),document.cookie="".concat(e,"=").concat(t,";expires=").concat(n.toUTCString())},getCookie=function(e){e=document.cookie.match("(^|;) ?".concat(e,"=([^;]*)(;|$)"));return e&&e[2]},settings={tinymce:{theme:"oxide"},chart:{borderColor:"rgba(255, 255, 255, 0.8)"}},newChart=function(e,t){e=e.getContext("2d");return new window.Chart(e,t)},getItemFromStore=function(t,o){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:localStorage;try{return JSON.parse(n.getItem(t))||o}catch(e){return n.getItem(t)||o}},setItemToStore=function(e,t){return(2<arguments.length&&void 0!==arguments[2]?arguments[2]:localStorage).setItem(e,t)},getStoreSpace=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:localStorage;return parseFloat((escape(encodeURIComponent(JSON.stringify(e))).length/1048576).toFixed(2))},utils={docReady:docReady,resize:resize,isIterableArray:isIterableArray,camelize:camelize,getData:getData,hasClass:hasClass,addClass:addClass,hexToRgb:hexToRgb,rgbaColor:rgbaColor,colors:colors,grays:grays,getOffset:getOffset,isScrolledIntoView:isScrolledIntoView,getBreakpoint:getBreakpoint,setCookie:setCookie,getCookie:getCookie,newChart:newChart,settings:settings,getItemFromStore:getItemFromStore,setItemToStore:setItemToStore,getStoreSpace:getStoreSpace},detectorInit=function(){var e=window.is,t=document.querySelector("html");e.opera()&&addClass(t,"opera"),e.mobile()&&addClass(t,"mobile"),e.firefox()&&addClass(t,"firefox"),e.safari()&&addClass(t,"safari"),e.ios()&&addClass(t,"ios"),e.iphone()&&addClass(t,"iphone"),e.ipad()&&addClass(t,"ipad"),e.ie()&&addClass(t,"ie"),e.edge()&&addClass(t,"edge"),e.chrome()&&addClass(t,"chrome"),e.mac()&&addClass(t,"osx"),e.windows()&&addClass(t,"windows"),navigator.userAgent.match("CriOS")&&addClass(t,"chrome")},navbarInit=function(){var t,o,n,e,r,a,s,i=".navbar-collapse",c=".navbar-toggler",d="collapsed",l="scroll",f="show.bs.collapse",u="hide.bs.collapse",g="hidden.bs.collapse",p=document.querySelector("[data-navbar-on-scroll]");p.addEventListener("click",function(e){e.target.classList.contains("nav-link")&&window.innerWidth<utils.getBreakpoint(p)&&p.querySelector(c).click()}),p&&(t=window.innerHeight,o=document.documentElement,n=p.querySelector(i),e=_objectSpread(_objectSpread({},utils.colors),utils.grays),i=utils.getData(p,"navbar-light-on-scroll"),i=e[e=Object.keys(e).includes(i)?i:"light"],r="bg-".concat(e),a=utils.hexToRgb(i),s=window.getComputedStyle(p).backgroundImage,p.style.backgroundImage="none",window.addEventListener(l,function(){var e=o.scrollTop/t*.35;p.classList.add("backdrop"),0===e&&p.classList.remove("backdrop"),1<=e&&(e=1),p.style.backgroundColor="rgba(".concat(a[0],", ").concat(a[1],", ").concat(a[2],", ").concat(e,")"),p.style.backgroundImage=0<e||utils.hasClass(n,"show")?s:"none"}),utils.resize(function(){var e=utils.getBreakpoint(p);window.innerWidth>e?p.style.backgroundImage=o.scrollTop?s:"none":utils.hasClass(p.querySelector(c),d)||(p.style.backgroundImage=s),window.innerWidth}),n.addEventListener(f,function(){p.classList.add(r),p.style.backgroundImage=s,p.style.transition="background-color,padding 0.35s ease"}),n.addEventListener(u,function(){p.classList.remove(r),o.scrollTop||(p.style.backgroundImage="none")}),n.addEventListener(g,function(){}))},scrollToTop=function(){document.querySelectorAll("[data-anchor] > a, [data-scroll-to]").forEach(function(e){e.addEventListener("click",function(e){e.preventDefault();var t=e.target,e=utils.getData(t,"scroll-to")||t.getAttribute("href");window.scroll({top:null!==(t=utils.getData(t,"offset-top"))&&void 0!==t?t:utils.getOffset(document.querySelector(e)).top-100,left:0,behavior:"smooth"}),window.location.hash=e})})};docReady(navbarInit),docReady(detectorInit),docReady(scrollToTop);
//# sourceMappingURL=theme.min.js.map
