<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>My page</title>
    <!-- Bootstrap core CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
        crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>
    <link href="https://cdn.lineicons.com/4.0/lineicons.css" rel="stylesheet" />
    <script>
        // When the user scrolls the page, execute navbarSticky
        window.onscroll = function () {
            if ( document.querySelector( "nav.fixed-top" ) )
                navbarSticky()
        };

        function toggleNavbarTheme () {
            // Get the navbar
            var navbar = document.getElementsByClassName( "navbar" )[0];

            if ( navbar.classList.contains( "navbar-dark" ) ) {
                navbar.classList.add( "navbar-light" );
                navbar.classList.remove( "navbar-dark" );
            } else if ( navbar.classList.contains( "navbar-light" ) ) {
                navbar.classList.add( "navbar-dark" );
                navbar.classList.remove( "navbar-light" );
            }
        }


        // Add the sticky class to the navbar when you reach its scroll position. Remove "sticky" when you leave the scroll position
        function navbarSticky () {
            // Get the navbar
            var navbar = document.getElementsByClassName( "navbar" )[0];

            // Get the offset position of the navbar
            var sticky = navbar.offsetTop ? navbar.offsetTop : navbar.offsetHeight;

            let isSticky = ( window.pageYOffset >= sticky );

            if ( isSticky ) {
                if ( !navbar.classList.contains( "sticky" ) ) {
                    navbar.classList.add( "sticky" );
                    toggleNavbarTheme();
                }
            } else {
                if ( navbar.classList.contains( "sticky" ) ) {
                    navbar.classList.remove( "sticky" );
                    toggleNavbarTheme();
                }
            }
        }

    </script>
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <!-- Page Content -->
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center">
                <h1 class="mt-5">Bootstrap 5 start page</h1>
                <p class="lead">Start by dragging components to page or double click to edit text</p>
            </div>
        </div>
    </div>
</body>

</html>