![alt webtop](https://github.com/owlling/olicons-site/blob/master/images/link/webtop.jpg?raw=true)

# [olicons](http://olicons.yemaosheji.com) 

olicons is a completely open-source icon set with crafted for designer and coder. You may use for any purpose, whether personal or commercial. Icons in olicons for immediate implementation to your apps or websites. The olicons will keep updating!

>olicons是一款完全开源的图标集，专为设计人员和编码人员设计。您可以将其用于任何目的，无论是个人用途还是商业用途。 olicon中的图标，可立即用于您的应用或网站。olicons将会不断更新！




### Usage

#### Font icons

##### Remote use

You don't need to download any resources. Just remotely link to the olicons fonts.

>不需要下载任何资源,只需远程链接到olicons字体即可。

1、 Add the following to the <head> tag.
>将以下内容添加到<head>中。
  
`<link rel="stylesheet" href = "http://olicons.yemaosheji.com/css/olicons.css">`


2、Add the class attribute corresponding to the “ i ” tag where you need to use the icon.
>在需要使用图标的位置添加“i”标签的class对应图标名称。

>图标共有四种主题风格，只需要使用不同的后缀即可：

|  Theme   | Code reference  |  Preview  |
|  :----:  | :----:  | :----:  |
|  Outline | `<i class="ol-tju-o"><i>` | ![alt ol-tju-o](http://olicons.yemaosheji.com/images/icons/res/outline/ol-tju-o.svg) |
| Filled | `<i class="ol-tju-f"><i>` | ![alt ol-tju-f](http://olicons.yemaosheji.com/images/icons/res/filled/ol-tju-f.svg)  |
| Sharp Outline | `<i class="ol-tju-so"><i>` | ![alt ol-tju-so](http://olicons.yemaosheji.com/images/icons/res/sharp-o/ol-tju-so.svg)  |
| Sharp Filled | `<i class="ol-tju-sf"><i>` | ![alt ol-tju-sf](http://olicons.yemaosheji.com/images/icons/res/sharp-f/ol-tju-sf.svg)  |


Click the card on the [olicons](http://olicons.yemaosheji.com) to copy the icon name.

>点击 [olicons](http://olicons.yemaosheji.com) 站点上的卡片复制图标名称。


![alt web](https://github.com/owlling/olicons-site/blob/master/images/link/web.jpg?raw=true)



#### Local use

Download and save the font folder and css file to your project. Just quote in the same way as above.

>下载并保存字体文件夹和css文件到您的项目中，按以上方式引用即可。

`<link rel="stylesheet" href = "local-path/olicons.css">`


#### SVGs

You can directly download the svg resource pack to the local for secondary creation or use directly.

>您可以直接将svg资源包下载到本地进行二次修改或直接使用。

`<img src="local-path/olicons.svg"/>`




### Open source agreement

The olicons font follows SIL OFL 1.1 agreement.

The css files in olicons follow the MIT agreement.

>olicon字体遵循SIL OFL 1.1协议。

>olicons中的css文件遵循MIT协议。




### Design

The olicons is drawn using Sketch App vector design software.

The olicons uses IcoMoon App to build font files and css files.

>olicons是用Sketch App矢量设计软件绘制的。

>olicon使用IcoMoon应用程序构建字体文件和css文件。




### Update log

|  Version   | Date  |  Total  |
|  ----  | ----  | ----  |
| v2.0.1  | 2020.09.07 | 678 |
| v1.0.3  | 2020.07.28 | 394 |
| v1.0.2  | 2020.07.24 | 266 |
| v1.0.1  | 2020.07.15 | 218 |


***


If you don’t find what you need, you can leave a message in the comment area, or add the author’s WeChat directly.
>如果没找到你需要的，可在[评论区留言](https://github.com/owlling/olicons/issues) ，或者直接[加作者微信](https://www.owlling.com/befriend)。
>>![alt Wechat](https://www.owlling.com/images/befriend/wechat.svg)


