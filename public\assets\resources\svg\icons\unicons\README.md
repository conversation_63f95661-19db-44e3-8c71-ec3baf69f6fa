# Unicons

1000+ Pixel-perfect vector icons and Iconfont for your next project. These icons are designed by [Iconscout](https://iconscout.com).

## Getting Started

### Using as a font

It's easy to use Unicons on your website by just inserting following css. You can also download this repo and use the css from `css` folder.

```html
<link
  rel="stylesheet"
  href="https://unicons.iconscout.com/release/v4.0.0/css/unicons.css"
/>
```

And use the icons in your `body` as below.

```html
<i class="uil uil-comments"></i>
```

### Using as npm package

You can easily install [Unicons](https://iconscout.com/unicons) using npm.

```bash
npm install --save @iconscout/unicons
```

### Using as an SVG

If you're a designer then it's always great to have SVG files. You can download SVGs from [Official Unicons Website](https://iconscout.com/unicons) or [download Icondrop](https://iconscout.com/icondrop) to access these icons right into Sketch, Adobe Xd, Adobe Illustrator, and many more.

### Using in Flutter Framework

There's a Flutter package created by [<PERSON>](https://github.com/pedrolemoz), avaliable in [pub.dev](https://pub.dev/packages/unicons), which can be easily used in your project.

Just add the dependency in your `pubspec.yaml` file as following:

```yaml
dependencies:
  unicons: ^1.0.0+2
```

Then, run the command above:

```bash
flutter pub get
```

Alternatively, your editor might support `flutter pub get`. Check the docs for your editor to learn more.

Now, in your Dart code, import the package:

```dart
import 'package:unicons/unicons.dart';
```

### Use with JS Frameworks

We've created components for popular libraries like React, VueJS, React Native. You can head over to official GitHub Repo to know more.

#### Line Style

- For React: https://github.com/Iconscout/react-unicons
- For VueJS: https://github.com/Iconscout/vue-unicons
- For React Native: https://github.com/Iconscout/react-native-unicons

#### Solid Style

- For React: https://github.com/Iconscout/react-unicons-solid
- For VueJS: https://github.com/Iconscout/vue-unicons-solid

#### Monochrome Style

- For React: https://github.com/Iconscout/react-unicons-monochrome

### More ways (Community supported)

- [Vue-unicons](https://github.com/antonreshetov/vue-unicons) by [Anton Reshetov](https://github.com/antonreshetov)

## Roadmap

- [x] Make React Components
- [x] Adding Brand Icons
- [x] Designing Solid Style
- [ ] Designing Thin-line Style
- [ ] Support for CSS Animations
- [ ] Make SVG Sprite in which user can control different attributes such as stroke, fill, width, height and more.

## Contributing

We will be happy to have community support for Unicons. Feel free to fork and create pull requests. We have given a small roadmap above so that you can help us build these features.

### Icon Requests

We've created a thread at [Iconscout Community](https://discuss.iconscout.com/new-topic?title=Icon%20Request:%20%3Cicon%3E&body=Hey%20there,%20%3Cicon%3E%20will%20be%20great%20fit%20for%20Unicons.%20I%20would%20love%20to%20use%20it!&category=Unicons&tags=requests) where you can request for new icons. And we'll be happy to design them in upcoming weeks.

## License

Unicons are Open Source icons and licensed under [Apache 2.0](https://www.apache.org/licenses/LICENSE-2.0.txt). You're free to use these icons in your personal and commercial project. We would love to see the attribution in your app's **about** screen, but it's not mandatory.

```html
Unicons by <a href="https://iconscout.com/">Iconscout</a>
```
