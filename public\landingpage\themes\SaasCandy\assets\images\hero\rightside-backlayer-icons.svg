<svg width="169" height="707" viewBox="0 0 169 707" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_10)">
<circle cx="156.953" cy="23.4489" r="7.26185" fill="#A8C0FF"/>
<circle cx="134.874" cy="3.19111" r="2.73603" fill="#EE7B11"/>
<circle cx="32.3177" cy="671.832" r="8" fill="#FFE073"/>
<circle cx="6.75752" cy="695.719" r="2.73603" fill="#EE7B11"/>
<circle cx="66.5355" cy="681.754" r="1.92193" fill="#A8C0FF"/>
</g>
<defs>
<filter id="filter0_d_1_10" x="0.0214844" y="0.455078" width="168.193" height="706" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_10"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_10" result="shape"/>
</filter>
</defs>
</svg>
