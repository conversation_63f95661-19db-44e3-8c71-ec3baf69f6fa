{"name": "athlon1600/php-proxy", "type": "library", "keywords": ["php proxy", "proxy script", "php web proxy", "web proxy", "php proxy script"], "license": "MIT", "homepage": "https://www.php-proxy.com/", "require": {"ext-curl": "*"}, "suggest": {"predis/predis": "For caching purposes"}, "autoload": {"psr-4": {"Proxy\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Proxy\\Tests\\": "tests/"}}, "require-dev": {"phpunit/phpunit": "7"}}