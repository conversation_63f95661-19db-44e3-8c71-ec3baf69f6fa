{"packages": [{"name": "athlon1600/php-proxy", "version": "v5.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Athlon1600/php-proxy.git", "reference": "dc8266db597c5b5e88a3ea5a8dbec00d20abe60a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Athlon1600/php-proxy/zipball/dc8266db597c5b5e88a3ea5a8dbec00d20abe60a", "reference": "dc8266db597c5b5e88a3ea5a8dbec00d20abe60a", "shasum": ""}, "require": {"ext-curl": "*"}, "require-dev": {"phpunit/phpunit": "7"}, "suggest": {"predis/predis": "For caching purposes"}, "time": "2019-12-31T20:33:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "homepage": "https://www.php-proxy.com/", "keywords": ["php proxy", "php proxy script", "php web proxy", "proxy script", "web proxy"], "support": {"issues": "https://github.com/Athlon1600/php-proxy/issues", "source": "https://github.com/Athlon1600/php-proxy/tree/master"}, "install-path": "../athlon1600/php-proxy"}, {"name": "firebase/php-jwt", "version": "v6.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2023-02-09T21:01:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.4.0"}, "install-path": "../firebase/php-jwt"}, {"name": "rmccue/requests", "version": "v2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/WordPress/Requests.git", "reference": "b717f1d2f4ef7992ec0c127747ed8b7e170c2f49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/Requests/zipball/b717f1d2f4ef7992ec0c127747ed8b7e170c2f49", "reference": "b717f1d2f4ef7992ec0c127747ed8b7e170c2f49", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "requests/test-server": "dev-main", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.6", "wp-coding-standards/wpcs": "^2.0", "yoast/phpunit-polyfills": "^1.0.0"}, "time": "2022-10-11T08:15:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/Deprecated.php"], "psr-4": {"WpOrg\\Requests\\": "src/"}, "classmap": ["library/Requests.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "https://rmccue.io/"}, {"name": "<PERSON>", "homepage": "https://github.com/schlessera"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl"}, {"name": "Contributors", "homepage": "https://github.com/WordPress/Requests/graphs/contributors"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "https://requests.ryanmccue.info/", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "support": {"docs": "https://requests.ryanmccue.info/", "issues": "https://github.com/WordPress/Requests/issues", "source": "https://github.com/WordPress/Requests"}, "install-path": "../rmccue/requests"}], "dev": true, "dev-package-names": []}