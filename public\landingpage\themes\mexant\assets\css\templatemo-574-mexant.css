/*

TemplateMo 574 Mexant

https://templatemo.com/tm-574-mexant

*/

/* ---------------------------------------------
Table of contents
------------------------------------------------
01. font & reset css
02. reset
03. global styles
04. header
05. banner
06. features
07. testimonials
08. contact
09. footer
10. preloader
11. search
12. portfolio

--------------------------------------------- */
/* 
---------------------------------------------
font & reset css
--------------------------------------------- 
*/
@import url("https://fonts.googleapis.com/css?family=Open+Sans:100,200,300,400,500,600,700,800,900");
/* 
---------------------------------------------
reset
--------------------------------------------- 
*/
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, div
pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q,
s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li,
figure, header, nav, section, article, aside, footer, figcaption {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
}

.clearfix:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

.clearfix {
  display: inline-block;
}

html[xmlns] .clearfix {
  display: block;
}

* html .clearfix {
  height: 1%;
}

ul, li {
  padding: 0;
  margin: 0;
  list-style: none;
}

header, nav, section, article, aside, footer, hgroup {
  display: block;
}

* {
  box-sizing: border-box;
}

html, body {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  background-color: #fff;
  font-size: 16px;
  -ms-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  text-decoration: none !important;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0px;
  margin-bottom: 0px;
  color: #212741;
}

ul {
  margin-bottom: 0px;
}

p {
  font-size: 15px;
  line-height: 26px;
  color: #212741;
}

img {
  width: 100%;
  overflow: hidden;
}

/* 
---------------------------------------------
global styles
--------------------------------------------- 
*/
html,
body {
  background: #fff;
  font-family: 'Poppins', sans-serif;
}

::selection {
  background: #ff511a;
  color: #fff;
}

::-moz-selection {
  background: #ff511a;
  color: #fff;
}

@media (max-width: 991px) {
  html, body {
    overflow-x: hidden;
  }
  .mobile-top-fix {
    margin-top: 30px;
    margin-bottom: 0px;
  }
  .mobile-bottom-fix {
    margin-bottom: 30px;
  }
  .mobile-bottom-fix-big {
    margin-bottom: 60px;
  }
}

.green-button a {
  font-size: 14px;
  color: #fff;
  background-color: #43ba7f;
  padding: 12px 30px;
  display: inline-block;
  border-radius: 5px;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.5px;
  transition: all .3s;
}

.green-button a:hover {
  opacity: 0.9;
}

.orange-button a {
  font-size: 14px;
  color: #fff;
  background-color: #ff511a;
  padding: 12px 30px;
  display: inline-block;
  border-radius: 5px;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.5px;
  transition: all .3s;
}

.orange-button a:hover {
  opacity: 0.9;
}

section {
  margin-top: 120px;
}

.section-heading {
  position: relative;
  z-index: 2;
  margin-top: 0px;
  text-align: center;
  margin-bottom: 70px;
}

.section-heading h6 {
  font-size: 16px;
  text-transform: uppercase;
  color: #ff511a;
  font-weight: 700;
}

.section-heading h4 {
  margin-top: 10px;
  line-height: 40px;
  font-size: 36px;
  font-weight: 700;
  text-transform: capitalize;
  color: #212741;
}

.section-heading p {
  margin-top: 30px;
}


/* 
---------------------------------------------
header
--------------------------------------------- 
*/
.background-header {
  background-color: #212741;
  height: 80px!important;
  position: fixed!important;
  top: 0!important;
  left: 0;
  right: 0;
  box-shadow: 0px 0px 10px rgba(0,0,0,0.15)!important;
}

.header-area {
  background-image: url(../images/heading-bg.jpg);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  height: 110px;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  -webkit-transition: all .5s ease 0s;
  -moz-transition: all .5s ease 0s;
  -o-transition: all .5s ease 0s;
  transition: all .5s ease 0s;
}

.header-area .main-nav {
  min-height: 80px;
  background: transparent;
}

.header-area .main-nav .logo {
  line-height: 120px;
  color: #fff;
  font-size: 28px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  float: left;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.background-header .main-nav .logo {
  line-height: 80px;
  width: 160px;
}

.background-header .nav {
  margin-top: 18px !important;
}

.header-area .main-nav .nav {
  float: right;
  margin-top: 40px;
  margin-right: 0px;
  background-color: transparent;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  position: relative;
  z-index: 999;
}

.header-area .main-nav .nav li {
  padding-left: 25px;
  padding-right: 25px;
}

.header-area .main-nav .nav li:last-child {
  padding-right: 0px;
}

.header-area .main-nav .nav li a {
  display: block;
  font-weight: 300;
  font-size: 14px;
  text-transform: capitalize;
  color: #fff;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  height: 40px;
  line-height: 40px;
  border: transparent;
  letter-spacing: 1px;
}

.header-area .main-nav .nav li:last-child a {
  font-size: 14px;
  color: #fff;
  background-color: #43ba7f;
  padding: 10px 20px !important;
  display: inline-block;
  border-radius: 5px;
  font-weight: 500;
  text-transform: capitalize;
  height: auto;
  line-height: 25px;
  letter-spacing: 0.5px;
  transition: all .3s;
}

.header-area .main-nav .nav li:last-child a:hover {
  color: #fff;
}

.header-area .main-nav .nav li.has-sub ul.sub-menu li:last-child a,
.background-header .main-nav .nav li.has-sub ul.sub-menu li:last-child a {
  margin-left: 0px;
  padding-left: 0px;
  padding-right: 0px;
  border-radius: 0px;
  padding-top: 0px !important;
  height: 40px;
}


.header-area .main-nav .nav li.has-sub ul.sub-menu li:last-child a:hover,
.background-header .main-nav .nav li.has-sub ul.sub-menu li:last-child a:hover {
  padding-left: 25px !important;
}

.header-area .main-nav .nav li:hover a,
.header-area .main-nav .nav li a.active {
  color: #ff511a;
}

.background-header .main-nav .nav li:hover a,
.background-header .main-nav .nav li a.active {
  color: #ff511a;
  opacity: 1;
}

.header-area .main-nav .nav li.has-sub {
  position: relative;
  padding-right: 15px;
}

.header-area .main-nav .nav li.has-sub:after {
  font-family: FontAwesome;
  content: "\f107";
  font-size: 12px;
  color: #fff;
  position: absolute;
  right: 2px;
  top: 12px;
}

.header-area .main-nav .nav li.has-sub ul.sub-menu {
  position: absolute;
  width: 140px;
  background-color: #fff;
  box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
  top: 40px;
  border-radius: 5px;
  opacity: 0;
  transition: all .3s;
  transform: translateY(+2em);
  visibility: hidden;
  z-index: -1;
}

.header-area .main-nav .nav li.has-sub ul.sub-menu li {
  margin-left: 0px;
  padding-left: 0px;
  padding-right: 0px;
}

.header-area .main-nav .nav li.has-sub ul.sub-menu li a {
  opacity: 1;
  display: block;
  background: #f7f7f7;
  color: #2a2a2a!important;
  padding-left: 20px;
  height: 40px;
  line-height: 40px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  position: relative;
  font-size: 12px;
  font-weight: 400;
  border-bottom: 1px solid #eee;
}

.header-area .main-nav .nav li.has-sub ul li a:hover {
  background: #fff;
  color: #ff511a!important;
  padding-left: 25px;
}

.header-area .main-nav .nav li.has-sub ul li a:hover:before {
  width: 3px;
}

.header-area .main-nav .nav li.has-sub:hover ul.sub-menu {
  visibility: visible;
  opacity: 1;
  z-index: 1;
  transform: translateY(0%);
  transition-delay: 0s, 0s, 0.3s;
}

.header-area .main-nav .menu-trigger {
  cursor: pointer;
  display: block;
  position: absolute;
  top: 33px;
  width: 32px;
  height: 40px;
  text-indent: -9999em;
  z-index: 99;
  right: 40px;
  display: none;
}

.background-header .main-nav .menu-trigger {
  top: 23px;
}

.header-area .main-nav .menu-trigger span,
.header-area .main-nav .menu-trigger span:before,
.header-area .main-nav .menu-trigger span:after {
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  background-color: #fff;
  display: block;
  position: absolute;
  width: 30px;
  height: 2px;
  left: 0;
}

.background-header .main-nav .menu-trigger span,
.background-header .main-nav .menu-trigger span:before,
.background-header .main-nav .menu-trigger span:after {
  background-color: #fff;
}

.header-area .main-nav .menu-trigger span:before,
.header-area .main-nav .menu-trigger span:after {
  -moz-transition: all 0.4s;
  -o-transition: all 0.4s;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  background-color: #fff;
  display: block;
  position: absolute;
  width: 30px;
  height: 2px;
  left: 0;
  width: 75%;
}

.background-header .main-nav .menu-trigger span:before,
.background-header .main-nav .menu-trigger span:after {
  background-color: #fff;
}

.header-area .main-nav .menu-trigger span:before,
.header-area .main-nav .menu-trigger span:after {
  content: "";
}

.header-area .main-nav .menu-trigger span {
  top: 16px;
}

.header-area .main-nav .menu-trigger span:before {
  -moz-transform-origin: 33% 100%;
  -ms-transform-origin: 33% 100%;
  -webkit-transform-origin: 33% 100%;
  transform-origin: 33% 100%;
  top: -10px;
  z-index: 10;
}

.header-area .main-nav .menu-trigger span:after {
  -moz-transform-origin: 33% 0;
  -ms-transform-origin: 33% 0;
  -webkit-transform-origin: 33% 0;
  transform-origin: 33% 0;
  top: 10px;
}

.header-area .main-nav .menu-trigger.active span,
.header-area .main-nav .menu-trigger.active span:before,
.header-area .main-nav .menu-trigger.active span:after {
  background-color: transparent;
  width: 100%;
}

.header-area .main-nav .menu-trigger.active span:before {
  -moz-transform: translateY(6px) translateX(1px) rotate(45deg);
  -ms-transform: translateY(6px) translateX(1px) rotate(45deg);
  -webkit-transform: translateY(6px) translateX(1px) rotate(45deg);
  transform: translateY(6px) translateX(1px) rotate(45deg);
  background-color: #fff;
}

.background-header .main-nav .menu-trigger.active span:before {
  background-color: #fff;
}

.header-area .main-nav .menu-trigger.active span:after {
  -moz-transform: translateY(-6px) translateX(1px) rotate(-45deg);
  -ms-transform: translateY(-6px) translateX(1px) rotate(-45deg);
  -webkit-transform: translateY(-6px) translateX(1px) rotate(-45deg);
  transform: translateY(-6px) translateX(1px) rotate(-45deg);
  background-color: #fff;
}

.background-header .main-nav .menu-trigger.active span:after {
  background-color: #fff;
}

.header-area.header-sticky {
  min-height: 80px;
}

.header-area .nav {
  margin-top: 30px;
}

.header-area.header-sticky .nav li a.active {
  color: #ff511a;
}

@media (max-width: 1200px) {
  .header-area .main-nav .nav li {
    padding-left: 10px;
    padding-right: 10px;
  }
  .header-area .main-nav:before {
    display: none;
  }
}

@media (max-width: 767px) {
  .header-area .main-nav .logo {
    color: #1e1e1e;
  }
  .header-area.header-sticky .nav li a:hover,
  .header-area.header-sticky .nav li a.active {
    color: #ff511a!important;
    opacity: 1;
  }
  .header-area.header-sticky .nav li.search-icon a {
    width: 100%;
  }
  .header-area {
    background-color: #f7f7f7;
    padding: 0px 15px;
    height: 100px;
    box-shadow: none;
    text-align: center;
  }
  .header-area .container {
    padding: 0px;
  }
  .header-area .logo {
    margin-left: 0px;
    line-height: 80px;
  }
  .header-area .menu-trigger {
    display: block !important;
  }
  .header-area .main-nav {
    overflow: hidden;
  }
  .header-area .main-nav .nav {
    float: none;
    width: 100%;
    display: none;
    -webkit-transition: all 0s ease 0s;
    -moz-transition: all 0s ease 0s;
    -o-transition: all 0s ease 0s;
    transition: all 0s ease 0s;
    margin-left: 0px;
  }
  .header-area .main-nav .nav li:first-child {
    border-top: 1px solid #eee;
  }
  .header-area.header-sticky .nav {
    margin-top: 100px !important;
  }
  .background-header.header-sticky .nav {
    margin-top: 80px !important;
  }
  .header-area .main-nav .nav li {
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #eee;
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
  .header-area .main-nav .nav li a {
    height: 50px !important;
    line-height: 50px !important;
    padding: 0px !important;
    border: none !important;
    background: #f7f7f7 !important;
    color: #191a20 !important;
  }
  .header-area .main-nav .nav li a:hover {
    background: #eee !important;
    color: #ff511a!important;
  }
  .header-area .main-nav .nav li.has-sub ul.sub-menu {
    position: relative;
    visibility: inherit;
    opacity: 1;
    z-index: 1;
    transform: translateY(0%);
    top: 0px;
    width: 100%;
    box-shadow: none;
    height: 0px;
    transition: all 0s;
  }
  .header-area .main-nav .nav li.submenu ul li a {
    font-size: 12px;
    font-weight: 400;
  }
  .header-area .main-nav .nav li.submenu ul li a:hover:before {
    width: 0px;
  }
  .header-area .main-nav .nav li.has-sub ul.sub-menu {
    height: auto;
  }
  .header-area .main-nav .nav li.has-sub:after {
    color: #3B566E;
    right: 30px;
    font-size: 14px;
    top: 15px;
  }
  .header-area .main-nav .nav li.submenu:hover ul, .header-area .main-nav .nav li.submenu:focus ul {
    height: 0px;
  }
}

@media (min-width: 767px) {
  .header-area .main-nav .nav {
    display: flex !important;
  }
}


/* 
---------------------------------------------
banner
--------------------------------------------- 
*/

.swiper-container {
  height: calc(100vh - 0px);
  margin: 0px;
  background-color: #212741;
}

.swiper-slide {
  overflow: hidden;
}

.slide-inner {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-size: cover;
  background-position: center;
}

.swiper-slide-active h2 {
  animation: fadeInLeft .8s;
  -webkit-animation-delay: 1s;
  animation-delay: .3s;
  opacity: 1;
}

.swiper-slide-active p {
  animation: fadeInRight .8s;
  -webkit-animation-delay: 1s;
  animation-delay: .3s;
  opacity: 1;
}

.swiper-slide-active .div-dec {
  animation: fadeIn .8s;
  -webkit-animation-delay: 1s;
  animation-delay: .3s;
  opacity: 1;
}

.swiper-slide-active .buttons .green-button,
.swiper-slide-active .buttons .orange-button {
  animation: fadeInUp .8s;
  -webkit-animation-delay: 1s;
  animation-delay: .3s;
  opacity: 1;
}

.slide-inner .header-text {
  position: absolute;
  width: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.slide-inner .header-text h2 {
  position: relative;
  z-index: 2;
  font-size: 56px;
  color: #fff;
  font-weight: 700;
  opacity: 1;
  visibility: visible;
  line-height: 70px;
  margin-bottom: 40px;
}

.slide-inner .header-text .div-dec {
  width: 80px;
  height: 6px;
  border-radius: 3px;
  background-color: #fff;
  margin-bottom: 40px;
  opacity: 1;
  visibility: visible;
}

.slide-inner .header-text p {
  color: #fff;
  margin-right: 60px;
  margin-bottom: 50px;
  opacity: 1;
  visibility: visible;
}

.slide-inner .header-text h2 em {
  font-style: normal;
  color: #ff511a;
}

.slide-inner .header-text .buttons {
  display: inline;
  opacity: 1;
  visibility: visible;
}

.slide-inner .header-text .buttons .green-button {
  display: inline;
  float: left;
  margin-right: 20px;
}

.swiper-button-next, .swiper-button-prev {
  color: #fff !important;
}

/*
---------------------------------------------
services
---------------------------------------------
*/ 

section.services {
  margin-top: 90px;
}

.services .service-item {
  border-radius: 5px;
  padding: 40px;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.services .service-item:hover i {
  margin-top: 15px;
}

.services .service-item h4 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
}

.services .service-item i {
  transition: all .5s;
  float: left;
  font-size: 64px;
  color: #43ba7f;
  margin-bottom: 30px;
  margin-right: 30px;
}



/*
---------------------------------------------
cta
---------------------------------------------
*/

section.simple-cta {
  background-image: url(../images/cta-bg.jpg);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  width: 100%;
  padding: 80px 0px;
}

section.simple-cta .left-image {
  margin-right: 45px;
}

section.simple-cta h4 {
  font-size: 36px;
  font-weight: 700;
  color: #fff;
  line-height: 50px;
}

section.simple-cta h4 em {
  font-style: normal;
  color: #43ba7f;
}

section.simple-cta h4 strong {
  color: #ff511a;
  font-weight: 700;
}

section.simple-cta p {
  color: #fff;
  margin-top: 30px;
  margin-bottom: 40px;
}

section.simple-cta .buttons {
  display: flex;
  justify-content: end;
}

section.simple-cta .buttons .orange-button {
  margin-left: 20px;
}

section.simple-cta .buttons .orange-button,
section.simple-cta .buttons .green-button {
  margin-top: 25px;
}



/*
---------------------------------------------
about us
---------------------------------------------
*/

section.about-us {
  margin-top: 90px;
}

section.about-us .section-heading {
  padding-top: 30px;
}

section.about-us .naccs {
  position: relative;
}

section.about-us .naccs .menu div {
  padding: 15px 30px;
  display: inline-block;
  text-align: center;
  border-radius: 5px !important;
  margin-right: 30px;
  font-size: 18px;
  color: #212741;
  background-color: #fff;
  cursor: pointer;
  vertical-align: middle;
  font-weight: 600;
  transition: 1s all cubic-bezier(0.075, 0.82, 0.165, 1);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}

section.about-us .naccs .menu div.active {
  color: #ff511a;
}

section.about-us ul.nacc {
  margin-top: 50px !important;
  position: relative;
  min-height: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}

section.about-us ul.nacc li {
  background-color: #fff;
  overflow: hidden;
  opacity: 0;
  transform: translateX(50px);
  position: absolute;
  list-style: none;
  transition: 1s all cubic-bezier(0.075, 0.82, 0.165, 1);
}

section.about-us ul.nacc li .main-list,
section.about-us ul.nacc li .list-item {
  border-bottom: 1px solid #eee;
  margin-bottom: 25px;
  padding-bottom: 25px;
}

section.about-us ul.nacc li .last-item {
  margin-bottom: 0px;
}

section.about-us ul.nacc li span.title {
  font-size: 15px;
  color: #212741;
  font-weight: 600;
  display: inline-block;
  width: 24%;
}

section.about-us ul.nacc li span.item-title {
  color: #43ba7f !important;
  font-weight: 600 !important;
}

section.about-us ul.nacc li span.item {
  font-size: 15px;
  color: #212741;
  font-weight: 400;
  display: inline-block;
  width: 24%;
}

section.about-us ul.nacc li.active {
  position: relative;
  transition-delay: 0.3s;
  z-index: 2;
  opacity: 1;
  transform: translateX(0px);
}

section.about-us .right-content {
  margin-left: 60px;
}

section.about-us .right-content h4 {
  font-size: 22px;
  font-weight: 700;
  line-height: 40px;
  margin-bottom: 40px;
}

section.about-us .right-content p {
  margin-bottom: 50px;
}


/*
---------------------------------------------
calculator
---------------------------------------------
*/

section.calculator {
  margin-top: 150px;
  background-image: url(../images/calculator-bg.jpg);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  width: 100%;
  padding: 80px 0px;
}

section.calculator .left-image {
  margin: -110px 45px -80px 0px;
}

section.calculator .section-heading {
  text-align: left;
}

section.calculator .section-heading h4 {
  color: #fff;
}

#calculate label {
  color: #fff;
  font-size: 14px;
  margin-bottom: 5px;
}

#calculate input,
#calculate select {
  background-color: rgba(121,134,151, 0.7);
  border: none;
  border-radius: 5px;
  width: 100%;
  height: 42px;
  color: #fff;
  font-size: 14px;
  margin-bottom: 30px;
  outline: none;
}

#calculate select {
  cursor: pointer;
}

#calculate button.orange-button {
  font-size: 14px;
  color: #fff;
  background-color: #ff511a;
  padding: 12px 30px;
  display: inline-block;
  border-radius: 5px;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.5px;
  border: none;
  transition: all .3s;
}

#calculate button.orange-button:hover {
  opacity: 0.9;
}



/* 
---------------------------------------------
testimonials
--------------------------------------------- 
*/

section.testimonials {
  margin-top: 90px;
}

section.testimonials .section-heading {
  padding-top: 30px;
}

.testimonials .item {
  border-radius: 15px;
  padding: 50px 320px 50px 50px;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  margin: 15px;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  padding-top: 35px;
}

.testimonials .item i {
  width: 70px;
  height: 70px;
  background-color: #43ba7f;
  display: inline-block;
  text-align: center;
  line-height: 70px;
  color: #fff;
  font-size: 44px;
  border-radius: 5px;
  margin-bottom: 30px;
}

.testimonials .item p {
  font-size: 16px;
  font-style: italic;
}

.testimonials .item h4 {
  margin-top: 30px;
  font-size: 22px;
  font-weight: 600;
  position: relative;
}

.testimonials .item span {
  margin-top: 5px;
  display: block;
  font-size: 15px;
  font-weight: 600;
  color: #43ba7f;
}

.testimonials .item img {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  max-width: 300px;
  border-radius: 5px;
}

.testimonials .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.testimonials .owl-dots .owl-dot {
  width: 10px;
  height: 10px;
  background-color: #ffa88c;
  border-radius: 50%;
  margin: 0px 4px;
}

.testimonials .owl-dots .active {
  background-color: #ff511a;
}



/* 
---------------------------------------------
partners
--------------------------------------------- 
*/

section.partners {
  background-color: #212741;
  padding: 80px 0px;
}

section.partners .item {
  margin: 0px 15px;
  cursor: pointer;
  transition: all .3s;
}

section.partners .item:hover {
  opacity: 0.75;
}



/* 
---------------------------------------------
footer 
--------------------------------------------- 
*/

footer {
  background-color: #181d30;
  text-align: center;
  padding: 25px 0px;
}

footer p {
  color: #fff;
  font-weight: 300;
}

footer p a {
  color: #ff511a;
}



/* 
---------------------------------------------
page heading
--------------------------------------------- 
*/

.page-heading {
  background-image: url(../images/heading-bg.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 215px 0px 120px 0px;
  text-align: center;
}

.page-heading h2 {
  font-size: 56px;
  color: #fff;
  font-weight: 700;
}

.page-heading .div-dec {
  width: 80px;
  height: 6px;
  border-radius: 3px;
  background-color: #fff;
  margin: 20px auto 0 auto;
}



/* 
---------------------------------------------
about us page
--------------------------------------------- 
*/

section.top-section .left-image img {
  border-radius: 10px;
}

section.top-section .accordions {
  border-radius: 5px;
  padding: 50px 40px;
  background-color: #fff;
  margin-left: 30px;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}
.accordions .accordion {
  border-bottom: 1px solid #eee;
}
.accordions .last-accordion {
  border-bottom: none;
}
.accordion-head {
  padding: 35px 0px !important;  
  font-size: 22px;
  font-weight: 600;
  color: #212741;
  cursor: pointer;
  transition: color 200ms ease-in-out;
  
}
@media screen and (min-width: 768px) {
  .accordion-head {
    padding: 1rem;
    font-size: 1.2rem;
  }
}
.accordion-head .icon {
  float: right;
  transition: transform 200ms ease-in-out;
}
.accordion-head.is-open {
  color: #43ba7f;
  border-bottom: none;
}
.accordion-head.is-open .icon {
  transform: rotate(45deg);
}
.accordion-body {
  padding: 0px;
  overflow: hidden;
  height: 0;
  transition: height 300ms ease-in-out;
  border-bottom: 1px solid #fff;
}
.accordion-body > .content p {
  padding: 0px 0px 30px 0px;
  padding-top: 0;
}

section.what-we-do {
  margin-bottom: -30px;
}

.what-we-do .left-content {
  margin-right: 60px;
}

.what-we-do .left-content h4 {
  font-size: 22px;
  font-weight: 700;
  line-height: 40px;
  margin-bottom: 40px;
}

.what-we-do .left-content p {
  margin-bottom: 40px;
}

.what-we-do .right-items .item {
  border-radius: 5px;
  padding: 40px 30px;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  position: relative;
}

.what-we-do .right-items .item em {
  position: absolute;
  font-style: normal;
  font-size: 44px;
  color: #43ba7f;
  font-weight: 600;
  right: 15px;
  top: 5px;
  transition: all .3s;
}

.what-we-do .right-items .item h4 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 22px;
}

.what-we-do .right-items .item:hover em {
  top: 10px;
}



/* 
---------------------------------------------
services page
--------------------------------------------- 
*/

.main-services .service-item {
  margin-bottom: 60px;
  padding-bottom: 60px;
  border-bottom: 1px solid #ecf8f2;
}

.main-services .last-service {
  border-bottom: none;
  margin-bottom: 0px;
  padding-bottom: 0px;
}

.main-services  .left-image {
  margin-right: 15px;
}

.main-services  .left-image img {
  border-radius: 5px;
}

.main-services  .right-image {
  margin-left: 15px;
}

.main-services  .right-image img {
  border-radius: 5px;
}

.main-services .right-text-content {
  margin-left: 15px;
}

.main-services .left-text-content {
  margin-right: 15px;
}

.main-services .right-text-content i,
.main-services .left-text-content i {
  font-size: 64px;
  color: #43ba7f;
  background-color: #ecf8f2;
  width: 120px;
  height: 120px;
  text-align: center;
  display: inline-block;
  line-height: 120px;
  border-radius: 5px;
}

.main-services .right-text-content h4,
.main-services .left-text-content h4 {
  font-size: 36px;
  font-weight: 700;
  color: #212741;
  margin-top: 40px;
  margin-bottom: 30px;
}

section.service-details .naccs {
  position: relative;
}

section.service-details .naccs .menu {
  text-align: center;
}

section.service-details .naccs .menu div {
  padding: 15px 30px;
  display: inline-block;
  text-align: center;
  border-radius: 5px !important;
  margin-right: 30px;
  font-size: 18px;
  color: #212741;
  background-color: #fff;
  cursor: pointer;
  vertical-align: middle;
  font-weight: 600;
  transition: 1s all cubic-bezier(0.075, 0.82, 0.165, 1);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}

section.service-details .naccs .menu div.active {
  color: #ff511a;
}

section.service-details ul.nacc {
  margin-top: 50px !important;
  position: relative;
  min-height: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}

section.service-details ul.nacc li {
  background-color: #fff;
  overflow: hidden;
  opacity: 0;
  transform: translateX(50px);
  position: absolute;
  list-style: none;
  border-radius: 10px;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  transition: 1s all cubic-bezier(0.075, 0.82, 0.165, 1);
}

section.service-details ul.nacc li .left-image {
  position: absolute;
  margin-right: 50px;
  top: 50%;
  transform: translateY(-50%);
}

section.service-details ul.nacc li .left-image img {
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

section.service-details ul.nacc li .right-content {
  padding: 50px 50px 50px 0px;
  margin-left: 470px;
}

section.service-details ul.nacc li .right-content h4 {
  font-size: 24px;
  color: #212741;
  margin-bottom: 25px;
  font-weight: 700;
  line-height: 40px;
  margin-right: 120px;
}

section.service-details ul.nacc li .right-content ul {
  margin-top: 20px;
  margin-bottom: 20px;
}

section.service-details ul.nacc li .right-content span {
  display: block;
  font-size: 15px;
  font-weight: 500;
  color: #212741;
  font-style: italic;
  margin-bottom: 12px;
}

section.service-details ul.nacc li .right-content .last-span {
  margin-bottom: 0px;
}

section.service-details ul.nacc li .right-content p {
  margin-bottom: 30px;
}

section.service-details ul.nacc li.active {
  position: relative;
  transition-delay: 0.3s;
  z-index: 2;
  opacity: 1;
  transform: translateX(0px);
}



/* 
---------------------------------------------
contact us page
--------------------------------------------- 
*/

section.map .info-item {
  text-align: center;
  padding: 40px 30px;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
  margin-top: -60px;
  margin-left: 15px;
  margin-right: 15px;
  position: relative;
  z-index: 2;
  transition: all .5s;
}

section.map .info-item:hover {
  margin-top: -70px;
}

section.map .info-item i {
  font-size: 36px;
  color: #43ba7f;
  margin-bottom: 25px;
}

section.map .info-item h4 {
  font-size: 18px;
  color: #212741;
  font-weight: 600;
  margin-bottom: 12px;
}

section.map .info-item a {
  font-size: 15px;
  font-weight: 600;
  color: #43ba7f;
  transition: all 0.3s;
}

section.map .info-item a:hover {
  color: #ff511a;
}

#contact input {
  width: 100%;
  height: 50px;
  background-color: #f7f7f7;
  color: #212741;
  border-radius: 5px;
  border: none;
  padding: 0px 15px;
  font-size: 14px;
  outline: none;
  margin-bottom: 30px;
}

#contact textarea {
  width: 100%;
  height: 180px;
  max-height: 24px;
  min-height: 150px;
  background-color: #f7f7f7;
  color: #212741;
  border-radius: 5px;
  border: none;
  padding: 15px 15px;
  font-size: 14px;
  outline: none;
  margin-bottom: 30px;
}

#contact input::placeholder, 
#contact textarea::placeholder {
  color: #212741;
}
#contact button.orange-button {
  font-size: 14px;
  color: #fff;
  background-color: #ff511a;
  padding: 12px 30px;
  display: inline-block;
  border-radius: 5px;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.5px;
  border: none;
  transition: all .3s;
}

#contact button.orange-button:hover {
  background-color: #43ba7f;
}


/* 
---------------------------------------------
responsive
--------------------------------------------- 
*/


body {
  overflow-x: hidden;
}

@media (max-width: 992px) {
  .header-area .main-nav .nav li:last-child {
    display: none;
  }
  .header-area .main-nav .nav li ul li:last-child {
    display: inline-block;
  }
  .header-area {
    background-color: #212741;
  }
  .slide-inner .header-text {
    width: 80%;
  }
  .simple-cta {
    text-align: center;
  }
  section.simple-cta .buttons {
    justify-content: center;
  }
  section.about-us .naccs .menu div,
  section.service-details .naccs .menu div {
    margin-right: 15px;
    font-size: 15px;
    padding: 15px 20px;
  }
  section.about-us .right-content {
    margin-left: 0px;
    margin-top: 60px;
  }
  section.calculator .left-image {
    display: none;
  }
  .testimonials .item img {
    max-width: 200px;
    right: 50px;
  }
  section.partners .item {
    margin:15px; 
  }
  section.top-section .accordions {
    margin-left: 0px;
    margin-top: 45px;
  }
  .what-we-do .left-content {
    margin-right: 0px;
    margin-bottom: 45px;
  }
  .main-services .left-image {
    margin-right: 0px;
    margin-bottom: 30px;
  }
  .main-services .right-text-content {
    margin-left: 0px;
  }
  .main-services .right-image {
    margin-left: 0px;
    margin-top: 30px;
  }
  section.service-details ul.nacc li .left-image {
    position: relative;
    margin-right: 0px;
    transform: translateY(0px);
  }
  section.service-details ul.nacc li .left-image img {
    border-radius: 15px;
  }
  section.service-details ul.nacc li .right-content {
    padding: 50px;
    margin-left: 0px;
  }
  section.service-details ul.nacc li .right-content h4 {
    margin-right: 0px;
  }
  section.service-details ul.nacc li.active {
    display: inline-block;
  }
  section.service-details ul.nacc li {
    display: none;
  }
  section.map .info-item {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .header-area .main-nav .logo {
    line-height: 100px;
  }
  .background-header .main-nav .logo {
    line-height: 80px;
  }
  .header-area .main-nav .nav li.has-sub ul.sub-menu {
    display: none;
  }
  .slide-inner .header-text p {
    display: none;
  }
  .slide-inner .header-text {
    text-align: center;
    width: 80%;
    margin-left: 10%;
    margin-right: 10%;
  }
  .slide-inner .header-text .div-dec {
    margin-left: auto;
    margin-right: auto;
  }
  .slide-inner .header-text .buttons {
    display: inline-flex;
  }
  .slide-inner .header-text .buttons .green-button {
    float: none;
  }
  .services .service-item i {
    margin-bottom: 35px;
  }
  section.about-us ul.nacc li span.item-title {
    font-size: 14px !important;
  }
  section.about-us ul.nacc li span.item {
    font-size: 13px;
  }
  .testimonials .item i {
    margin-bottom: 60px;
  }
  .testimonials .item img {
    max-width: 100px;
    right: 50px;
    top: 35px;
    transform: translateY(0);
  }
  .testimonials .item {
    padding: 50px;
  }
}