Vvveb.Sections.add("hero/hero-1", {
	name: "Hero 1",
	image: Vvveb.sectionsBaseUrl + "/screenshots/hero/hero-1-thumb.jpeg",
	html: `<header class="hero-1" title="hero-1">

  <div class="heading">
    <h1>Open Source CMS
      <br /> Reinvented
    </h1>

    <h2>Powerful and easy to use drag and drop builder
      <br /> for blogs, websites or ecommerce stores.
    </h2>

    <div class="btns">
      <a class="btn btn-lg btn-primary" href="download.php" role="button">
        <span>&#9889;</span> Free download <b>›</b>
      </a>

      <div class="nav-item dropdown">
        <a class="btn btn-lg btn-outline-primary nav-link" href="//demo.vvveb.com" role="button">Live demo <span>&rarr;</span>
          <b>›</b>
        </a>
        <div class="dropdown-menu" aria-labelledby="dropdown01">
          <a class="dropdown-item" target="_blank" href="//demo.vvveb.com/admin/?module=/editor/editor">Page builder</a>
          <a class="dropdown-item" target="_blank" href="//demo.vvveb.com">Frontend</a>
        </div>
      </div>
    </div>

    <i class="text-muted">
      <small>* Note: Early Alpha Preview</small>
    </i>
  </div>


</header>`,
});
Vvveb.Sections.add("hero/hero-2", {
	name: "Hero 2",
	image: Vvveb.sectionsBaseUrl + "/screenshots/hero/hero-2-thumb.jpeg",
	html: `<header class="hero-2 overlay" title="hero-2">

  <div class="container">
    <div class="row align-items-center justify-content-between">
      <div class="col-lg-6 mb-5 ms-5" data-aos="fade-up" data-aos-delay="0">
        <h1 class="heading text-white">The next generation website builder</h1>
        <p class="text-white-50 mb-5">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
        <p>
          <a href="#" class="btn btn-white">Free Download</a>
          <a href="#" class="btn text-white">Live Demo</a>
        </p>
      </div>
      <div class="col-lg-5" data-aos="fade-up" data-aos-delay="100">
        <a href="https://www.youtube.com/watch?v=3xsP3u-CVO4" class="video-wrap glightbox">
          <span class="play-button">
            <i class="lni lni-play"></i>
          </span>
          <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" alt="Video image" class="img-fluid">
        </a>
      </div>
    </div>
  </div>

  <div class="separator bottom">
    <svg class="pricing-divider-img" enable-background="new 0 0 300 100" height="100px" id="Layer_1" preserveAspectRatio="none" version="1.1" viewBox="0 0 300 100" width="300px" x="0px" xml:space="preserve" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" y="0px">
      <path class="deco-layer deco-layer--1" d="M30.913,43.944c0,0,42.911-34.464,87.51-14.191c77.31,35.14,113.304-1.952,146.638-4.729
		c48.654-4.056,69.94,16.218,69.94,16.218v54.396H30.913V43.944z" fill="#FFFFFF" opacity="0.6"></path>
      <path class="deco-layer deco-layer--2" d="M-35.667,44.628c0,0,42.91-34.463,87.51-14.191c77.31,35.141,113.304-1.952,146.639-4.729
		c48.653-4.055,69.939,16.218,69.939,16.218v54.396H-35.667V44.628z" fill="#FFFFFF" opacity="0.6"></path>
      <path class="deco-layer deco-layer--3" d="M43.415,98.342c0,0,48.283-68.927,109.133-68.927c65.886,0,97.983,67.914,97.983,67.914v3.716
		H42.401L43.415,98.342z" fill="#FFFFFF" opacity="0.7"></path>
      <path class="deco-layer deco-layer--4" d="M-34.667,62.998c0,0,56-45.667,120.316-27.839C167.484,57.842,197,41.332,232.286,30.428
		c53.07-16.399,104.047,36.903,104.047,36.903l1.333,36.667l-372-2.954L-34.667,62.998z" fill="#FFFFFF"></path>
    </svg>
  </div>

</header>`,
});
Vvveb.Sections.add("hero/hero-3", {
	name: "Hero 3",
	image: Vvveb.sectionsBaseUrl + "/screenshots/hero/hero-3-thumb.jpeg",
	html: `<header class="hero-3 overlay" title="hero-3">

  <div class="container">
    <div>
      <div class="row align-items-center justify-content-center text-center">
        <div class="col-lg-12">
          <h1 class="heading text-white mb-3" data-aos="fade-up" data-aos-delay="100">The next generation website builder</h1>
          <h3 class="text-white" data-aos="fade-up" data-aos-delay="100">Powerful and easy to use drag and drop website builder for blogs,
            <br /> presentation or ecommerce stores.
          </h3>

          <div class="buttons" data-aos="fade-up" data-aos-delay="300">
            <a href="#" class="btn btn-primary text-white me-4">Free Download</a>
            <a href="#" class="btn btn-white text-white">Live Demo</a>
          </div>


        </div>
      </div>
    </div>
  </div>

  <div class="separator bottom">


    <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 41" width="100%" preserveAspectRatio="none">
      <defs>
        <style>
          .cls-1 {
            fill: #fff;
          }
        </style>
      </defs>
      <title>rough-edges-bottom</title>
      <path class="cls-1" d="M0,185l125-26,33,17,58-12s54,19,55,19,50-11,50-11l56,6,60-8,63,15v15H0Z" transform="translate(0 -159)" />
    </svg>
  </div>

</header>`,
});
Vvveb.Sections.add("hero/hero-4", {
	name: "Hero 4",
	image: Vvveb.sectionsBaseUrl + "/screenshots/hero/hero-4-thumb.jpeg",
	html: `<header class="hero-4 overlay" title="hero-4">

  <div class="container">
    <div class="row align-items-center justify-content-center text-center">
      <div class="col-lg-8" data-aos="fade-up" data-aos-delay="100">
        <h1 class="heading text-white mb-3">The next generation website builder</h1>
        <!-- h5 class="subheading text-white">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</h5 -->
        <div class="buttons">
          <a href="#" class="btn btn-outline-light me-4">Free Download</a>
          <a href="#" class="btn text-white me-4">Live demo</a>
        </div>
      </div>
    </div>
  </div>

</header>`,
});
Vvveb.Sections.add("hero/hero-5", {
	name: "Hero 5",
	image: Vvveb.sectionsBaseUrl + "/screenshots/hero/hero-5-thumb.jpeg",
	html: `<header class="hero-5 pt-5 pb-5 mt-0 align-items-center d-flex overlay" style="min-height: 100vh; background-size: cover; background-image: url('${Vvveb.sectionsBaseUrl}/img/demo/hero-3.jpg');" title="hero-5">
  <div class="container" style="z-index:2">
    <div class="row align-items-center d-flex justify-content-between">
      <div class="col-12 col-md-6 pb-5 order-2 order-sm-2 ">
        <h1 class="  text-white font-weight-bold mb-3 mt-5 display-3">The next generation website builder.</h1>
        <p class="lead text-white">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
        <div class=" d-flex mt-3 mb-1">
          <a class="btn btn-primary btn-lg  mt-md-3 " href="#" role="button">Download Now</a>
        </div>
      </div>
      <div class="col-12 col-md-6 order-sm-1 order-md-2  ">
        <div class="icon-wrap text-primary d-flex justify-content-md-center my-3">
          <button href="#" class="icon d-flex border-0 align-items-center justify-content-center bg-white text-dark shadow-lg rounded-circle " style="width:70px; height:70px">
            <i class="lni lni-play la-lg ms-1"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</header>`,
});
Vvveb.SectionsGroup["Hero"] = [
	"hero/hero-1",
	"hero/hero-2",
	"hero/hero-3",
	"hero/hero-4",
	"hero/hero-5",
];
Vvveb.Sections.add("features/features-1", {
	name: "Features 1",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-1-thumb.jpeg",
	html: `<section class="features-1 bg-alternate" title="features-1">
  <div class="container">
    <div class="row wrap">
      <div class="col-md-7 align-self-center">
        <div class="max-box">
          <span class="badge  bg-success rounded-pill px-3">new</span>
          <h3 class="mt-2">Code editor with syntax highglighting that updates in real time</h3>
          <div class="mt-4">
            <p>The html for sections blocks and components and this template are built using Bootstrap.</p>
            <p>Use any of the hundreds fonts from google fonts for your design.</p>
            <p>Powerful and easy to use drag and drop builder for blogs, websites or ecommerce stores.</p>
          </div>
          <a href="#">Learn More</a>
        </div>
      </div>
      <div class="col-md-5 col-md-5">
        <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day68-happy-cat.svg" class="img-fluid" />
      </div>
    </div>
    <div class="row wrap">
      <div class="col-md-6">
        <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day67-dog.svg" class="img-fluid" />
      </div>
      <div class="col-md-6 align-self-center">
        <span class="badge  bg-success rounded-pill px-3">on sale</span>
        <h3 class="mt-2">Intuitive building with simple drag and drop for sections, components and blocks</h3>
        <div class="mt-4">
          <p>The html for sections blocks and components and this template are built using Bootstrap.</p>
          <p>Use any of the hundreds fonts from google fonts for your design.</p>
          <p>Powerful and easy to use drag and drop builder for blogs, websites or ecommerce stores.</p>
        </div>
        <a href="#">Learn More</a>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-2", {
	name: "Features 2",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-2-thumb.jpeg",
	html: `<section class="features-2 py-5 bg-alternate" title="features-2">

  <div class="row no-gutters">
    <div class="col-lg-6">
      <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day68-happy-cat.svg" class="img-fluid">
    </div>

    <div class="col-lg-6 bg-info-gradiant text-white d-flex">

      <div class="text-box align-self-center">
        <h3 class="text-white mb-3">Code editor with syntax highlighting that updates in real time</h3>
        <p>The html for sections blocks and components and this template are built using Bootstrap 5</p>
        <ul class="list-block">
          <li class="my-2">
            <span>Intuitive building with simple drag and drop for sections, components and blocks/snippets.</span>
            </span>
          </li>
          <li class="my-2">
            <span>Code editor with syntax highglighting that updates in real time.</span>
          </li>
          <li class="my-2">
            <span>Search and insert CCO images directly into the page. </span>
          </li>
          <li class="my-2">
            <span>The html for sections blocks and components and this template are built using bootstrap 5.</span>
          </li>
        </ul>
        <a class="btn btn-outline-light btn-md mt-3" href="#">
          <span>Learn More</span>
        </a>
      </div>

    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-3", {
	name: "Features 3",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-3-thumb.jpeg",
	html: `<section class="features-3 py-5 bg-alternate" title="features-3">
  <div class="container">

    <div class="row">

      <div class="col-lg-6 d-flex align-items-center">
        <div class="row">
          <div class="col-md-12 mb-4">
            <div class="d-flex align-items-center">
              <div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="72" height="72" color="#1064ea" fill="#fff">
                  <polyline points="336 176 225.2 304 176 255.8" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px"></polyline>
                  <path d="M463.1,112.37C373.68,64.33,336.71,84.45,256,48,175.29,84.45,138.32,64.33,48.9,112.37,32.7,369.13,240.58,457.79,256,464,271.42,457.79,479.3,369.13,463.1,112.37Z" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px"></path>
                </svg>

              </div>
              <div class="p-4">
                <h6 class="text-muted">
                  <a href="#" class="linking">Live code edit</a>
                </h6>
                <p class="mt-3">Code editor with syntax highglighting for html css and javascript that updates in real time.</p>
              </div>
            </div>
          </div>
          <div class="col-md-12 mb-4">
            <div class="d-flex align-items-center">
              <div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="icons" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" style="enable-background:new 0 0 512 512;" xml:space="preserve" width="72" height="72" color="#1064ea" fill="#fff">
                  <path d="M419.1,337.45a3.94,3.94,0,0,0-6.1,0c-10.5,12.4-45,46.55-45,77.66,0,27,21.5,48.89,48,48.89h0c26.5,0,48-22,48-48.89C464,384,429.7,349.85,419.1,337.45Z" style="fill:none;stroke:currentColor;stroke-miterlimit:10;stroke-width:32px"></path>
                  <path d="M387,287.9,155.61,58.36a36,36,0,0,0-51,0l-5.15,5.15a36,36,0,0,0,0,51l52.89,52.89,57-57L56.33,263.2a28,28,0,0,0,.3,40l131.2,126a28.05,28.05,0,0,0,38.9-.1c37.8-36.6,118.3-114.5,126.7-122.9,5.8-5.8,18.2-7.1,28.7-7.1h.3A6.53,6.53,0,0,0,387,287.9Z" style="fill:none;stroke:currentColor;stroke-miterlimit:10;stroke-width:32px"></path>
                </svg>

              </div>
              <div class="p-4">
                <h6 class="text-muted">
                  <a href="#" class="linking">Drag and drop</a>
                </h6>
                <p class="mt-3">The html for sections blocks and components and this template are built using Bootstrap 5.</p>
              </div>
            </div>
          </div>
          <div class="col-md-12 mb-4">
            <div class="d-flex align-items-center">
              <div>

                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="icons" width="72" height="72" color="#1064ea" fill="#fff" stroke-width="28">
                  <path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" fill-rule="evenodd" d="M80,176a16,16,0,0,0-16,16V408c0,30.24,25.76,56,56,56H392c30.24,0,56-24.51,56-54.75V192a16,16,0,0,0-16-16Z"></path>
                  <path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" fill-rule="evenodd" d="M160,176V144a64,64,0,0,1,64-64h0a64,64,0,0,1,64,64v32"></path>
                </svg>

              </div>
              <div class="p-4">
                <h6 class="text-muted">
                  <a href="#" class="linking">Bootstrap 5</a>
                </h6>
                <p class="mt-3">The html for sections blocks and components and this template are built using Bootstrap 5.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day67-dog.svg" class="img-fluid" />
      </div>

      <div class="col-md-12 mt-3 text-center">
        <a class="btn btn-primary btn-md" href="#">View Details</a>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-4", {
	name: "Features 4",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-4-thumb.jpeg",
	html: `<section class="features-4 bg-info-gradiant" title="features-4">

  <div class="container">
    <div class="row">
      <div class="col text-center">
        <h2>Our product features</h2>
      </div>
    </div>


    <div class="row text-center justify-content-sm-center no-gutters">
      <div class="col-12 col-sm-10 col-md-8 col-lg-7 col-xl-3 m-auto">
        <div>

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="96" height="96" color="#1064ea" fill="#fff">
            <polyline points="336 176 225.2 304 176 255.8" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px"></polyline>
            <path d="M463.1,112.37C373.68,96.33,336.71,84.45,256,48,175.29,84.45,138.32,96.33,48.9,112.37,32.7,369.13,240.58,457.79,256,464,271.42,457.79,479.3,369.13,463.1,112.37Z" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px"></path>
          </svg>

          <h3 class="mt-3">Drag and drop</h3>

          <p class="text-muted mt-2">Intuitive building with simple drag and drop for sections, components and blocks/snippets.</p>
          <p class="mt-4">
            <a href="#">Learn More <i class="fas fa-angle-right"></i>
            </a>
          </p>
        </div>
      </div>
      <div class="col-12 col-sm-10 col-md-8 col-lg-7 col-xl-3 m-auto pt-5 pt-xl-0">
        <div>

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="icons" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" style="enable-background:new 0 0 512 512;" xml:space="preserve" width="96" height="96" color="#1064ea" fill="#fff">
            <path d="M419.1,337.45a3.94,3.94,0,0,0-6.1,0c-10.5,12.4-45,46.55-45,77.66,0,27,21.5,48.89,48,48.89h0c26.5,0,48-22,48-48.89C464,384,429.7,349.85,419.1,337.45Z" style="fill:none;stroke:currentColor;stroke-miterlimit:10;stroke-width:32px"></path>
            <path d="M387,287.9,155.61,58.36a36,36,0,0,0-51,0l-5.15,5.15a36,36,0,0,0,0,51l52.89,52.89,57-57L56.33,263.2a28,28,0,0,0,.3,40l131.2,126a28.05,28.05,0,0,0,38.9-.1c37.8-36.6,118.3-114.5,126.7-122.9,5.8-5.8,18.2-7.1,28.7-7.1h.3A6.53,6.53,0,0,0,387,287.9Z" style="fill:none;stroke:currentColor;stroke-miterlimit:10;stroke-width:32px"></path>
          </svg>

          <h3 class="mt-3">Live code edit</h3>

          <p class="text-muted mt-2">Code editor with syntax highglighting that updates in real time.</p>
          <p class="mt-4">
            <a href="#">Learn More <i class="fas fa-angle-right"></i>
            </a>
          </p>
        </div>
      </div>
      <div class="col-12 col-sm-10 col-md-8 col-lg-7 col-xl-3 m-auto pt-5 pt-xl-0">
        <div>

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="icons" width="96" height="96" color="#1064ea" fill="#fff" stroke-width="28">
            <path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" fill-rule="evenodd" d="M80,176a16,16,0,0,0-16,16V408c0,30.24,25.76,56,56,56H392c30.24,0,56-24.51,56-54.75V192a16,16,0,0,0-16-16Z"></path>
            <path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" fill-rule="evenodd" d="M160,176V144a96,96,0,0,1,96-96h0a96,96,0,0,1,96,96v32"></path>
          </svg>

          <h3 class="mt-3">Bootstrap 5</h3>

          <p class="text-muted mt-2">The html for sections blocks and components and this template are built using Bootstrap 5.</p>
          <p class="mt-4">
            <a href="#">Learn More <i class="fas fa-angle-right"></i>
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>

</section>`,
});
Vvveb.Sections.add("features/features-5", {
	name: "Features 5",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-5-thumb.jpeg",
	html: `<section class="features-5 py-5 bg-alternate" title="features-5">

  <div class="container">
    <div class="row align-items-center">
      <div class="col-12 col-md-8 col-lg-6 m-md-auto ms-lg-0 me-lg-auto">
        <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day67-dog.svg" class="img-fluid" />
      </div>
      <div class="col-12 col-lg-6 col-xl-5 ms-sm-auto pt-5 pt-lg-0">
        <h2>Open Source CMS Reinvented</h2>

        <div class="row pt-4 pt-xl-5">
          <div class="col-12 col-sm-6 col-xl-5">
            <h4>
              <strong>Drag and drop</strong>
            </h4>
            <p>Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>
          </div>
          <div class="col-12 col-sm-6 col-xl-5 m-auto pt-3 pt-sm-0">
            <h4>
              <strong>Live code edit</strong>
            </h4>
            <p>Code editor with syntax highglighting that updates in real time.</p>
          </div>
        </div>

        <div class="row pt-3">
          <div class="col-12 col-sm-6 col-xl-5">
            <h4>
              <strong>Bootstrap 5</strong>
            </h4>
            <p>The html for sections blocks and components and this template are built using bootstrap 5</p>
          </div>
          <div class="col-12 col-sm-6 col-xl-5 m-auto pt-3 pt-sm-0">
            <h4>
              <strong>Google fonts</strong>
            </h4>
            <p>Use any of the hundreds fonts from google fonts for your design</p>
          </div>
        </div>
      </div>
    </div>
  </div>

</section>`,
});
Vvveb.Sections.add("features/features-6", {
	name: "Features 6",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-6-thumb.jpeg",
	html: `<section class="features-6 py-5 bg-alternate" title="features-6">
  <div class="container">
    <div class="row text-center">
      <div class="col-12">
        <h2>Our product features</h2>
      </div>
    </div>
    <div class="row text-center justify-content-center">
      <div class="col-12 col-sm-4 col-xl-3 m-md-auto">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">
          <h3>Drag and drop</h3>
          <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-sm-4 col-xl-3 m-md-auto">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          <h3>Live code editor</h3>
          <p class="lead text-muted mt-4">Code editor with syntax highglighting that updates in real time</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-sm-4 col-xl-3 m-md-auto">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg">
          <h3>Bootstrap 5</h3>
          <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using bootrap 5</p>

          <a href="#">Learn More</a>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-7", {
	name: "Features 7",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-7-thumb.jpeg",
	html: `<section class="features-7 py-5 bg-alternate" title="features-7">
  <div class="container">
    <div class="row text-center">
      <div class="col-12">
        <h2>Our product features</h2>
      </div>
    </div>
    <div class="row text-center justify-content-center">
      <div class="col-12 col-md-6 col-lg-3">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">
          <h3>Drag and drop</h3>
          <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-md-6 col-lg-3">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          <h3>Live code editor</h3>
          <p class="lead text-muted mt-4">Code editor with syntax highglighting that updates in real time</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-md-6 col-lg-3">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg">
          <h3>Bootstrap 5</h3>
          <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using Bootstrap 5</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-md-6 col-lg-3">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/126-namaste-no-hand-shake.svg">
          <h3>Google fonts</h3>
          <p class="lead text-muted mt-4">Use any of the hundreds fonts from google fonts for your design</p>

          <a href="#">Learn More</a>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-8", {
	name: "Features 8",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-8-thumb.jpeg",
	html: `<section class="features-8 py-5 bg-alternate" title="features-8">
  <div class="container">
    <div class="row text-center">
      <div class="col-12">
        <h2>Our product features</h2>
      </div>
    </div>
    <div class="row text-start mt-5">
      <div class="col-12 col-md-4">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">
          </div>
          <div class="col-9">
            <h4>Drag and drop</h4>
            <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>

      <div class="col-12 col-md-4 pt-3 pt-sm-4 pt-md-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          </div>
          <div class="col-9">
            <h4>Live code editor</h4>
            <p class="lead text-muted mt-4">Code editor with syntax highglighting that updates in real time</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>

      <div class="col-12 col-md-4 pt-3 pt-sm-4 pt-md-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg">
          </div>
          <div class="col-9">
            <h4>Bootstrap 5</h4>
            <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using bootrap 5</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
    </div>

    <div class="row text-start pt-3 pt-sm-4 pt-md-5">
      <div class="col-12 col-md-4">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/126-namaste-no-hand-shake.svg">
          </div>
          <div class="col-9">
            <h4>Google fonts</h4>
            <p class="lead text-muted mt-4">Use any of the hundreds fonts from google fonts for your design</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>

      <div class="col-12 col-md-4 pt-3 pt-sm-4 pt-md-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">
          </div>
          <div class="col-9">
            <h4>Drag and drop</h4>
            <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>

      <div class="col-12 col-md-4 pt-3 pt-sm-4 pt-md-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          </div>
          <div class="col-9">
            <h4>Bootstrap 5</h4>
            <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using bootrap 5</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-9", {
	name: "Features 9",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-9-thumb.jpeg",
	html: `<section class="features-9 py-5 bg-alternate" title="features-9">
  <div class="container">
    <div class="row text-center">
      <div class="col-12">
        <h2>Our product features</h2>
      </div>
    </div>
    <div class="row text-start mt-5">
      <div class="col-12 col-sm-6 col-lg-3">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">
          </div>
          <div class="col-9">
            <h4>Drag and drop</h4>
            <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 pt-3 pt-sm-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          </div>
          <div class="col-9">
            <h4>Live code editor</h4>
            <p class="lead text-muted mt-4">Code editor with syntax highglighting that updates in real time</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 pt-3 pt-lg-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg">
          </div>
          <div class="col-9">
            <h4>Bootstrap 5</h4>
            <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using bootrap 5</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 pt-3 pt-lg-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/126-namaste-no-hand-shake.svg">
          </div>
          <div class="col-9">
            <h4>Google fonts</h4>
            <p class="lead text-muted mt-4">Use any of the hundreds fonts from google fonts for your design</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
    </div>

    <div class="row text-start pt-3 pt-lg-5">
      <div class="col-12 col-sm-6 col-lg-3">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">
          </div>
          <div class="col-9">
            <h4>Drag and drop</h4>
            <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 pt-3 pt-sm-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          </div>
          <div class="col-9">
            <h4>Bootstrap 5</h4>
            <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using bootrap 5</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 pt-3 pt-lg-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg">

          </div>
          <div class="col-9">
            <h4>Drag and drop</h4>
            <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 pt-3 pt-lg-0">
        <div class="row">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          </div>
          <div class="col-9">
            <h4>Live code editor</h4>
            <p class="lead text-muted mt-4">Code editor with syntax highglighting that updates in real time</p>
            <a href="#">Learn More</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-10", {
	name: "Features 10",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-10-thumb.jpeg",
	html: `<section class="features-10 py-5 bg-alternate" title="features-10">
  <div class="container">
    <div class="row text-end align-items-center">
      <div class="col-12 col-md-6 m-auto">
        <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day67-dog.svg" class="img-fluid" />
      </div>

      <div class="col-12 col-md-7 col-lg-5 m-auto text-start pt-5 pt-md-0">
        <div class="row pb-lg-5">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg">
          </div>
          <div class="col-9">
            <h3>
              <strong>Drag and drop</strong>
            </h3>
            <p>Even the all-powerful Pointing has no control about the blind texts.</p>
          </div>
        </div>

        <div class="row pt-4 pt-md-5 pb-lg-5">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg" class="img-fluid">
          </div>
          <div class="col-9">
            <h3>
              <strong>Live code editor</strong>
            </h3>
            <p>Duden flows by their place far far away, behind the word mountains.</p>
          </div>
        </div>


        <div class="row pt-4 pt-md-5">
          <div class="col-3">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg" class="img-fluid">
          </div>
          <div class="col-9">
            <h3>
              <strong>Bootstrap 5 Components</strong>
            </h3>
            <p>A small river named Duden flows by their place and supplies it.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("features/features-11", {
	name: "Features 11",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/features/features-11-thumb.jpeg",
	html: `<section class="features-11 py-5 bg-alternate" title="features-11">
  <div class="container">
    <div class="row text-center">
      <div class="col-12 col-md-8 col-lg-4">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/109-map-location.svg" class="img-fluid">
          <h3>Drag and drop</h3>
          <p class="lead text-muted mt-4">Intuitive building with simple drag and drop for sections, components and blocks/snippets.</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-md-8 col-lg-4">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg" class="img-fluid">
          <h3>Live code editor</h3>
          <p class="lead text-muted mt-4">Code editor with syntax highglighting that updates in real time.</p>

          <a href="#">Learn More</a>
        </div>
      </div>

      <div class="col-12 col-md-8 col-lg-4">
        <div>
          <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg" class="img-fluid">
          <h3>Bootstrap 5 Components</h3>
          <p class="lead text-muted mt-4">The html for sections blocks and components and this template are built using bootstrap 5.</p>

          <a href="#">Learn More</a>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Features"] = [
	"features/features-1",
	"features/features-2",
	"features/features-3",
	"features/features-4",
	"features/features-5",
	"features/features-6",
	"features/features-7",
	"features/features-8",
	"features/features-9",
	"features/features-10",
	"features/features-11",
];
Vvveb.Sections.add("posts/posts-1", {
	name: "Posts 1",
	image: Vvveb.sectionsBaseUrl + "/screenshots/posts/posts-1-thumb.jpeg",
	html: `<section class="posts-1" title="latest-post-1">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="section-heading text-center">
          <h2>Latest Posts</h2>
        </div>
      </div>
    </div>
  </div>



  <div class="container" data-v-component-posts="popular" data-v-limit="4">
    <div class="row">



      <div class="col-12 col-lg-4 mb-2" data-v-post>

        <article class="card">
          <div class="card-img-top">
            <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" alt="" data-v-post-image>
          </div>
          <!-- Post Title -->
          <div class="card-body">
            <div class="post-title card-title">
              <a href="#" class="text-dark" data-v-post-url>
                <h6 data-v-post-name>
                  Vivamus sed nunc in arcu cursus mollis quis et orci. Interdum et malesuada
                </h6>
              </a>
            </div>
            <!-- Hover Content -->
            <p class="card-text text-muted" data-v-post-excerpt>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce enim nulla, mollis eu metus in, sagittis fringilla tortor. Phasellus purus dignissim convallis.</p>
            <a href="#" class="" data-v-post-url>
              <span>Read more</span>
              <i class="lni lni-angle-right"></i>
            </a>
          </div>
        </article>


      </div>



      <div class="col-12 col-lg-4 mb-2" data-v-post>

        <article class="card">
          <div class="card-img-top">
            <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" alt="" data-v-post-image>
          </div>
          <!-- Post Title -->
          <div class="card-body">
            <div class="post-title card-title">
              <a href="#" class="text-dark" data-v-post-url>
                <h6 data-v-post-name>
                  Vivamus sed nunc in arcu cursus mollis quis et orci. Interdum et malesuada
                </h6>
              </a>
            </div>
            <!-- Hover Content -->
            <p class="card-text text-muted" data-v-post-excerpt>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce enim nulla, mollis eu metus in, sagittis fringilla tortor. Phasellus purus dignissim convallis.</p>
            <a href="#" class="" data-v-post-url>
              <span>Read more</span>
              <i class="lni lni-angle-right"></i>
            </a>
          </div>
        </article>


      </div>



      <div class="col-12 col-lg-4 mb-2" data-v-post>

        <article class="card">
          <div class="card-img-top">
            <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" alt="" data-v-post-image>
          </div>
          <!-- Post Title -->
          <div class="card-body">
            <div class="post-title card-title">
              <a href="#" class="text-dark" data-v-post-url>
                <h6 data-v-post-name>
                  Vivamus sed nunc in arcu cursus mollis quis et orci. Interdum et malesuada
                </h6>
              </a>
            </div>
            <!-- Hover Content -->
            <p class="card-text text-muted" data-v-post-excerpt>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce enim nulla, mollis eu metus in, sagittis fringilla tortor. Phasellus purus dignissim convallis.</p>
            <a href="#" class="" data-v-post-url>
              <span>Read more</span>
              <i class="lni lni-angle-right"></i>
            </a>
          </div>
        </article>


      </div>



      <div class="col-12 col-lg-4 mb-2" data-v-post>

        <article class="card">
          <div class="card-img-top">
            <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" alt="" data-v-post-image>
          </div>
          <!-- Post Title -->
          <div class="card-body">
            <div class="post-title card-title">
              <a href="#" class="text-dark" data-v-post-url>
                <h6 data-v-post-name>
                  Vivamus sed nunc in arcu cursus mollis quis et orci. Interdum et malesuada
                </h6>
              </a>
            </div>
            <!-- Hover Content -->
            <p class="card-text text-muted" data-v-post-excerpt>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce enim nulla, mollis eu metus in, sagittis fringilla tortor. Phasellus purus dignissim convallis.</p>
            <a href="#" class="" data-v-post-url>
              <span>Read more</span>
              <i class="lni lni-angle-right"></i>
            </a>
          </div>
        </article>


      </div>



    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("posts/posts-2", {
	name: "Posts 2",
	image: Vvveb.sectionsBaseUrl + "/screenshots/posts/posts-2-thumb.jpeg",
	html: `<section class="pt-5 pb-5" title="posts-2">
  <div class="container">
    <div class="row">
      <div class="col-6">
        <h3 class="mb-3">Carousel cards title </h3>
      </div>
      <div class="col-6 text-end">
        <a class="btn btn-primary mb-3 me-1" href="#carouselExampleIndicators2" role="button" data-slide="prev">
          <i class="lni lni-arrow-left"></i>
        </a>
        <a class="btn btn-primary mb-3 " href="#carouselExampleIndicators2" role="button" data-slide="next">
          <i class="lni lni-arrow-right"></i>
        </a>
      </div>
      <div class="col-12">
        <div id="carouselExampleIndicators2" class="carousel slide" data-ride="carousel">

          <div class="carousel-inner">
            <div class="carousel-item active">
              <div class="row">

                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1532781914607-2031eca2f00d?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=7c625ea379640da3ef2e24f20df7ce8d">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>

                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1517760444937-f6397edcbbcd?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=42b2d9ae6feb9c4ff98b9133addfb698">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=3d2e8a2039c06dd26db977fe6ac6186a">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>
                  </div>
                </div>

              </div>
            </div>
            <div class="carousel-item">
              <div class="row">

                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1532771098148-525cefe10c23?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=3f317c1f7a16116dec454fbc267dd8e4">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>

                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1532715088550-62f09305f765?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=ebadb044b374504ef8e81bdec4d0e840">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=0754ab085804ae8a3b562548e6b4aa2e">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>
                  </div>
                </div>

              </div>
            </div>
            <div class="carousel-item">
              <div class="row">

                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=ee8417f0ea2a50d53a12665820b54e23">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>

                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1532777946373-b6783242f211?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=8ac55cf3a68785643998730839663129">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>

                    </div>
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card">
                    <img class="img-fluid" alt="100%x280" src="https://images.unsplash.com/photo-1532763303805-529d595877c5?ixlib=rb-0.3.5&amp;q=80&amp;fm=jpg&amp;crop=entropy&amp;cs=tinysrgb&amp;w=1080&amp;fit=max&amp;ixid=eyJhcHBfaWQiOjMyMDc0fQ&amp;s=5ee4fd5d19b40f93eadb21871757eda6">
                    <div class="card-body">
                      <h4 class="card-title">Special title treatment</h4>
                      <p class="card-text">With supporting text below as a natural lead-in to additional content.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("posts/post-3", {
	name: "Post 3",
	image: Vvveb.sectionsBaseUrl + "/screenshots/posts/post-3-thumb.jpeg",
	html: `<section class="py-5" title="posts-1">
  <div class="container">

    <div class="row justify-content-center">

      <div class="col-md-8 text-center">
        <h3 class="mb-3">Latest from Our Blog</h3>
        <h6 class="subtitle font-weight-normal">You can relay on our amazing features list and also our customer services will be great experience for you without doubt</h6>
      </div>

    </div>

    <div class="row mt-4">

      <div class="col-md-4">
        <div class="card b-h-box position-relative font-14 border-0 mb-4">
          <img class="card-img" src="https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/blog/blog-home/img9.jpg" alt="Card image">
          <div class="card-img-overlay overflow-hidden">
            <div class="d-flex align-items-center">
              <span class="bg-danger-gradiant badge overflow-hidden text-white px-3 py-1 font-weight-normal">Charity, Ngo</span>
              <div class="ml-2">
                <span class="ml-2">Feb 18, 2018</span>
              </div>
            </div>
            <h5 class="card-title my-3 font-weight-normal">Help out the people who really need it on time.</h5>
            <p class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod...</p>
          </div>
        </div>
      </div>


      <div class="col-md-4">
        <div class="card b-h-box position-relative font-14 border-0 mb-4">
          <img class="card-img" src="https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/blog/blog-home/img10.jpg" alt="Card image">
          <div class="card-img-overlay overflow-hidden">
            <div class="d-flex align-items-center">
              <span class="bg-danger-gradiant badge overflow-hidden text-white px-3 py-1 font-weight-normal">Charity, Ngo</span>
              <div class="ml-2">
                <span class="ml-2">Feb 18, 2018</span>
              </div>
            </div>
            <h5 class="card-title my-3 font-weight-normal">Help out the people who really need it on time.</h5>
            <p class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod...</p>
          </div>
        </div>
      </div>


      <div class="col-md-4">
        <div class="card b-h-box position-relative font-14 border-0 mb-4">
          <img class="card-img" src="https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/blog/blog-home/img11.jpg" alt="Card image">
          <div class="card-img-overlay overflow-hidden">
            <div class="d-flex align-items-center">
              <span class="bg-danger-gradiant badge overflow-hidden text-white px-3 py-1 font-weight-normal">Charity, Ngo</span>
              <div class="ml-2">
                <span class="ml-2">Feb 18, 2018</span>
              </div>
            </div>
            <h5 class="card-title my-3 font-weight-normal">Help out the people who really need it on time.</h5>
            <p class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod...</p>
          </div>
        </div>
      </div>

    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("posts/post-4", {
	name: "Post 4",
	image: Vvveb.sectionsBaseUrl + "/screenshots/posts/post-4-thumb.jpeg",
	html: `<section class="py-5" title="posts-2">
  <div class="container">

    <div class="row justify-content-center">

      <div class="col-md-8 text-center">
        <h3 class="my-3">Upcoming Events</h3>
        <h6 class="subtitle font-weight-normal">You can relay on our amazing features list and also our customer services will be great experience for you without doubt</h6>
      </div>


    </div>
    <div class="row mt-4">

      <div class="col-md-4 on-hover">
        <div class="card border-0 mb-4">
          <a href="#">
            <img class="card-img-top" src="https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/blog/blog-home/img3.jpg" alt="wrappixel kit">
          </a>
          <div class="date-pos bg-info-gradiant p-2 d-inline-block text-center rounded text-white position-absolute">Oct<span class="d-block">23</span>
          </div>
          <h5 class="font-weight-medium mt-3">
            <a href="#" class="text-decoration-none link">You should have eagle’s eye on new trends and techonogies</a>
          </h5>
          <p class="mt-3">Business Park, Opp. Corns Sam Restaurant, New Yoark, US</p>
          <a href="#" class="text-decoration-none linking text-themecolor mt-2">Learn More</a>
        </div>
      </div>

      <div class="col-md-4 on-hover">
        <div class="card border-0 mb-4">
          <a href="#">
            <img class="card-img-top" src="https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/blog/blog-home/img2.jpg" alt="wrappixel kit">
          </a>
          <div class="date-pos bg-info-gradiant p-2 d-inline-block text-center rounded text-white position-absolute">Oct<span class="d-block">23</span>
          </div>
          <h5 class="font-weight-medium mt-3">
            <a href="#" class="text-decoration-none link">New Seminar on Newest Food Recipe from World’s Best</a>
          </h5>
          <p class="mt-3">Business Park, Opp. Corns Sam Restaurant, New Yoark, US</p>
          <a href="#" class="text-decoration-none linking text-themecolor mt-2">Learn More</a>
        </div>
      </div>

      <div class="col-md-4 on-hover">
        <div class="card border-0 mb-4">
          <a href="#">
            <img class="card-img-top" src="https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/blog/blog-home/img1.jpg" alt="wrappixel kit">
          </a>
          <div class="date-pos bg-info-gradiant p-2 d-inline-block text-center rounded text-white position-absolute">Oct<span class="d-block">23</span>
          </div>
          <h5 class="font-weight-medium mt-3">
            <a href="#" class="text-decoration-none link">Learn from small things to create something bigger.</a>
          </h5>
          <p class="mt-3">Business Park, Opp. Corns Sam Restaurant, New Yoark, US</p>
          <a href="#" class="text-decoration-none linking text-themecolor mt-2">Learn More</a>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("posts/post-5", {
	name: "Post 5",
	image: Vvveb.sectionsBaseUrl + "/screenshots/posts/post-5-thumb.jpeg",
	html: `<section class="blog_section" title="posts-5">
  <div class="container">
    <div class="blog_content">
      <div class="owl-carousel owl-theme">
        <div class="blog_item">
          <div class="blog_image">
            <img class="img-fluid" src="https://cdn.pixabay.com/photo/2019/03/10/18/31/hong-kong-4046913_960_720.jpg" alt="images not found">
            <span>
              <i class="icon ion-md-create"></i>
            </span>
          </div>
          <div class="blog_details">
            <div class="blog_title">
              <h5>
                <a href="#">We are best for any industrial & business solution.</a>
              </h5>
            </div>
            <ul>
              <li>
                <i class="icon ion-md-person"></i>Alexa
              </li>
              <li>
                <i class="icon ion-md-calendar"></i>August 1, 2020
              </li>
            </ul>
            <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem...</p>
            <a href="#">Read More<i class="icofont-long-arrow-right"></i>
            </a>
          </div>
        </div>
        <div class="blog_item">
          <div class="blog_image">
            <img class="img-fluid" src="https://cdn.pixabay.com/photo/2015/07/09/22/45/tree-838667_960_720.jpg" alt="images not found">
            <span>
              <i class="icon ion-md-create"></i>
            </span>
          </div>
          <div class="blog_details">
            <div class="blog_title">
              <h5>
                <a href="#">We are best for any industrial & business solution.</a>
              </h5>
            </div>
            <ul>
              <li>
                <i class="icon ion-md-person"></i>Alexa
              </li>
              <li>
                <i class="icon ion-md-calendar"></i>August 1, 2020
              </li>
            </ul>
            <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem...</p>
            <a href="#">Read More<i class="icofont-long-arrow-right"></i>
            </a>
          </div>
        </div>
        <div class="blog_item">
          <div class="blog_image">
            <img class="img-fluid" src="https://cdn.pixabay.com/photo/2015/10/30/20/13/sunrise-1014712_960_720.jpg" alt="images not found">
            <span>
              <i class="icon ion-md-create"></i>
            </span>
          </div>
          <div class="blog_details">
            <div class="blog_title">
              <h5>
                <a href="#">We are best for any industrial & business solution.</a>
              </h5>
            </div>
            <ul>
              <li>
                <i class="icon ion-md-person"></i>Alexa
              </li>
              <li>
                <i class="icon ion-md-calendar"></i>August 1, 2020
              </li>
            </ul>
            <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem...</p>
            <a href="#">Read More<i class="icofont-long-arrow-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("posts/post-6", {
	name: "Post 6",
	image: Vvveb.sectionsBaseUrl + "/screenshots/posts/post-6-thumb.jpeg",
	html: `<section class="details-card" title="posts-6">
  <div class="container">
    <div class="row">
      <div class="col-md-4">
        <div class="card-content">
          <div class="card-img">
            <img src="https://placeimg.com/380/230/nature" alt="">
            <span>
              <h4>heading</h4>
            </span>
          </div>
          <div class="card-desc">
            <h3>Heading</h3>
            <p>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Laboriosam, voluptatum! Dolor quo, perspiciatis
              voluptas totam</p>
            <a href="#" class="btn-card">Read</a>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card-content">
          <div class="card-img">
            <img src="https://placeimg.com/380/230/animals" alt="">
            <span>
              <h4>heading2</h4>
            </span>
          </div>
          <div class="card-desc">
            <h3>Heading2</h3>
            <p>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Laboriosam, voluptatum! Dolor quo, perspiciatis
              voluptas totam</p>
            <a href="#" class="btn-card">Read</a>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card-content">
          <div class="card-img">
            <img src="https://placeimg.com/380/230/tech" alt="">
            <span>
              <h4>heading3</h4>
            </span>
          </div>
          <div class="card-desc">
            <h3>Heading3</h3>
            <p>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Laboriosam, voluptatum! Dolor quo, perspiciatis
              voluptas totam</p>
            <a href="#" class="btn-card">Read</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Posts"] = [
	"posts/posts-1",
	"posts/posts-2",
	"posts/post-3",
	"posts/post-4",
	"posts/post-5",
	"posts/post-6",
];
Vvveb.Sections.add("contact-form/contact-form-2", {
	name: "Contact form 2",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-2-thumb.jpeg",
	html: `<section class="contact-form-2 container-fluid px-5 my-5" title="contact-form-2">
  <div class="row justify-content-center">
    <div class="col-xl-10">
      <div class="card border-0 rounded-3 shadow-lg ">
        <div class="card-body p-0">
          <div class="row g-0">
            <div class="col-sm-6 d-none d-sm-block bg-image">

              <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day68-happy-cat.svg" class="img-fluid">

            </div>
            <div class="col-sm-6 p-4">
              <div class="text-center">
                <div class="h3 fw-light">Contact Form</div>
                <p class="mb-4 text-muted">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
              </div>


              <form id="contactForm">


                <div class=" mb-3">
                  <input class="form-control" id="name" type="text" placeholder="Name" required />
                  <label for="name">Name</label>
                </div>


                <div class=" mb-3">
                  <input class="form-control" id="emailAddress" type="email" placeholder="Email Address" required />
                  <label for="emailAddress">Email Address</label>
                </div>


                <div class=" mb-3">
                  <textarea class="form-control" id="message" type="text" placeholder="Message" style="height: 10rem;" required></textarea>
                  <label for="message">Message</label>
                </div>




                <div class="d-grid">
                  <button class="btn btn-primary btn-lg disabled" id="submitButton" type="submit">Submit</button>
                </div>
              </form>


            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-3", {
	name: "Contact form 3",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-3-thumb.jpeg",
	html: `<section class="contact-form-3 py-5" title="contact-form-3">
  <div class="row no-gutters">
    <div class="container">
      <div class="row">
        <div class="col-lg-6">
          <div class="card-shadow">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/day68-happy-cat.svg" class="img-fluid">
          </div>
        </div>
        <div class="col-lg-6">
          <div class="contact-box ms-3">
            <h1 class=" mt-2">Quick Contact</h1>
            <form class="mt-4">
              <div class="row">
                <div class="col-lg-12">
                  <div class="form-group mt-2">
                    <input class="form-control" type="text" placeholder="name">
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="form-group mt-2">
                    <input class="form-control" type="email" placeholder="email address">
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="form-group mt-2">
                    <input class="form-control" type="text" placeholder="phone">
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="form-group mt-2">
                    <textarea class="form-control" rows="3" placeholder="message"></textarea>
                  </div>
                </div>
                <div class="col-lg-12">
                  <button type="submit" class="btn btn-success mt-3 text-white border-0 px-3 py-2">
                    <span> Submit</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div class="col-lg-12">
          <div class="card mt-4 border-0 mb-4">
            <div class="row">
              <div class="col-lg-4 col-md-4">
                <div class="card-body d-flex align-items-center c-detail pl-0">
                  <div class="mr-3 align-self-center">
                    <i class="lni lni-map fa-lg"></i>
                  </div>
                  <div class="">
                    <h6 class="">Address</h6>
                    <p class="">708 Picadilly Ave.
                      <br> New York
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-md-4">
                <div class="card-body d-flex align-items-center c-detail">
                  <div class="mr-3 align-self-center">
                    <i class="lni lni-phone fa-lg"></i>
                  </div>
                  <div class="">
                    <h6 class="">Phone</h6>
                    <p class="">555 111 333</p>
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-md-4">
                <div class="card-body d-flex align-items-center c-detail">
                  <div class="mr-3 align-self-center">
                    <i class="lni lni-email fa-lg"></i>
                  </div>
                  <div class="">
                    <h6 class="">Email</h6>
                    <p class="">
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-6", {
	name: "Contact form 6",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-6-thumb.jpeg",
	html: `<section title="contact-form-6" class="contact-form-6 bg-alternate">
  <div class="container-fluid p-0 pb-md-5">
    <iframe class="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2848.8444388087937!2d26.101253041406952!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40b1ff4770adb5b7%3A0x58147f39579fe6fa!2zR3J1cHVsIFN0YXR1YXIgIkPEg3J1yJthIEN1IFBhaWHIm2Ui!5e0!3m2!1sen!2sro!4v1507381157656" style="border:0" allowfullscreen="" width="100%" height="300" frameborder="0"></iframe>
  </div>
  <div class="container">
    <div class="row mt-5">
      <div class="col-12 col-md-6 col-lg-5">
        <h2>Contact Us</h2>
        <p class="lead">
          Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.
        </p>

        <p class="lead">
          The html for sections blocks and components and this template are built using Bootstrap 5.
        </p>


        <p class="h3 mt-5">
          <strong>Email:</strong>
          <a href="#"><EMAIL></a>
        </p>
        <p class="lead">
          <strong>Phone:</strong>
          <a href="#">+55 (111) 123 777</a>
        </p>
      </div>

      <div class="col-12 col-md-6 ms-auto pt-5 pt-md-0">
        <form>
          <div class="row">
            <div class="col">
              <input type="text" class="form-control" placeholder="First name">
            </div>
            <div class="col">
              <input type="text" class="form-control" placeholder="Last name">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <input type="email" class="form-control" placeholder="Enter email">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <input type="email" class="form-control" placeholder="Subject">
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <textarea class="form-control" name="message" rows="3" placeholder="How can we help?"></textarea>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <button type="submit" class="btn btn-primary">Submit</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-8", {
	name: "Contact form 8",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-8-thumb.jpeg",
	html: `<section class="contact-form-8" title="contact-form-8">
  <div class="container">
    <div class="row text-center justify-content-center">
      <div class="col-12 col-md-8 col-lg-7">
        <h1>Contact Us</h1>
        <p class="lead">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
      </div>
    </div>

    <div class="row pt-4">
      <div class="col-12 col-md-6">
        <iframe class="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2848.8444388087937!2d26.101253041406952!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40b1ff4770adb5b7%3A0x58147f39579fe6fa!2zR3J1cHVsIFN0YXR1YXIgIkPEg3J1yJthIEN1IFBhaWHIm2Ui!5e0!3m2!1sen!2sro!4v1507381157656" style="border:0" allowfullscreen="" width="100%" height="300" frameborder="0"></iframe>
      </div>

      <div class="col-12 col-md-6 pt-5 pt-md-0">
        <form>
          <div class="row">
            <div class="col">
              <input type="email" class="form-control" placeholder="Enter email">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <input type="email" class="form-control" placeholder="Subject">
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <textarea class="form-control" name="message" rows="3" placeholder="How can we help?"></textarea>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <button type="submit" class="btn btn-primary">Submit</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-9", {
	name: "Contact form 9",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-9-thumb.jpeg",
	html: `<section class="contact-form-9" title="contact-form-9">
  <div class="container py-5 my-5">
    <div class="row py-5">
      <div class="col py-5">
        <div>
          <div class="row text-center justify-content-center">
            <div class="col-12 col-md-9 col-lg-7">
              <h1>Contact Us</h1>
              <p class="lead">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
            </div>
          </div>

          <div class="row justify-content-center pt-4">
            <div class="col-12 col-md-8">
              <form>
                <div class="row">
                  <div class="col-12 col-md">
                    <input type="text" class="form-control" placeholder="Name">
                  </div>
                  <div class="col-12 col-md mt-4 mt-md-0">
                    <input type="text" class="form-control" placeholder="Email">
                  </div>
                </div>

                <div class="row mt-4">
                  <div class="col">
                    <input type="email" class="form-control" placeholder="Subject">
                  </div>
                </div>

                <div class="row mt-4">
                  <div class="col">
                    <textarea class="form-control" name="message" rows="3" placeholder="How can we help?"></textarea>
                  </div>
                </div>
                <div class="row mt-4">
                  <div class="col text-center">
                    <button type="submit" class="btn btn-primary">Send</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-10", {
	name: "Contact form 10",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-10-thumb.jpeg",
	html: `<section class="contact-form-10 pt-0" title="contact-form-10">
  <div class="container-fluid p-0 pb-3">
    <iframe class="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2848.8444388087937!2d26.101253041406952!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40b1ff4770adb5b7%3A0x58147f39579fe6fa!2zR3J1cHVsIFN0YXR1YXIgIkPEg3J1yJthIEN1IFBhaWHIm2Ui!5e0!3m2!1sen!2sro!4v1507381157656" style="border:0" allowfullscreen="" width="100%" height="300" frameborder="0"></iframe>
  </div>
  <div class="container">
    <div class="row text-center justify-content-center pt-5">
      <div class="col-12 col-md-7">
        <h1>Contact Us</h1>
      </div>
    </div>

    <div class="row justify-content-center pt-4">
      <div class="col-12 col-md-7">
        <form>
          <div class="row">
            <div class="col">
              <input type="text" class="form-control" placeholder="Email">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <input type="email" class="form-control" placeholder="Subject">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <textarea class="form-control" name="message" rows="3" placeholder="How can we help?"></textarea>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col text-center">
              <button type="submit" class="btn btn-primary">Send</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="row-100"></div>
  </div>
  <div class="bg-dark">
    <div class="container">
      <div class="row-50"></div>
      <div class="row justify-content-center text-center">
        <div class="col-12 col-md me-auto ms-auto">
          <img alt="image" class="mb-2" src="./imgs/icons/phone.svg" height="40">
          <p class="lead">+55 (111) 123 777</p>
        </div>

        <div class="col-12 col-md pt-4 pt-md-0 me-auto ms-auto">
          <img alt="image" class="mb-2" src="./imgs/icons/navigation.svg" height="40">
          <p class="lead">7th St.
            <br>New York, NY 12345
          </p>
        </div>

        <div class="col-12 col-md pt-4 pt-md-0 me-auto ms-auto">
          <img alt="image" class="mb-2" src="./${Vvveb.sectionsBaseUrl}/img/mail.svg" height="40">
          <p class="lead"><EMAIL></p>
        </div>
      </div>
      <div class="row-50"></div>
    </div>
  </div>

  <div class="container">
    <div class="row-70"></div>
    <div class="row text-center">
      <div class="col">
        <p class="h2">
          <a href="#" class="mx-2">
            <i class="lni lni-facebook"></i>
          </a>
          <a href="#" class="mx-2">
            <i class="lni lni-twitter"></i>
          </a>
          <a href="#" class="mx-2">
            <i class="lni lni-instagram"></i>
          </a>
          <a href="#" class="mx-2">
            <i class="lni lni-google"></i>
          </a>
          <a href="#" class="mx-2">
            <i class="lni lni-pinterest"></i>
          </a>
        </p>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-11", {
	name: "Contact form 11",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-11-thumb.jpeg",
	html: `<section class="contact-form-11 pt-0" title="contact-form-11">
  <div class="container-fluid p-0 pb-5">
    <iframe class="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2848.8444388087937!2d26.101253041406952!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40b1ff4770adb5b7%3A0x58147f39579fe6fa!2zR3J1cHVsIFN0YXR1YXIgIkPEg3J1yJthIEN1IFBhaWHIm2Ui!5e0!3m2!1sen!2sro!4v1507381157656" style="border:0" allowfullscreen="" width="100%" height="300" frameborder="0"></iframe>
  </div>
  <div class="container">
    <div class="row pt-5">
      <div class="col-12">
        <form>
          <div class="row">
            <div class="col-12 col-md">
              <label>First Name</label>
              <input type="text" class="form-control">
            </div>
            <div class="col-12 col-md mt-4 mt-md-0">
              <label>Last Name</label>
              <input type="text" class="form-control">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <label>Your Email</label>
              <input type="email" class="form-control">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <label>Subject (optional but helpful)</label>
              <input type="email" class="form-control">
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <label>How can we help?</label>
              <textarea class="form-control" name="message" rows="3"></textarea>
            </div>
          </div>
          <div class="row mt-4 text-center">
            <div class="col">
              <button type="submit" class="btn btn-primary">Submit</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-12", {
	name: "Contact form 12",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-12-thumb.jpeg",
	html: `<section class="contact-form-12 py-0" title="contact-form-12">
  <div class="container py-5" style="background-image: url(${Vvveb.sectionsBaseUrl}/img/sections/contct-form-12.jpg);">
    <div class="row text-center justify-content-center">
      <div class="col-12 col-md-8 col-lg-7">
        <h1>Contact Us</h1>
        <p class="lead">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
      </div>
    </div>
    <div class="row-50">
    </div>
    <div class="row justify-content-center">
      <div class="col-12 col-md-8 col-lg-7">
        <form>
          <div class="row">
            <div class="col">
              <label>Your Email Address</label>
              <input type="text" class="form-control">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <label>Subject</label>
              <input type="email" class="form-control">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <label>How can we help?</label>
              <textarea class="form-control" name="message" rows="3"></textarea>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col text-end">
              <button type="submit" class="btn btn-dark">Send</button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="row-100"></div>
  </div>

  <div class="container-fluid p-0">
    <iframe class="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2848.8444388087937!2d26.101253041406952!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40b1ff4770adb5b7%3A0x58147f39579fe6fa!2zR3J1cHVsIFN0YXR1YXIgIkPEg3J1yJthIEN1IFBhaWHIm2Ui!5e0!3m2!1sen!2sro!4v1507381157656" style="border:0" allowfullscreen="" width="100%" height="300" frameborder="0"></iframe>
  </div>
</section>`,
});
Vvveb.Sections.add("contact-form/contact-form-13", {
	name: "Contact form 13",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/contact-form/contact-form-13-thumb.jpeg",
	html: `<section class="contact-form-13" title="contact-form-13">
  <div class="container">
    <div class="row text-center justify-content-center">
      <div class="col-12 col-md-8 col-lg-7">
        <h1>Contact Us</h1>
        <p class="lead">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
      </div>
    </div>
    <div class="row-70"></div>
    <div class="row">
      <div class="col-12 col-md-8 col-lg m-auto">
        <form>
          <div class="row">
            <div class="col">
              <input type="email" class="form-control" placeholder="Enter email">
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <input type="email" class="form-control" placeholder="Subject">
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <textarea class="form-control" name="message" rows="3" placeholder="How can we help?"></textarea>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <button type="submit" class="btn btn-primary">Submit</button>
            </div>
          </div>
        </form>
      </div>

      <div class="col-12 col-md-8 col-lg pt-5 m-auto pt-lg-0">
        <iframe class="mb-4" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2848.8444388087937!2d26.101253041406952!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40b1ff4770adb5b7%3A0x58147f39579fe6fa!2zR3J1cHVsIFN0YXR1YXIgIkPEg3J1yJthIEN1IFBhaWHIm2Ui!5e0!3m2!1sen!2sro!4v1507381157656" style="border:0" allowfullscreen="" width="100%" height="200" frameborder="0"></iframe>

        <p>
          <strong>Showroom</strong>
        </p>
        <p>
          71 Pilgrim Avenue
          <br>Chevy Chase, MD 20815
        </p>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Contact form"] = [
	"contact-form/contact-form-2",
	"contact-form/contact-form-3",
	"contact-form/contact-form-6",
	"contact-form/contact-form-8",
	"contact-form/contact-form-9",
	"contact-form/contact-form-10",
	"contact-form/contact-form-11",
	"contact-form/contact-form-12",
	"contact-form/contact-form-13",
];
Vvveb.Sections.add("navigation/navigation-1", {
	name: "Navigation 1",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/navigation/navigation-1-thumb.jpeg",
	html: `<nav id="top" class="top-nav clearfix" title="navigation-1">
  <div class="container">
    <div class="nav float-start">
      <ul class="list-inline">
        <li class="list-inline-item">
          <a href="#">
            <i class="lni lni-phone"></i>
          </a>
          <span class="d-none d-md-inline"> +55 (111) 123 777</span>
        </li>
        <li class="list-inline-item">
          <a href="#">
            <i class="lni lni-envelope"></i>
          </a>
          <span class="d-none d-md-inline"><EMAIL></span>
        </li>
        <li class="list-inline-item"></li>
      </ul>
    </div>
    <div class="nav float-end">
      <ul class="list-inline">
        <!--
                <li class="list-inline-item"><a href="#" id="wishlist-total" title="Wish List (0)"><i class="lni lni-heart"></i> <span class="d-none d-md-inline">Favorites</span></a></li>
                <li class="list-inline-item"><a href="#" title="Checkout"><i class="lni lni-share"></i> <span class="d-none d-md-inline">Checkout</span></a></li>
                <li class="list-inline-item">
                    <div class="dropdown">
                        <a href="" class="dropdown-toggle" data-bs-toggle="dropdown"><i class="lni lni-user"></i> <span class="d-none d-md-inline">My Account</span></i></a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="#" class="dropdown-item">Register</a></li>
                            <li><a href="#" class="dropdown-item">Login</a></li>
                        </ul>
                    </div>
                </li>
                -->
        <li class="list-inline-item">
          <div data-v-component-currency>
            <form method="post" enctype="multipart/form-data" id="form-language">

              <a type="button" class="dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <!-- <i class="lni lni-dollar-sign me-1"></i> -->
                <span class="d-none d-md-inline" data-v-currency-info-name>USD</span>
              </a>


              <div class="dropdown-menu dropdown-menu-end">

                <div data-v-currency>
                  <button class="dropdown-item" value="EUR" name="currency" data-v-currency-code>
                    <span data-v-currency-symbol_left>€</span>
                    <span data-v-currency-symbol_right>€</span>
                    <span data-v-currency-name>Euro</span>
                  </button>
                </div>

                <div data-v-currency>
                  <button class="dropdown-item" value="GBP" name="currency" data-v-currency-code>
                    <span data-v-currency-symbol_left>£</span>
                    <span data-v-currency-symbol_right>£</span>
                    <span data-v-currency-name>Pound Sterling</span>
                  </button>
                </div>

                <div data-v-currency>
                  <button class="dropdown-item" value="USD" name="currency" data-v-currency-code>
                    <span data-v-currency-symbol_left>$</span>
                    <span data-v-currency-symbol_right>$</span>
                    <span data-v-currency-name>US Dollar</span>
                  </button>
                </div>

              </div>
            </form>
          </div>
        </li>
        <li class="list-inline-item">
          <div data-v-component-language>
            <form method="post" enctype="multipart/form-data" id="form-language">

              <a type="button" class="dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <!-- <i class="lni lni-flag me-1"></i> -->
                <!-- 
									<img src="" data-v-language-info-img>
									-->
                <span class="d-none d-md-inline" data-v-language-info-name>English</span>
              </a>


              <div class="dropdown-menu dropdown-menu-end">

                <div data-v-language>
                  <button class="dropdown-item" value="eng" name="language" data-v-language-code>
                    <!-- <i class="lni lni-flag la-lg me-2"></i> -->
                    <img src="" data-v-language-img>
                    <span data-v-language-name>English</span>
                  </button>
                </div>

                <div data-v-language>
                  <button class="dropdown-item" value="ro" name="language" data-v-language-code>
                    <!-- <i class="lni lni-flag la-lg me-2"></i> -->
                    <img src="" data-v-language-img>
                    <span data-v-language-name>Romanian</span>
                  </button>
                </div>

              </div>
            </form>
          </div>

        </li>
      </ul>
    </div>
  </div>
</nav>



<nav class="navigation-1 clearfix">

  <div class="navbar navbar-expand-md">

    <div class="container">

      <a class="navbar-brand" href="/" data-v-url-params='{"host":"www.*.*"}'>
        <!-- img src="${Vvveb.sectionsBaseUrl}/img/logo-white.png" class="logo-default-dark" -->
        <img src="${Vvveb.sectionsBaseUrl}/img/logo.png" class="logo-default">
        <img src="${Vvveb.sectionsBaseUrl}/img/logo.png" class="logo-sticky">
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-target="#navbar" aria-controls="navbar" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbar" data-v-component-menu="header" data-v-slug="main-menu">
        <ul class="navbar-nav ms-auto" data-v-cats>
          <li class="nav-item dropdown" data-v-cat data-v-class-if-has-dropdown="category.children > 0">

            <a class="nav-link" href="https://themes.vvveb.com" data-v-if="category.children <= 0" data-v-cat-url data-v-cat-name>Services</a>

            <a class="nav-link dropdown-toggle" href="#" data-v-if="category.children > 0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-cat-url data-v-cat-name>Resources</a>

            <div class="dropdown-menu" data-v-cat-recursive>
              <div data-v-cat class="nav-item" data-v-cat data-v-class-if-dropdown="category.children > 0">
                <a class="dropdown-item" href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>User Documentation</a>
              </div>
              <div data-v-cat class="nav-item" data-v-cat data-v-class-if-dropdown="category.children > 0">
                <a class="dropdown-item" href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>Developer Documentation</a>
              </div>
            </div>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://blog.vvveb.com" data-v-cat-url data-v-cat-name>Blog</a>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://www.vvveb.com/page/contact" data-v-cat-url data-v-cat-name>Contact</a>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://www.vvveb.com" data-v-cat-url data-v-cat-name>About us</a>
          </li>
          <li>
            <!-- User Login Info -->
            <div class="dropdown user-login-info nav-item">
              <a class="dropdown-toggle nav-link " href role="button" id="user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-url="user/index">
                <img src="${Vvveb.sectionsBaseUrl}/img/user.svg" width="20" alt>
              </a>

              <div class="dropdown-menu dropdown-menu-end login-box p-4" aria-labelledby="user-dropdown">

                <div data-v-component-user>


                  <div data-v-errors>

                    <div data-v-error class="alert alert-danger d-flex" role="alert">
                      <span class="text-danger align-middle flex-grow-1">
                        <i class="lni lni-2x la-exclamation-circle pe-2">
                          <!-- &#10006;-->
                        </i>
                      </span>
                      <span data-v-error-text class="align-middle">This is an error message!</span>
                    </div>

                  </div>

                  <form action method="post" enctype="multipart/form-data" data-v-url="user/login/index" data-v-vvveb-action="login" data-v-vvveb-on="submit">

                    <input type="hidden" name="csrf" data-v-csrf>

                    <div class="login-form" data-v-if-not="component.user_id">

                      <div class="mb-3">
                        <label class="control-label" for="input-email">E-Mail Address</label>
                        <input type="email" name="email" value placeholder="E-Mail Address" id="input-email" class="form-control" required>
                      </div>

                      <div class="mb-3">
                        <label class="control-label" for="input-password">Password</label>
                        <input type="password" minlength="4" autocorrect="off" name="password" value placeholder="Password" id="input-password" class="form-control" required>
                      </div>

                      <button type="submit" value="Login" class="btn btn-primary  w-100">

                        <span class="loading d-none">
                          <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true">
                          </span>
                          <span>Authenticating</span>...
                        </span>

                        <span class="button-text">
                          Login
                        </span>

                      </button>
                      <div class="my-2"></div>
                      <a href="/user/recover" data-v-url="user/recover/index" class="my-2">Forgotten Password</a>

                      <div class="my-2"></div>
                      <a href="#">
                        <span class="btn btn-outline-primary btn-sm">
                          <i class="lab la-google la-lg"></i>
                        </span>
                      </a>
                      <a href="#">
                        <span class="btn btn-outline-primary btn-sm">
                          <i class="lab la-facebook la-lg"></i>
                        </span>
                      </a>
                      <hr>
                      <strong>New Customer</strong>
                      <a href="/user/signup" data-v-url="user/signup/index">Register Account</a>

                    </div>


                    <div class="user-form" data-v-if="component.user_id">
                      <div>Welcome <b data-v-display_name>John Doe</b>
                      </div>

                      <ul class="m-2">
                        <li>
                          <a href="#">Comments</a>
                        </li>
                        <li>
                          <a href="#">Orders</a>
                        </li>
                        <li>
                          <a href="#">Downloads</a>
                        </li>
                        <li>
                          <a href="#">Profile</a>
                        </li>
                      </ul>


                      <input type="hidden" name="logout">

                      <button type="submit" value="logout" class="btn btn-primary  w-100">

                        <span class="loading d-none">
                          <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true">
                          </span>
                          <span>Loading ...</span>...
                        </span>

                        <span class="button-text">
                          Logout
                        </span>

                      </button>
                    </div>
                  </form>



                </div>
              </div>
            </div>

          </li>
          <li>
            <!-- Cart Area -->
            <div class="dropdown nav-item" data-v-component-cart>

              <a class="dropdown-toggle cart-info nav-link " href role="button" id="cart-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-url="cart/cart/index">
                <img src="${Vvveb.sectionsBaseUrl}/img/bag.svg" width="20" alt>
                <strong class="text-top text-bold" data-v-total-items>3</strong>
              </a>


              <div class="dropdown-menu dropdown-menu-end cart-box" aria-labelledby="cart-dropdown">

                <div>
                  <div class="table-responsive">
                    <table class="table cart-table align-middle mb-0">
                      <tbody>


                        <tr data-v-cart-product>
                          <td class="text-center">
                            <a href="#40" data-v-cart-product-url>
                              <img src="#" alt="iPhone" title="" class="img-rounded" data-v-cart-product-img width=50>
                            </a>
                          </td>
                          <td class="text-start">
                            <a href="#40" class="d-block" data-v-cart-product-url data-v-cart-product-name>
                              iPhone 5
                            </a>

                            <span data-v-cart-product-amount>1</span>
                            <span data-v-cart-product-price>$123.20</span>
                          </td>

                        </tr>
                        <tr data-v-if-not="cart.total_items">
                          <td colspan="100">
                            <div class="d-flex  p-2">
                              <div class="text-center p-2 opacity-75">
                                <img src="${Vvveb.sectionsBaseUrl}/img/bag.svg" width="50" alt="Shopping cart">
                              </div>
                              <div class="p-2">
                                <strong>Empty cart</strong>
                                <br>
                                <span class="text-muted">No products added yet!</span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>

                    </table>
                  </div>

                  <div class="p-3 pt-0 bg-light" data-v-if="cart.total_items">
                    <div class="table-responsive">
                      <table class="table mb-0 cart-table" cellspacing="0">
                        <tfoot>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Sub-Total:</small>
                            </td>
                            <td class="text-end" data-v-cart-total>$101.00</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Eco Tax (-2.00):</small>
                            </td>
                            <td class="text-end">$2.00</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>VAT (20%):</small>
                            </td>
                            <td class="text-end">$20.20</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Total:</small>
                            </td>
                            <td class="text-end">$123.20</td>
                          </tr>
                        </tfoot>

                      </table>
                    </div>

                  </div>

                  <div class="row mt-2 g-2 px-3 pb-2" data-v-if="cart.total_items">
                    <div class="col-6">
                      <a href="" class="btn btn-light btn-sm border w-100" data-v-url="cart/cart/index">
                        <i class="lni lni-shopping-cart la-lg"></i>
                        <span>View cart</span>
                      </a>
                    </div>
                    <div class="col-6">
                      <a href="" class="btn btn-primary btn-sm w-100" data-v-url="checkout/checkout/index">
                        <span>Checkout</span>
                        <i class="lni lni-arrow-right la-lg"></i>
                      </a>
                    </div>
                  </div>


                </div>
              </div>

            </div>
          </li>
        </ul>

        <form class="d-flex">

          <div class="input-group">
            <!-- input type="text" class="form-control" placeholder="Search for..." -->
            <span class="input-group-append">
              <button class="btn btn-light la-flip-horizontal" type="button">

                <i class="lni lni-search"></i>


              </button>
            </span>
          </div>

        </form>
      </div>
      <!--
		  <div class="collapse navbar-collapse" id="navbar">
			<ul class="navbar-nav ms-auto">
			  <li class="nav-item">
				<a class="nav-link" href="#features">Features <span class="sr-only">(current)</span></a>
			  </li>
			  <li class="nav-item">
				<a class="nav-link" href="https://themes.vvveb.com">Themes</a>
			  </li>
			  <li class="nav-item">
				<a class="nav-link" href="https://plugins.vvveb.com">Plugins</a>
			  </li>
			  <li class="nav-item dropdown">
				<a class="nav-link dropdown-toggle" href="#" id="dropdown01" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Resources</a>
				<div class="dropdown-menu" aria-labelledby="dropdown01">
				  <a class="dropdown-item" target="_blank"  href="https://github.com/givanz/VvvebJs/wiki">User Documentation</a>
				  <a class="dropdown-item" target="_blank"  href="https://github.com/givanz/VvvebJs/wiki">Developer Documentation</a>
				</div>
			  </li>
			  <li class="nav-item">
				<a class="nav-link" target="_blank" href="https://github.com/givanz/VvvebJs">Github</a>
			  </li>
			  <li class="nav-item">
				<a class="nav-link" href="contact.html">Contact</a>
			  </li>
			  <li class="nav-item active">
				<a class="nav-link" href="//vvveb.com/download.php">Download for free</a>
			  </li>			  
			</ul>
		  </div>
		  -->
    </div>

  </div>

</nav>`,
});
Vvveb.Sections.add("navigation/navigation-2", {
	name: "Navigation 2",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/navigation/navigation-2-thumb.jpeg",
	html: `<nav class="navigation-2 clearfix fixed-top transparent" title="navigation-2">

  <div class="container">

    <div class="navbar navbar-expand-md navbar-dark">
      <a class="navbar-brand" href="/" data-v-url-params='{"host":"www.*.*"}'>
        <img src="${Vvveb.sectionsBaseUrl}/img/logo-white.png" class="logo-default">
        <img src="${Vvveb.sectionsBaseUrl}/img/logo.png" class="logo-sticky">
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-target="#navbar" aria-controls="navbar" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbar" data-v-component-menu="header" data-v-slug="main-menu">
        <ul class="navbar-nav ms-auto" data-v-cats>
          <li class="nav-item dropdown" data-v-cat data-v-class-if-has-dropdown="category.children > 0">

            <a class="nav-link" href="https://themes.vvveb.com" data-v-if="category.children <= 0" data-v-cat-url data-v-cat-name>Services</a>

            <a class="nav-link dropdown-toggle" href="#" data-v-if="category.children > 0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-cat-url data-v-cat-name>Resources</a>

            <div class="dropdown-menu" data-v-cat-recursive>
              <div data-v-cat class="nav-item" data-v-cat data-v-class-if-dropdown="category.children > 0">
                <a class="dropdown-item" href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>User Documentation</a>
              </div>
              <div data-v-cat class="nav-item" data-v-cat data-v-class-if-dropdown="category.children > 0">
                <a class="dropdown-item" href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>Developer Documentation</a>
              </div>
            </div>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://blog.vvveb.com" data-v-cat-url data-v-cat-name>Blog</a>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://www.vvveb.com/page/contact" data-v-cat-url data-v-cat-name>Contact</a>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://www.vvveb.com" data-v-cat-url data-v-cat-name>About us</a>
          </li>
          <li>
            <!-- User Login Info -->
            <div class="dropdown user-login-info nav-item">
              <a class="dropdown-toggle nav-link " href role="button" id="user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-url="user/index">
                <img src="${Vvveb.sectionsBaseUrl}/img/user.svg" width="20" alt>
              </a>

              <div class="dropdown-menu dropdown-menu-end login-box p-4" aria-labelledby="user-dropdown">

                <div data-v-component-user>


                  <div data-v-errors>

                    <div data-v-error class="alert alert-danger d-flex" role="alert">
                      <span class="text-danger align-middle flex-grow-1">
                        <i class="lni lni-2x la-exclamation-circle pe-2">
                          <!-- &#10006;-->
                        </i>
                      </span>
                      <span data-v-error-text class="align-middle">This is an error message!</span>
                    </div>

                  </div>

                  <form action method="post" enctype="multipart/form-data" data-v-url="user/login/index" data-v-vvveb-action="login" data-v-vvveb-on="submit">

                    <input type="hidden" name="csrf" data-v-csrf>

                    <div class="login-form" data-v-if-not="component.user_id">

                      <div class="mb-3">
                        <label class="control-label" for="input-email">E-Mail Address</label>
                        <input type="email" name="email" value placeholder="E-Mail Address" id="input-email" class="form-control" required>
                      </div>

                      <div class="mb-3">
                        <label class="control-label" for="input-password">Password</label>
                        <input type="password" minlength="4" autocorrect="off" name="password" value placeholder="Password" id="input-password" class="form-control" required>
                      </div>

                      <button type="submit" value="Login" class="btn btn-primary  w-100">

                        <span class="loading d-none">
                          <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true">
                          </span>
                          <span>Authenticating</span>...
                        </span>

                        <span class="button-text">
                          Login
                        </span>

                      </button>
                      <div class="my-2"></div>
                      <a href="/user/recover" data-v-url="user/recover/index" class="my-2">Forgotten Password</a>

                      <div class="my-2"></div>
                      <a href="#">
                        <span class="btn btn-outline-primary btn-sm">
                          <i class="lab la-google la-lg"></i>
                        </span>
                      </a>
                      <a href="#">
                        <span class="btn btn-outline-primary btn-sm">
                          <i class="lab la-facebook la-lg"></i>
                        </span>
                      </a>
                      <hr>
                      <strong>New Customer</strong>
                      <a href="/user/signup" data-v-url="user/signup/index">Register Account</a>

                    </div>


                    <div class="user-form" data-v-if="component.user_id">
                      <div>Welcome <b data-v-display_name>John Doe</b>
                      </div>

                      <ul class="m-2">
                        <li>
                          <a href="#">Comments</a>
                        </li>
                        <li>
                          <a href="#">Orders</a>
                        </li>
                        <li>
                          <a href="#">Downloads</a>
                        </li>
                        <li>
                          <a href="#">Profile</a>
                        </li>
                      </ul>


                      <input type="hidden" name="logout">

                      <button type="submit" value="logout" class="btn btn-primary  w-100">

                        <span class="loading d-none">
                          <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true">
                          </span>
                          <span>Loading ...</span>...
                        </span>

                        <span class="button-text">
                          Logout
                        </span>

                      </button>
                    </div>
                  </form>



                </div>
              </div>
            </div>

          </li>
          <li>
            <!-- Cart Area -->
            <div class="dropdown nav-item" data-v-component-cart>

              <a class="dropdown-toggle cart-info nav-link " href role="button" id="cart-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-url="cart/cart/index">
                <img src="${Vvveb.sectionsBaseUrl}/img/bag.svg" width="20" alt>
                <strong class="text-top text-bold" data-v-total-items>3</strong>
              </a>


              <div class="dropdown-menu dropdown-menu-end cart-box" aria-labelledby="cart-dropdown">

                <div>
                  <div class="table-responsive">
                    <table class="table cart-table align-middle mb-0">
                      <tbody>


                        <tr data-v-cart-product>
                          <td class="text-center">
                            <a href="#40" data-v-cart-product-url>
                              <img src="#" alt="iPhone" title="" class="img-rounded" data-v-cart-product-img width=50>
                            </a>
                          </td>
                          <td class="text-start">
                            <a href="#40" class="d-block" data-v-cart-product-url data-v-cart-product-name>
                              iPhone 5
                            </a>

                            <span data-v-cart-product-amount>1</span>
                            <span data-v-cart-product-price>$123.20</span>
                          </td>

                        </tr>
                        <tr data-v-if-not="cart.total_items">
                          <td colspan="100">
                            <div class="d-flex  p-2">
                              <div class="text-center p-2 opacity-75">
                                <img src="${Vvveb.sectionsBaseUrl}/img/bag.svg" width="50" alt="Shopping cart">
                              </div>
                              <div class="p-2">
                                <strong>Empty cart</strong>
                                <br>
                                <span class="text-muted">No products added yet!</span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>

                    </table>
                  </div>

                  <div class="p-3 pt-0 bg-light" data-v-if="cart.total_items">
                    <div class="table-responsive">
                      <table class="table mb-0 cart-table" cellspacing="0">
                        <tfoot>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Sub-Total:</small>
                            </td>
                            <td class="text-end" data-v-cart-total>$101.00</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Eco Tax (-2.00):</small>
                            </td>
                            <td class="text-end">$2.00</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>VAT (20%):</small>
                            </td>
                            <td class="text-end">$20.20</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Total:</small>
                            </td>
                            <td class="text-end">$123.20</td>
                          </tr>
                        </tfoot>

                      </table>
                    </div>

                  </div>

                  <div class="row mt-2 g-2 px-3 pb-2" data-v-if="cart.total_items">
                    <div class="col-6">
                      <a href="" class="btn btn-light btn-sm border w-100" data-v-url="cart/cart/index">
                        <i class="lni lni-shopping-cart la-lg"></i>
                        <span>View cart</span>
                      </a>
                    </div>
                    <div class="col-6">
                      <a href="" class="btn btn-primary btn-sm w-100" data-v-url="checkout/checkout/index">
                        <span>Checkout</span>
                        <i class="lni lni-arrow-right la-lg"></i>
                      </a>
                    </div>
                  </div>


                </div>
              </div>

            </div>
          </li>
        </ul>
      </div>
    </div>


  </div>

</nav>`,
});
Vvveb.Sections.add("navigation/navigation-3", {
	name: "Navigation 3",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/navigation/navigation-3-thumb.jpeg",
	html: `<nav class="navigation-3 clearfix fixed-top transparent" title="navigation-3">

  <div class="navbar navbar-expand-md navbar-dark">

    <div class="container">

      <a class="navbar-brand" href="/" data-v-url-params='{"host":"www.*.*"}'>
        <img src="${Vvveb.sectionsBaseUrl}/img/logo-white.png" class="logo-default-dark">
        <!-- img src="${Vvveb.sectionsBaseUrl}/img/logo.png" class="logo-default" -->
        <img src="${Vvveb.sectionsBaseUrl}/img/logo.png" class="logo-sticky">
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-target="#navbar" aria-controls="navbar" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbar" data-v-component-menu="header" data-v-slug="main-menu">
        <ul class="navbar-nav ms-auto" data-v-cats>
          <li class="nav-item dropdown" data-v-cat data-v-class-if-has-dropdown="category.children > 0">

            <a class="nav-link" href="https://themes.vvveb.com" data-v-if="category.children <= 0" data-v-cat-url data-v-cat-name>Services</a>

            <a class="nav-link dropdown-toggle" href="#" data-v-if="category.children > 0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-cat-url data-v-cat-name>Resources</a>

            <div class="dropdown-menu" data-v-cat-recursive>
              <div data-v-cat class="nav-item" data-v-cat data-v-class-if-dropdown="category.children > 0">
                <a class="dropdown-item" href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>User Documentation</a>
              </div>
              <div data-v-cat class="nav-item" data-v-cat data-v-class-if-dropdown="category.children > 0">
                <a class="dropdown-item" href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>Developer Documentation</a>
              </div>
            </div>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://blog.vvveb.com" data-v-cat-url data-v-cat-name>Blog</a>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://www.vvveb.com/page/contact" data-v-cat-url data-v-cat-name>Contact</a>
          </li>
          <li class="nav-item" data-v-cat>
            <a class="nav-link" href="https://www.vvveb.com" data-v-cat-url data-v-cat-name>About us</a>
          </li>

          <li>
            <!-- User Login Info -->
            <div class="dropdown user-login-info nav-item">
              <a class="dropdown-toggle nav-link " href role="button" id="user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-url="user/index">
                <img src="${Vvveb.sectionsBaseUrl}/img/user.svg" width="20" alt>
              </a>

              <div class="dropdown-menu dropdown-menu-end login-box p-4" aria-labelledby="user-dropdown">

                <div data-v-component-user>


                  <div data-v-errors>

                    <div data-v-error class="alert alert-danger d-flex" role="alert">
                      <span class="text-danger align-middle flex-grow-1">
                        <i class="lni lni-2x la-exclamation-circle pe-2">
                          <!-- &#10006;-->
                        </i>
                      </span>
                      <span data-v-error-text class="align-middle">This is an error message!</span>
                    </div>

                  </div>

                  <form action method="post" enctype="multipart/form-data" data-v-url="user/login/index" data-v-vvveb-action="login" data-v-vvveb-on="submit">

                    <input type="hidden" name="csrf" data-v-csrf>

                    <div class="login-form" data-v-if-not="component.user_id">

                      <div class="mb-3">
                        <label class="control-label" for="input-email">E-Mail Address</label>
                        <input type="email" name="email" value placeholder="E-Mail Address" id="input-email" class="form-control" required>
                      </div>

                      <div class="mb-3">
                        <label class="control-label" for="input-password">Password</label>
                        <input type="password" minlength="4" autocorrect="off" name="password" value placeholder="Password" id="input-password" class="form-control" required>
                      </div>

                      <button type="submit" value="Login" class="btn btn-primary  w-100">

                        <span class="loading d-none">
                          <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true">
                          </span>
                          <span>Authenticating</span>...
                        </span>

                        <span class="button-text">
                          Login
                        </span>

                      </button>
                      <div class="my-2"></div>
                      <a href="/user/recover" data-v-url="user/recover/index" class="my-2">Forgotten Password</a>

                      <div class="my-2"></div>
                      <a href="#">
                        <span class="btn btn-outline-primary btn-sm">
                          <i class="lab la-google la-lg"></i>
                        </span>
                      </a>
                      <a href="#">
                        <span class="btn btn-outline-primary btn-sm">
                          <i class="lab la-facebook la-lg"></i>
                        </span>
                      </a>
                      <hr>
                      <strong>New Customer</strong>
                      <a href="/user/signup" data-v-url="user/signup/index">Register Account</a>

                    </div>


                    <div class="user-form" data-v-if="component.user_id">
                      <div>Welcome <b data-v-display_name>John Doe</b>
                      </div>

                      <ul class="m-2">
                        <li>
                          <a href="#">Comments</a>
                        </li>
                        <li>
                          <a href="#">Orders</a>
                        </li>
                        <li>
                          <a href="#">Downloads</a>
                        </li>
                        <li>
                          <a href="#">Profile</a>
                        </li>
                      </ul>


                      <input type="hidden" name="logout">

                      <button type="submit" value="logout" class="btn btn-primary  w-100">

                        <span class="loading d-none">
                          <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true">
                          </span>
                          <span>Loading ...</span>...
                        </span>

                        <span class="button-text">
                          Logout
                        </span>

                      </button>
                    </div>
                  </form>



                </div>
              </div>
            </div>

          </li>
          <li>
            <!-- Cart Area -->
            <div class="dropdown nav-item" data-v-component-cart>

              <a class="dropdown-toggle cart-info nav-link " href role="button" id="cart-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-v-url="cart/cart/index">
                <img src="${Vvveb.sectionsBaseUrl}/img/bag.svg" width="20" alt>
                <strong class="text-top text-bold" data-v-total-items>3</strong>
              </a>


              <div class="dropdown-menu dropdown-menu-end cart-box" aria-labelledby="cart-dropdown">

                <div>
                  <div class="table-responsive">
                    <table class="table cart-table align-middle mb-0">
                      <tbody>


                        <tr data-v-cart-product>
                          <td class="text-center">
                            <a href="#40" data-v-cart-product-url>
                              <img src="#" alt="iPhone" title="" class="img-rounded" data-v-cart-product-img width=50>
                            </a>
                          </td>
                          <td class="text-start">
                            <a href="#40" class="d-block" data-v-cart-product-url data-v-cart-product-name>
                              iPhone 5
                            </a>

                            <span data-v-cart-product-amount>1</span>
                            <span data-v-cart-product-price>$123.20</span>
                          </td>

                        </tr>
                        <tr data-v-if-not="cart.total_items">
                          <td colspan="100">
                            <div class="d-flex  p-2">
                              <div class="text-center p-2 opacity-75">
                                <img src="${Vvveb.sectionsBaseUrl}/img/bag.svg" width="50" alt="Shopping cart">
                              </div>
                              <div class="p-2">
                                <strong>Empty cart</strong>
                                <br>
                                <span class="text-muted">No products added yet!</span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>

                    </table>
                  </div>

                  <div class="p-3 pt-0 bg-light" data-v-if="cart.total_items">
                    <div class="table-responsive">
                      <table class="table mb-0 cart-table" cellspacing="0">
                        <tfoot>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Sub-Total:</small>
                            </td>
                            <td class="text-end" data-v-cart-total>$101.00</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Eco Tax (-2.00):</small>
                            </td>
                            <td class="text-end">$2.00</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>VAT (20%):</small>
                            </td>
                            <td class="text-end">$20.20</td>
                          </tr>
                          <tr>
                            <td colspan="5" class="text-end">
                              <small>Total:</small>
                            </td>
                            <td class="text-end">$123.20</td>
                          </tr>
                        </tfoot>

                      </table>
                    </div>

                  </div>

                  <div class="row mt-2 g-2 px-3 pb-2" data-v-if="cart.total_items">
                    <div class="col-6">
                      <a href="" class="btn btn-light btn-sm border w-100" data-v-url="cart/cart/index">
                        <i class="lni lni-shopping-cart la-lg"></i>
                        <span>View cart</span>
                      </a>
                    </div>
                    <div class="col-6">
                      <a href="" class="btn btn-primary btn-sm w-100" data-v-url="checkout/checkout/index">
                        <span>Checkout</span>
                        <i class="lni lni-arrow-right la-lg"></i>
                      </a>
                    </div>
                  </div>


                </div>
              </div>

            </div>
          </li>
        </ul>
      </div>

      <!--
			  <div class="collapse navbar-collapse" id="navbar">
				<ul class="navbar-nav ms-auto">
				  <li class="nav-item">
					<a class="nav-link" href="#features">Features <span class="sr-only">(current)</span></a>
				  </li>
				  <li class="nav-item">
					<a class="nav-link" href="https://themes.vvveb.com">Themes</a>
				  </li>
				  <li class="nav-item">
					<a class="nav-link" href="https://plugins.vvveb.com">Plugins</a>
				  </li>
				  <li class="nav-item dropdown">
					<a class="nav-link dropdown-toggle" href="#" id="dropdown01" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Resources</a>
					<div class="dropdown-menu" aria-labelledby="dropdown01">
					  <a class="dropdown-item" target="_blank"  href="https://github.com/givanz/VvvebJs/wiki">User Documentation</a>
					  <a class="dropdown-item" target="_blank"  href="https://github.com/givanz/VvvebJs/wiki">Developer Documentation</a>
					</div>
				  </li>
				  <li class="nav-item">
					<a class="nav-link" target="_blank" href="https://github.com/givanz/VvvebJs">Github</a>
				  </li>
				  <li class="nav-item">
					<a class="nav-link" href="contact.html">Contact</a>
				  </li>
				  <li class="nav-item active">
					<a class="nav-link" href="//vvveb.com/download.php">Download for free</a>
				  </li>			  
				</ul>
			  </div>
			  -->
    </div>

  </div>

</nav>`,
});
Vvveb.SectionsGroup["Navigation"] = [
	"navigation/navigation-1",
	"navigation/navigation-2",
	"navigation/navigation-3",
];
Vvveb.Sections.add("footer/footer-1", {
	name: "Footer 1",
	image: Vvveb.sectionsBaseUrl + "/screenshots/footer/footer-1-thumb.jpeg",
	html: `<footer class="footer-1" title="footer-1">
  <div class="container" data-v-component-menu="footer" data-v-menu_id="5">

    <div class="row" data-v-cats>

      <div class="col-md-3">
        <img class="logo" src="${Vvveb.sectionsBaseUrl}/img/logo.png">
      </div>


      <div class="col-md-3" data-v-cat data-v-if="category.children > 0">
        <h6 data-v-cat-name>Vvveb</h6>
        <nav data-v-cat-recursive>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://themes.vvveb.com/" data-v-cat-url data-v-cat-name>Themes</a>
          </div>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://plugins.vvveb.com/" data-v-cat-url data-v-cat-name>Plugins</a>
          </div>
        </nav>
      </div>

      <div class="col-md-3" data-v-cat data-v-if="category.children > 0">
        <h6 data-v-cat-name>Resources</h6>
        <nav data-v-cat-recursive>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>User documentation</a>
          </div>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>Developer documentation</a>
          </div>
        </nav>
      </div>

      <div class="col-md-3" data-v-cat data-v-if="category.children > 0">
        <h6 data-v-cat-name>Contact</h6>
        <nav data-v-cat-recursive>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="" target="contact.html">Contact</a>
          </div>
        </nav>
      </div>



    </div>
    <!--
		<div class="row justify-content-end">
			<div class="col-md-3 text-muted text-small mt-5">
				&copy; <span data-v-year>2022</span> <span data-v-sitename>Vvveb</span>. Powered by <a href="//vvveb.com" target="_blank">Vvveb</a>	
			</div>
		</div>
		-->

  </div>

  <div class="footer-copyright">
    <div class="container">
      <div class="d-flex flex-column flex-md-row">
        <div class="text-muted flex-grow-1">
          <a class="btn-link text-muted" href="/page/terms-conditions" target="_blank">Terms and conditions</a> |
          <a class="btn-link text-muted" href="/page/privacy-policy" target="_blank">Privacy Policy</a>
        </div>
        <div class="text-muted">
          &copy; <span data-v-year>2022</span>
          <span data-v-sitename>Vvveb</span>. <span>Powered by</span>
          <a href="//vvveb.com" class="btn-link text-muted" target="_blank">Vvveb</a>
        </div>
      </div>
    </div>
  </div>

</footer>`,
});
Vvveb.Sections.add("footer/footer-2", {
	name: "Footer 2",
	image: Vvveb.sectionsBaseUrl + "/screenshots/footer/footer-2-thumb.jpeg",
	html: `<footer class="bg-white" title="footer-2">

  <div class="container py-5">
    <div class="row py-4">
      <div class="col-lg-4 col-md-6 mb-4 mb-lg-0">
        <img src="${Vvveb.sectionsBaseUrl}/img/logo.png" alt="" width="180" class="mb-3">
        <p class="font-italic text-muted">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt.</p>
        <ul class="list-inline mt-4">
          <li class="list-inline-item">
            <a href="#" target="_blank" title="twitter">
              <i class="lni lni-twitter"></i>
            </a>
          </li>
          <li class="list-inline-item">
            <a href="#" target="_blank" title="facebook">
              <i class="lni lni-facebook"></i>
            </a>
          </li>
          <li class="list-inline-item">
            <a href="#" target="_blank" title="instagram">
              <i class="lni lni-instagram"></i>
            </a>
          </li>
          <li class="list-inline-item">
            <a href="#" target="_blank" title="pinterest">
              <i class="lni lni-pinterest"></i>
            </a>
          </li>
          <li class="list-inline-item">
            <a href="#" target="_blank" title="vimeo">
              <i class="lni lni-vimeo"></i>
            </a>
          </li>
        </ul>
      </div>
      <div class="col-lg-2 col-md-6 mb-4 mb-lg-0">
        <h6 class="text-uppercase font-weight-bold mb-4">Shop</h6>
        <ul class="list-unstyled mb-0">
          <li class="mb-2">
            <a href="#" class="text-muted">For Women</a>
          </li>
          <li class="mb-2">
            <a href="#" class="text-muted">For Men</a>
          </li>
          <li class="mb-2">
            <a href="#" class="text-muted">Stores</a>
          </li>
          <li class="mb-2">
            <a href="#" class="text-muted">Our Blog</a>
          </li>
        </ul>
      </div>
      <div class="col-lg-2 col-md-6 mb-4 mb-lg-0">
        <h6 class="text-uppercase font-weight-bold mb-4">Company</h6>
        <ul class="list-unstyled mb-0">
          <li class="mb-2">
            <a href="#" class="text-muted">Login</a>
          </li>
          <li class="mb-2">
            <a href="#" class="text-muted">Register</a>
          </li>
          <li class="mb-2">
            <a href="#" class="text-muted">Wishlist</a>
          </li>
          <li class="mb-2">
            <a href="#" class="text-muted">Our Products</a>
          </li>
        </ul>
      </div>
      <div class="col-lg-4 col-md-6 mb-lg-0">
        <h6 class="text-uppercase font-weight-bold mb-4">Newsletter</h6>
        <p class="text-muted mb-4">Lorem ipsum dolor sit amet, consectetur adipisicing elit. At itaque temporibus.</p>
        <div class="p-1 rounded border">
          <div class="input-group">
            <input type="email" placeholder="Enter your email address" aria-describedby="button-addon1" class="form-control border-0 shadow-0">
            <div class="input-group-append">
              <button id="button-addon1" type="submit" class="btn btn-link">
                <i class="lni lni-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</footer>`,
});
Vvveb.Sections.add("footer/footer-3", {
	name: "Footer 3",
	image: Vvveb.sectionsBaseUrl + "/screenshots/footer/footer-3-thumb.jpeg",
	html: `<footer class="footer-3 bg-dark text-white" title="footer-3">
  <div class="container" data-v-component-menu="footer" data-v-menu_id="5">

    <div class="row" data-v-cats>

      <div class="col-md-3">
        <img class="logo" src="${Vvveb.sectionsBaseUrl}/img/logo.png">
      </div>


      <div class="col-md-3" data-v-cat data-v-if="category.children > 0">
        <h6 data-v-cat-name>Vvveb</h6>
        <nav data-v-cat-recursive>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://themes.vvveb.com/" data-v-cat-url data-v-cat-name>Themes</a>
          </div>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://plugins.vvveb.com/" data-v-cat-url data-v-cat-name>Plugins</a>
          </div>
        </nav>
      </div>

      <div class="col-md-3" data-v-cat data-v-if="category.children > 0">
        <h6 data-v-cat-name>Resources</h6>
        <nav data-v-cat-recursive>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>User documentation</a>
          </div>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="https://github.com/givanz/VvvebJs/wiki" data-v-cat-url data-v-cat-name>Developer documentation</a>
          </div>
        </nav>
      </div>

      <div class="col-md-3" data-v-cat data-v-if="category.children > 0">
        <h6 data-v-cat-name>Contact</h6>
        <nav data-v-cat-recursive>
          <div data-v-cat data-v-if="category.children == 0">
            <a href="" target="contact.html">Contact</a>
          </div>
        </nav>
      </div>



    </div>
    <!--
		<div class="row justify-content-end">
			<div class="col-md-3 text-muted text-small mt-5">
				&copy; <span data-v-year>2022</span> <span data-v-sitename>Vvveb</span>. Powered by <a href="//vvveb.com" target="_blank">Vvveb</a>	
			</div>
		</div>
		-->

  </div>

  <div class="footer-copyright">
    <div class="container">
      <div class="d-flex">
        <div class="text-muted text-small flex-grow-1">
          <a class="btn-link text-muted" href="/page/terms-conditions" target="_blank">Terms and conditions</a> |
          <a class="btn-link text-muted" href="/page/privacy-policy" target="_blank">Privacy Policy</a>
        </div>
        <div class="text-muted text-small">
          &copy; <span data-v-year>2022</span>
          <span data-v-sitename>Vvveb</span>. Powered by <a href="//vvveb.com" class="btn-link text-muted" target="_blank">Vvveb</a>
        </div>
      </div>
    </div>
  </div>

</footer>`,
});
Vvveb.SectionsGroup["Footer"] = [
	"footer/footer-1",
	"footer/footer-2",
	"footer/footer-3",
];
Vvveb.Sections.add("pricing-table/pricing-table-3", {
	name: "Pricing table 3",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/pricing-table/pricing-table-3-thumb.jpeg",
	html: `<section class="pricing-table-3 bg-alternate" title="pricing-table-3">
  <div class="container">
    <div class="row justify-content-center pb-2">
      <div class="col-md-7 heading-section text-center">
        <h1 class="text-center">Choose your plan</h1>
        <p class="lead text-center mb-4">14-day free trial no credit card required.</p>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4">
        <div class="price-col">
          <div class="img">
            <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/104-dumbbell.svg" class="img-fluid" />
          </div>
          <div class="text-center p-4">
            <span class="excerpt d-block">Personal</span>
            <span class="price">
              <sup>$</sup>
              <span class="number">49</span>
              <sub>/mos</sub>
            </span>
            <ul class="pricing-text mb-5">
              <li>
                <span class="lni lni-check me-2"></span>5 Dog Walk
              </li>
              <li>
                <span class="lni lni-check me-2"></span>3 Vet Visit
              </li>
              <li>
                <span class="lni lni-check me-2"></span>3 Pet Spa
              </li>
              <li>
                <span class="lni lni-check me-2"></span>Free Supports
              </li>
            </ul>
            <a href="#" class="btn btn-primary d-block px-2 py-3">Get Started</a>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="price-col">
          <div class="img">
            <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/107-healthy.svg" class="img-fluid" />
          </div>
          <div class="text-center p-4">
            <span class="excerpt d-block">Business</span>
            <span class="price">
              <sup>$</sup>
              <span class="number">79</span>
              <sub>/mos</sub>
            </span>
            <ul class="pricing-text mb-5">
              <li>
                <span class="lni lni-check me-2"></span>5 Dog Walk
              </li>
              <li>
                <span class="lni lni-check me-2"></span>3 Vet Visit
              </li>
              <li>
                <span class="lni lni-check me-2"></span>3 Pet Spa
              </li>
              <li>
                <span class="lni lni-check me-2"></span>Free Supports
              </li>
            </ul>
            <a href="#" class="btn btn-primary d-block px-2 py-3">Get Started</a>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="price-col">
          <div class="img">
            <img src="${Vvveb.sectionsBaseUrl}/img/illustrations.co/126-namaste-no-hand-shake.svg" class="img-fluid" />
          </div>
          <div class="text-center p-4">
            <span class="excerpt d-block">Ultimate</span>
            <span class="price">
              <sup>$</sup>
              <span class="number">109</span>
              <sub>/mos</sub>
            </span>
            <ul class="pricing-text mb-5">
              <li>
                <span class="lni lni-check me-2"></span>5 Dog Walk
              </li>
              <li>
                <span class="lni lni-check me-2"></span>3 Vet Visit
              </li>
              <li>
                <span class="lni lni-check me-2"></span>3 Pet Spa
              </li>
              <li>
                <span class="lni lni-check me-2"></span>Free Supports
              </li>
            </ul>
            <a href="#" class="btn btn-primary d-block px-2 py-3">Get Started</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Pricing table"] = ["pricing-table/pricing-table-3"];
Vvveb.Sections.add("products/products-1", {
	name: "Products 1",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/products/products-1-thumb.jpeg",
	html: `<section class="products-1" title="latest-products-1">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="section-heading text-center">
          <h2>Popular Products</h2>
        </div>
      </div>
    </div>
  </div>


  <div class="container" data-v-component-products="popular" data-v-category='' data-v-manufacturer='' data-v-page="1" data-v-limit="8" data-v-order="" data-v-parent="">
    <div class="row">



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



      <div class="col-md-3" data-v-product>

        <article class="single-product-wrapper">
          <!-- Product Image -->
          <a href="/product/product-one" data-v-product-url="" title=""> </a>
          <div class="product-img">
            <a href="/product/product-one" data-v-product-url="" title="">

              <img src="${Vvveb.sectionsBaseUrl}/img/demo/video-1.jpg" data-v-product-alt alt="" data-v-product-image="" />

              <!-- Hover Thumb -->
              <img class="hover-img" src="${Vvveb.sectionsBaseUrl}/img/demo/video-2.jpg" data-v-product-alt alt="" data-v-product-image-1="" />
            </a>

            <!-- Favourite -->
            <div class="product-favourite">
              <a href="/product/product-one" data-v-product-url data-v-product-title class="favme lni lni-heart"></a>
            </div>
          </div>

          <!-- Product Description -->
          <div class="product-content">
            <!-- span>topshop</span -->
            <a href="/product/product-one" data-v-product-url>
              <h6 data-v-product-name>Product 8</h6>
            </a>
            <span data-v-product-currency=""></span>
            <p class="product-price" data-v-product-price="">100.0000</p>

            <!-- Hover Content -->
            <div class="hover-content">
              <!-- Add to Cart -->
              <div class="add-to-cart-btn">
                <input type="hidden" name="product_id" value="" data-v-product-product_id />
                <a href="" class="btn btn-primary w-100" data-v-product-url="cart/cart/index" data-v-vvveb-action="addToCart" data-product_id="1">
                  <span class="loading d-none">
                    <span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"> </span>
                    <span>Add to cart</span>...
                  </span>

                  <span class="button-text">
                    Add to cart
                  </span>
                </a>
              </div>
            </div>
          </div>
        </article>


      </div>



    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Products"] = ["products/products-1"];
Vvveb.Sections.add("showcase/showcase-1", {
	name: "Showcase 1",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-1-thumb.jpeg",
	html: `<section class="d-flex showcase-1" title="showcase-1">
  <div class="container">
    <div class="row align-items-center w-100">
      <div class="col-md-4 col-sm-12">
        <h3>Drag and drop builder</h3>
        <p>Unlimited design powers with an easy to use interface, change anything on your website with a few clicks.</p>
      </div>
      <div class="col-md-8 col-sm-12 col-img">
        <img class="img-fluid" src="${Vvveb.sectionsBaseUrl}/img/builder.svg">
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-2", {
	name: "Showcase 2",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-2-thumb.jpeg",
	html: `<section class="showcase-2" title="showcase-2">
  <div class="container">
    <div class="row g-1 justify-content-center">
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-shield-alt"></i>
          </div>
          <h3>
            <a href="#">Security</a>
          </h3>
          <p class="text-center">Vvveb is 100% safe against sql injections, a vulerability that affects most CMSs.</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-praying-hands"></i>
          </div>
          <h3>
            <a href="#">Unlimited theme flexibility</a>
          </h3>
          <p class="text-center">Vvveb uses only html for templating for maximum flexibility.</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-shopping-cart"></i>
          </div>
          <h3>
            <a href="#">Ecommerce</a>
          </h3>
          <p class="text-center">Vvveb is a full featured ecommerce platform with advanced functionality.</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-globe-europe"></i>
          </div>
          <h3>
            <a href="#">Localization</a>
          </h3>
          <p class="text-center">Publish content in multiple languages or sell in different currencies.</p>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-3", {
	name: "Showcase 3",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-3-thumb.jpeg",
	html: `<section class="showcase-3" title="showcase-3">
  <div class="container">
    <div class="row g-1 justify-content-center">
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
        <div class="feature">
          <div class="font-container text-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="96" height="96" color="#1064ea" fill="#fff">
              <polyline points="336 176 225.2 304 176 255.8" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px"></polyline>
              <path d="M463.1,112.37C373.68,96.33,336.71,84.45,256,48,175.29,84.45,138.32,96.33,48.9,112.37,32.7,369.13,240.58,457.79,256,464,271.42,457.79,479.3,369.13,463.1,112.37Z" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px"></path>
            </svg>
          </div>
          <h3>
            <a href="#">Better Security</a>
          </h3>
          <p class="text-center">Vvveb is 100% safe against sql injections, a vulerability that affects most CMSs.</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
        <div class="feature">
          <div class="font-container text-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="icons" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" style="enable-background:new 0 0 512 512;" xml:space="preserve" width="96" height="96" color="#1064ea" fill="#fff">
              <path d="M419.1,337.45a3.94,3.94,0,0,0-6.1,0c-10.5,12.4-45,46.55-45,77.66,0,27,21.5,48.89,48,48.89h0c26.5,0,48-22,48-48.89C464,384,429.7,349.85,419.1,337.45Z" style="fill:none;stroke:currentColor;stroke-miterlimit:10;stroke-width:32px"></path>
              <path d="M387,287.9,155.61,58.36a36,36,0,0,0-51,0l-5.15,5.15a36,36,0,0,0,0,51l52.89,52.89,57-57L56.33,263.2a28,28,0,0,0,.3,40l131.2,126a28.05,28.05,0,0,0,38.9-.1c37.8-36.6,118.3-114.5,126.7-122.9,5.8-5.8,18.2-7.1,28.7-7.1h.3A6.53,6.53,0,0,0,387,287.9Z" style="fill:none;stroke:currentColor;stroke-miterlimit:10;stroke-width:32px"></path>
            </svg>
          </div>
          <h3>
            <a href="#">Unlimited customization</a>
          </h3>
          <p class="text-center">Vvveb uses only html for templating for maximum flexibility.</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
        <div class="feature">
          <div class="font-container text-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" id="icons" width="96" height="96" color="#1064ea" fill="#fff" stroke-width="28">
              <path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" fill-rule="evenodd" d="M80,176a16,16,0,0,0-16,16V408c0,30.24,25.76,56,56,56H392c30.24,0,56-24.51,56-54.75V192a16,16,0,0,0-16-16Z"></path>
              <path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" fill-rule="evenodd" d="M160,176V144a96,96,0,0,1,96-96h0a96,96,0,0,1,96,96v32"></path>
            </svg>
          </div>
          <h3>
            <a href="#">Advanced Ecommerce</a>
          </h3>
          <p class="text-center">Vvveb is a full featured ecommerce platform with advanced functionality.</p>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="400">
        <div class="feature">
          <div class="font-container text-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="96" height="96" color="#1064ea" fill="#fff">
              <path d="M80,464V68.14a8,8,0,0,1,4-6.9C91.81,56.66,112.92,48,160,48c64,0,145,48,192,48a199.53,199.53,0,0,0,77.23-15.77A2,2,0,0,1,432,82.08V301.44a4,4,0,0,1-2.39,3.65C421.37,308.7,392.33,320,352,320c-48,0-128-32-192-32s-80,16-80,16" style="fill:none;stroke:currentColor;stroke-linecap:round;stroke-miterlimit:10;stroke-width:32px"></path>
            </svg>
          </div>
          <h3>
            <a href="#">Full Localization</a>
          </h3>
          <p class="text-center">Publish content in multiple languages or sell in different currencies.</p>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-4", {
	name: "Showcase 4",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-4-thumb.jpeg",
	html: `<section class="showcase-4" title="showcase-4">
  <div class="container">
    <div class="row g-1 justify-content-center">
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-shield-alt"></i>
          </div>
          <h3>
            <a href="#">Security</a>
          </h3>
          <p class="">Vvveb is 100% safe against sql injections, a vulerability that affects most CMSs.</p>
          <a class="more  d-block" href="#">
            Read more
            <i class="font-icon lni lni-chevron-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-praying-hands"></i>
          </div>
          <h3>
            <a href="#">Unlimited theme flexibility</a>
          </h3>
          <p class="">Vvveb uses only html for templating for maximum flexibility.</p>
          <a class="more d-block" href="#">
            Read more
            <i class="font-icon lni lni-chevron-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-shopping-cart"></i>
          </div>
          <h3>
            <a href="#">Ecommerce</a>
          </h3>
          <p class="">Vvveb is a full featured ecommerce platform with advanced functionality.</p>
          <a class="more d-block" href="#">
            Read more
            <i class="font-icon lni lni-chevron-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-globe-europe"></i>
          </div>
          <h3>
            <a href="#">Localization</a>
          </h3>
          <p class="">Publish content in multiple languages or sell in different currencies.</p>
          <a class="more d-block" href="#">
            Read more
            <i class="font-icon lni lni-chevron-circle-right"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-5", {
	name: "Showcase 5",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-5-thumb.jpeg",
	html: `<section class="showcase-5" title="showcase-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-shield-alt"></i>
          </div>
          <h3>
            <a href="#">Security</a>
          </h3>
          <p class="">Vvveb is 100% safe against sql injections, a vulerability that affects most CMSs.</p>
          <!-- a class="more d-block" href="#">
			  Read more
			  <i class="font-icon lni lni-chevron-circle-right"></i>
          </a-->
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-praying-hands"></i>
          </div>
          <h3>
            <a href="#">Unlimited flexibility</a>
          </h3>
          <p class="">Vvveb uses only html for templating for maximum flexibility.</p>
          <!-- a class="more d-block" href="#">
			  Read more
			  <i class="font-icon lni lni-chevron-circle-right"></i>
          </a-->
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-shopping-cart"></i>
          </div>
          <h3>
            <a href="#">Ecommerce</a>
          </h3>
          <p class="">Vvveb is a full featured ecommerce platform with advanced functionality.</p>
          <!-- a class="more d-block" href="#">
			  Read more
			  <i class="font-icon lni lni-chevron-circle-right"></i>
          </a-->
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
        <div class="feature">
          <div class="font-container text-center">
            <i class="font-icon lni lni-globe-europe"></i>
          </div>
          <h3>
            <a href="#">Localization</a>
          </h3>
          <p class="">Publish content in multiple languages or sell in different currencies.</p>
          <!-- a class="more d-block" href="#">
			  Read more
			  <i class="font-icon lni lni-chevron-circle-right"></i>
          </a-->
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-6", {
	name: "Showcase 6",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-6-thumb.jpeg",
	html: `<section class="showcase-6" title="showcase-6">
  <div class="separator top">
    <svg class="pricing-divider-img" enable-background="new 0 0 300 100" height="100px" id="Layer_1" fill="currentColor" preserveAspectRatio="none" version="1.1" viewBox="0 0 300 100" width="300px" x="0px" xml:space="preserve" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" y="0px">
      <path class="deco-layer deco-layer--1" d="M30.913,43.944c0,0,42.911-34.464,87.51-14.191c77.31,35.14,113.304-1.952,146.638-4.729
		c48.654-4.056,69.94,16.218,69.94,16.218v54.396H30.913V43.944z" fill="currentColor" opacity="0.6"></path>
      <path class="deco-layer deco-layer--2" d="M-35.667,44.628c0,0,42.91-34.463,87.51-14.191c77.31,35.141,113.304-1.952,146.639-4.729
		c48.653-4.055,69.939,16.218,69.939,16.218v54.396H-35.667V44.628z" fill="currentColor" opacity="0.6"></path>
      <path class="deco-layer deco-layer--3" d="M43.415,98.342c0,0,48.283-68.927,109.133-68.927c65.886,0,97.983,67.914,97.983,67.914v3.716
		H42.401L43.415,98.342z" fill="currentColor" opacity="0.7"></path>
      <path class="deco-layer deco-layer--4" d="M-34.667,62.998c0,0,56-45.667,120.316-27.839C167.484,57.842,197,41.332,232.286,30.428
		c53.07-16.399,104.047,36.903,104.047,36.903l1.333,36.667l-372-2.954L-34.667,62.998z" fill="currentColor"></path>
    </svg>
  </div>


  <div class="background-container"></div>

  <div class="container">
    <div class="row justify-content-center">
      <div class="col-12 col-sm-6 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-shield-alt"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">Security</a>
            </h3>
            <p class="">Vvveb is 100% safe against sql injections, a vulerability that affects most CMSs.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="300">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-praying-hands"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">Unlimited flexibility</a>
            </h3>
            <p class="">Vvveb uses only html for templating for maximum flexibility.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="600">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-shopping-cart"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">Ecommerce</a>
            </h3>
            <p class="">Vvveb is a full featured ecommerce platform with advanced functionality.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="separator bottom">
    <svg class="pricing-divider-img" enable-background="new 0 0 300 100" height="100px" fill="currentColor" id="Layer_1" preserveAspectRatio="none" version="1.1" viewBox="0 0 300 100" width="300px" x="0px" xml:space="preserve" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" y="0px">
      <path class="deco-layer deco-layer--1" d="M30.913,43.944c0,0,42.911-34.464,87.51-14.191c77.31,35.14,113.304-1.952,146.638-4.729
		c48.654-4.056,69.94,16.218,69.94,16.218v54.396H30.913V43.944z" fill="currentColor" opacity="0.6"></path>
      <path class="deco-layer deco-layer--2" d="M-35.667,44.628c0,0,42.91-34.463,87.51-14.191c77.31,35.141,113.304-1.952,146.639-4.729
		c48.653-4.055,69.939,16.218,69.939,16.218v54.396H-35.667V44.628z" fill="currentColor" opacity="0.6"></path>
      <path class="deco-layer deco-layer--3" d="M43.415,98.342c0,0,48.283-68.927,109.133-68.927c65.886,0,97.983,67.914,97.983,67.914v3.716
		H42.401L43.415,98.342z" fill="currentColor" opacity="0.7"></path>
      <path class="deco-layer deco-layer--4" d="M-34.667,62.998c0,0,56-45.667,120.316-27.839C167.484,57.842,197,41.332,232.286,30.428
		c53.07-16.399,104.047,36.903,104.047,36.903l1.333,36.667l-372-2.954L-34.667,62.998z" fill="currentColor"></path>
    </svg>
  </div>

</section>`,
});
Vvveb.Sections.add("showcase/showcase-7", {
	name: "Showcase 7",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-7-thumb.jpeg",
	html: `<section class="showcase-7" title="showcase-7">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-12 col-sm-6 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-shield-alt"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">Security</a>
            </h3>
            <p class="">Vvveb is 100% safe against sql injections, a vulerability that affects most CMSs.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="300">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-praying-hands"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">Unlimited theme flexibility</a>
            </h3>
            <p class="">Vvveb uses only html for templating for maximum flexibility.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="600">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-shopping-cart"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">Ecommerce</a>
            </h3>
            <p class="">Vvveb is a full featured ecommerce platform with advanced functionality and internationalization.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-8", {
	name: "Showcase 8",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-8-thumb.jpeg",
	html: `<section class="showcase-8" title="showcase-8">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-7 mx-auto" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-robot"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">The next generation website builder</a>
            </h3>
            <p class="">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-9", {
	name: "Showcase 9",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-9-thumb.jpeg",
	html: `<section class="showcase-8" title="showcase-9">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-7 mx-auto" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-robot"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">The next generation website builder</a>
            </h3>
            <p class="">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-10", {
	name: "Showcase 10",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-10-thumb.jpeg",
	html: `<section class="showcase-10" title="showcase-10">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-7 mx-auto" data-aos="fade-up" data-aos-delay="0">
        <div class="feature">
          <div class="font-container">
            <i class="font-icon lni lni-robot"></i>
          </div>
          <div class="text">
            <h3>
              <a href="#">The next generation website builder</a>
            </h3>
            <p class="">Powerful and easy to use drag and drop website builder for blogs, presentation or ecommerce stores.</p>
            <a class="more d-block" href="#">
              Read more
              <i class="font-icon lni lni-chevron-circle-right"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
</section>`,
});
Vvveb.Sections.add("showcase/showcase-11", {
	name: "Showcase 11",
	image:
		Vvveb.sectionsBaseUrl + "/screenshots/showcase/showcase-11-thumb.jpeg",
	html: `<section class="pt-5 pb-5">
  <div class="container">
    <div class="row align-items-center justify-content-center">
      <div class="col-12 col-md-6   mt-4 mt-md-0 order-md-1 order-2">
        <img alt="image" class="img-fluid" src="http://via.placeholder.com/650x450/5fa9f8/fff">
      </div>
      <div class="col-12 col-md-4 order-1 order-md-2">
        <h2>Nice Heading</h2>
        <p class="text-h3 mt-3">A collection of coded HTML and CSS elements to help your build your new website. Clean design, fully responsive and based on Bootstrap 4.</p>
      </div>
    </div>
    <div class="row align-items-center justify-content-center pt-5 pb-5">
      <div class="col-12 col-md-4 offset-md-1  ">
        <h2>Nice Heading</h2>
        <p class="text-h3 mt-3">A collection of coded HTML and CSS elements to help your build your new website. Clean design, fully responsive and based on Bootstrap 4.</p>

      </div>
      <div class="col-12 col-md-6   mt-4 mt-md-0">
        <img alt="image" class="img-fluid" src="http://via.placeholder.com/650x450/5fa9f8/fff">
      </div>
    </div>
    <div class="row align-items-center justify-content-center">
      <div class="col-12 col-md-6   mt-4 mt-md-0 order-md-1 order-2">
        <img alt="image" class="img-fluid" src="http://via.placeholder.com/650x450/5fa9f8/fff">
      </div>
      <div class="col-12 col-md-4 order-1 order-md-2">
        <h2>Nice Heading</h2>
        <p class="text-h3 mt-3">A collection of coded HTML and CSS elements to help your build your new website. Clean design, fully responsive and based on Bootstrap 4.</p>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Showcase"] = [
	"showcase/showcase-1",
	"showcase/showcase-2",
	"showcase/showcase-3",
	"showcase/showcase-4",
	"showcase/showcase-5",
	"showcase/showcase-6",
	"showcase/showcase-7",
	"showcase/showcase-8",
	"showcase/showcase-9",
	"showcase/showcase-10",
	"showcase/showcase-11",
];
Vvveb.Sections.add("team/team-1", {
	name: "Team 1",
	image: Vvveb.sectionsBaseUrl + "/screenshots/team/team-1-thumb.jpeg",
	html: `<section class="py-5" title="team-1">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h3 class="mb-3">Meet Our Team</h3>
        <h5 class="text-muted">We are a group of professionals dedicated to their work</h5>
      </div>
    </div>
    <div class="row">

      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CTO - Founder</h6>
              <p class="text-muted">We are a group of professionals dedicated to their work</p>

              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>

            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CTO - Tech</h6>
              <p class="text-muted">We are a group of professionals dedicated to their work</p>

              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>

            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="text-muted">We are a group of professionals dedicated to their work</p>

              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>

            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/4.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CMO - Operations</h6>
              <p class="text-muted">You can relay on our amazing features list and also our customer services will be great experience.</p>

              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>

            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("team/team-2", {
	name: "Team 2",
	image: Vvveb.sectionsBaseUrl + "/screenshots/team/team-2-thumb.jpeg",
	html: `<section class="py-5 team-2" title="team-2">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h3 class="mb-3">Meet Our Team</h3>
        <h5 class="lead text-muted">We are a group of professionals dedicated to their work</h5>
      </div>
    </div>
    <div class="row justify-content-center py-5">
      <div class="card col-md-3">
        <div class="card-content">
          <div class="card-body p-0">
            <div class="profile">
              <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg">
            </div>
            <div class="card-title mt-4">
              <h4>Jane Doe</h4>
              <small>CEO - Founder</small>
            </div>
            <div class="card-subtitle">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing </p>
            </div>
            <div class="card-footer">
              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="card col-md-3">
        <div class="card-content">
          <div class="card-body p-0">
            <div class="profile">
              <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg">
            </div>
            <div class="card-title mt-4">
              <h4>John Doe</h4>
              <small>CTO - Co-Founder</small>
            </div>
            <div class="card-subtitle">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing </p>
            </div>
            <div class="card-footer">
              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="card col-md-3">
        <div class="card-content">
          <div class="card-body p-0">
            <div class="profile">
              <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg">
            </div>
            <div class="card-title mt-4">
              <h4>Jane Doe</h4>
              <small>CSO - Financial</small>
            </div>
            <div class="card-subtitle">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing </p>
            </div>
            <div class="card-footer">
              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook-f"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
</section>`,
});
Vvveb.Sections.add("team/team-5", {
	name: "Team 5",
	image: Vvveb.sectionsBaseUrl + "/screenshots/team/team-5-thumb.jpeg",
	html: `<section class="py-5" title="team-5">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h3 class="mb-3">Meet Our Team</h3>
        <h5 class="text-muted">We are a group of professionals dedicated to their work</h5>
      </div>
    </div>
    <div class="row">

      <div class="col-lg-3 mb-4">

        <div class="row no-gutters">
          <div class="col-md-12 pro-pic">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" class="img-fluid p-4">

            <ul class="mb-0 list-inline mt-3 px-4">
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-facebook"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-twitter"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-instagram"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-linkedin"></i>
                </a>
              </li>
            </ul>
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row no-gutters">
          <div class="col-md-12 pro-pic">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" class="img-fluid p-4">

            <ul class="mb-0 list-inline mt-3 px-4">
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-facebook"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-twitter"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-instagram"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-linkedin"></i>
                </a>
              </li>
            </ul>
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row no-gutters">
          <div class="col-md-12 pro-pic">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" class="img-fluid p-4">

            <ul class="mb-0 list-inline mt-3 px-4">
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-facebook"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-twitter"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-instagram"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-linkedin"></i>
                </a>
              </li>
            </ul>
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row no-gutters">
          <div class="col-md-12 pro-pic">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/4.jpg" class="img-fluid p-4">

            <ul class="mb-0 list-inline mt-3 px-4">
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-facebook"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-twitter"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-instagram"></i>
                </a>
              </li>
              <li class="list-inline-item">
                <a href="#" class="social-link">
                  <i class="lab la-linkedin"></i>
                </a>
              </li>
            </ul>
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
            </div>
          </div>
        </div>

      </div>

    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("team/team-6", {
	name: "Team 6",
	image: Vvveb.sectionsBaseUrl + "/screenshots/team/team-6-thumb.jpeg",
	html: `<section class="py-5" title="team-6">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h3 class="mb-3">Meet Our Team</h3>
        <h5 class="text-muted">We are a group of professionals dedicated to their work</h5>
      </div>
    </div>
    <div class="row">

      <div class="col-lg-4 mb-4">

        <div class="row">
          <div class="col-md-12">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" class="img-fluid p-4">
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-4 mb-4">

        <div class="row">
          <div class="col-md-12 pro-pic">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" class="img-fluid p-4">
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-4 mb-4">

        <div class="row">
          <div class="col-md-12 pro-pic">
            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" class="img-fluid p-4">
          </div>
          <div class="col-md-12">
            <div class="px-4">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
              <p class="mt-3">We are a group of professionals dedicated to their work</p>
              <ul class="mb-0 list-inline mt-3">
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-facebook"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-twitter"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-instagram"></i>
                  </a>
                </li>
                <li class="list-inline-item">
                  <a href="#" class="social-link">
                    <i class="lab la-linkedin"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

      </div>

    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("team/team-7", {
	name: "Team 7",
	image: Vvveb.sectionsBaseUrl + "/screenshots/team/team-7-thumb.jpeg",
	html: `<section class="py-5" title="team-7">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h3 class="mb-3">Meet Our Team</h3>
        <h5 class="text-muted">We are a group of professionals dedicated to their work</h5>
      </div>
    </div>
    <div class="row">

      <div class="col-lg-6">
        <div class="card shadow-sm border-1 mb-4 p-0">

          <div class="row no-gutters">
            <div class="col-md-5">

              <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" class="img-fluid">

            </div>
            <div class="col-md-7">
              <div class="p-4">
                <h4 class="mb-3">John Doe</h4>
                <p>We are a group of professionals dedicated to their work</p>
                <ul class="mb-0 list-inline mt-3">
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-facebook-f"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-twitter"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-instagram"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-linkedin"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div class="col-lg-6">
        <div class="card shadow-sm border-1 mb-4 p-0">

          <div class="row no-gutters">
            <div class="col-md-5">

              <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" class="img-fluid">

            </div>
            <div class="col-md-7">
              <div class="p-4">
                <h4 class="mb-3">John Doe</h4>
                <p>We are a group of professionals dedicated to their work</p>
                <ul class="mb-0 list-inline mt-3">
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-facebook-f"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-twitter"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-instagram"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-linkedin"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </div>

    </div>
    <div class="row">

      <div class="col-lg-6">
        <div class="card shadow-sm border-1 mb-4 p-0">

          <div class="row no-gutters">
            <div class="col-md-5">

              <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" class="img-fluid">
            </div>
            <div class="col-md-7">
              <div class="p-4">
                <h4 class="mb-3">John Doe</h4>
                <p>We are a group of professionals dedicated to their work</p>
                <ul class="mb-0 list-inline mt-3">
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-facebook-f"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-twitter"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-instagram"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-linkedin"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div class="col-lg-6">

        <div class="card shadow-sm border-1 mb-4 p-0">
          <div class="row no-gutters">
            <div class="col-md-5">

              <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/4.jpg" class="img-fluid">

            </div>
            <div class="col-md-7">
              <div class="p-4">
                <h4 class="mb-3">John Doe</h4>
                <p>We are a group of professionals dedicated to their work</p>
                <ul class="mb-0 list-inline mt-3">
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-facebook-f"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-twitter"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-instagram"></i>
                    </a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="social-link">
                      <i class="lab la-linkedin"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </div>

    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("team/team-11", {
	name: "Team 11",
	image: Vvveb.sectionsBaseUrl + "/screenshots/team/team-11-thumb.jpeg",
	html: `<section class="p-4" title="team-11">
  <div class="container">
    <div class="row text-center">


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow-sm py-5 px-4">

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow-sm">

          <h5 class="mb-0">John Doe</h5>
          <span class="small text-uppercase text-muted">CEO - Founder</span>
          <ul class="social mb-0 list-inline mt-3">
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-facebook-f-f"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-twitter"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-instagram"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-linkedin"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow-sm py-5 px-4">

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow-sm">

          <h5 class="mb-0">Jane Doe</h5>
          <span class="small text-uppercase text-muted">CTO - Co-Founder</span>
          <ul class="social mb-0 list-inline mt-3">
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-facebook-f"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-twitter"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-instagram"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-linkedin"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow-sm py-5 px-4">

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow-sm">

          <h5 class="mb-0">John Doe</h5>
          <span class="small text-uppercase text-muted">CMO - Operations</span>
          <ul class="social mb-0 list-inline mt-3">
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-facebook-f"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-twitter"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-instagram"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-linkedin"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow-sm py-5 px-4">

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/4.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow-sm">

          <h5 class="mb-0">Jane Doe</h5>
          <span class="small text-uppercase text-muted">CCO - Financial</span>
          <ul class="social mb-0 list-inline mt-3">
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-facebook-f"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-twitter"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-instagram"></i>
              </a>
            </li>
            <li class="list-inline-item">
              <a href="#" class="social-link">
                <i class="lab la-linkedin"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.SectionsGroup["Team"] = [
	"team/team-1",
	"team/team-2",
	"team/team-5",
	"team/team-6",
	"team/team-7",
	"team/team-11",
];
Vvveb.Sections.add("testimonials/testimonials-1", {
	name: "Testimonials 1",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/testimonials/testimonials-1-thumb.jpeg",
	html: `<section class="testimonials-1 bg-alternate" title="testimonials-1">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h2 class="mb-1">Some of our customers</h2>
        <h5 class="lead text-muted mb-3">Hear what our customers have to say</h5>
      </div>
    </div>

    <div class="row text-center">


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow py-5 px-4">

          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
            <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
            <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
          </svg>

          <p class="mt-3">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam eu sem tempor, varius quam at, luctus dui.</p>

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow">

          <h5 class="mb-0">John Doe</h5>
          <span class="small text-uppercase text-muted">Company Inc.</span>
        </div>
      </div>


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow py-5 px-4">

          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
            <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
            <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
          </svg>

          <p class="mt-3">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam eu sem tempor, varius quam at, luctus dui.</p>
          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow">

          <h5 class="mb-0">Jane Doe</h5>
          <span class="small text-uppercase text-muted">Company Inc.</span>
        </div>
      </div>


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow py-5 px-4">

          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
            <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
            <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
          </svg>

          <p class="mt-3">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam eu sem tempor, varius quam at, luctus dui.</p>

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow">

          <h5 class="mb-0">John Doe</h5>
          <span class="small text-uppercase text-muted">Company Inc.</span>
        </div>
      </div>


      <div class="col-xl-3 col-sm-6 mb-5">
        <div class="bg-white rounded shadow py-5 px-4">

          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
            <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
            <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
          </svg>

          <p class="mt-3">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam eu sem tempor, varius quam at, luctus dui.</p>

          <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/4.jpg" alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow">

          <h5 class="mb-0">Jane Doe</h5>
          <span class="small text-uppercase text-muted">Company Inc.</span>
        </div>
      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("testimonials/testimonials-2", {
	name: "Testimonials 2",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/testimonials/testimonials-2-thumb.jpeg",
	html: `<section class="py-5" title="testimonials-2">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h2 class="mb-1">Some of our customers</h2>
        <h5 class="lead text-muted mb-3">Hear what our customers have to say</h5>
      </div>
    </div>
    <div class="row text-center">

      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">

              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CTO - Founder</h6>

            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CTO - Tech</h6>

            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CSO - Financial</h6>
            </div>
          </div>
        </div>

      </div>


      <div class="col-lg-3 mb-4">

        <div class="row">
          <div class="col-md-12">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <img alt="image" src="${Vvveb.sectionsBaseUrl}/img/sections/team/4.jpg" class="img-fluid p-4 rounded-circle">
          </div>
          <div class="col-md-12 text-center">
            <div class="pt-2">
              <h5 class="mt-4 mb-0">John Doe</h5>
              <h6 class="text-muted mb-3">CMO - Operations</h6>


            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</section>`,
});
Vvveb.Sections.add("testimonials/testimonials-3", {
	name: "Testimonials 3",
	image:
		Vvveb.sectionsBaseUrl +
		"/screenshots/testimonials/testimonials-3-thumb.jpeg",
	html: `<section class="py-5 team-2" title="testimonials-3">
  <div class="container">
    <div class="row justify-content-center mb-4">
      <div class="col-md-7 text-center">
        <h2 class="mb-1">Some of our customers</h2>
        <h5 class="lead text-muted mb-3">Hear what our customers have to say</h5>
      </div>
    </div>
    <div class="row justify-content-center py-5">
      <div class="card col-md-3">
        <div class="card-content">
          <div class="card-body">


            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <div class="profile">
              <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/1.jpg">
            </div>
            <div class="card-title mt-4">
              <h4>Jane Doe</h4>
              <small>CEO - Founder</small>
            </div>
          </div>
        </div>
      </div>
      <div class="card col-md-3">
        <div class="card-content">
          <div class="card-body">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <div class="profile">
              <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/2.jpg">
            </div>
            <div class="card-title mt-4">
              <h4>John Doe</h4>
              <small>CTO - Co-Founder</small>
            </div>
          </div>
        </div>
      </div>
      <div class="card col-md-3">
        <div class="card-content">
          <div class="card-body">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="42" height="42" fill="#0011ff">
              <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z"></path>
              <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z"></path>
            </svg>

            <div class="card-subtitle mt-3">
              <p class="text-muted"> I really enjoyed working with them, they are Group of Professionals and they know what they're Doing</p>
            </div>

            <div class="profile">
              <img src="${Vvveb.sectionsBaseUrl}/img/sections/team/3.jpg">
            </div>
            <div class="card-title mt-4">
              <h4>Jane Doe</h4>
              <small>CSO - Financial</small>
            </div>
          </div>
        </div>
      </div>
    </div>
</section>`,
});
Vvveb.SectionsGroup["Testimonials"] = [
	"testimonials/testimonials-1",
	"testimonials/testimonials-2",
	"testimonials/testimonials-3",
];
