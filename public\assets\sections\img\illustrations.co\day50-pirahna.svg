<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Generator: Adobe Illustrator 23.1.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   id="Layer_1"
   x="0px"
   y="0px"
   viewBox="0 0 1000 1000"
   style="enable-background:new 0 0 1000 1000;"
   xml:space="preserve"
   sodipodi:docname="day50-pirahna.svg"
   inkscape:version="0.92.5 (2060ec1f9f, 2020-04-08)"><metadata
   id="metadata286"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /><dc:title /></cc:Work></rdf:RDF></metadata><defs
   id="defs284" /><sodipodi:namedview
   pagecolor="#ffffff"
   bordercolor="#666666"
   borderopacity="1"
   objecttolerance="10"
   gridtolerance="10"
   guidetolerance="10"
   inkscape:pageopacity="0"
   inkscape:pageshadow="2"
   inkscape:window-width="2560"
   inkscape:window-height="1379"
   id="namedview282"
   showgrid="false"
   inkscape:zoom="0.472"
   inkscape:cx="1354.4878"
   inkscape:cy="247.3204"
   inkscape:window-x="0"
   inkscape:window-y="0"
   inkscape:window-maximized="1"
   inkscape:current-layer="Layer_1" />
<style
   type="text/css"
   id="style2">
	.st0{fill:#FCFCFC;}
	.st1{fill:#372E42;}
	.st2{fill:#E4EFEF;}
	.st3{fill:none;stroke:#272130;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st4{opacity:0.83;fill:#2D2638;}
	.st5{fill:none;stroke:#272130;stroke-width:4;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;}
	.st7{fill:#1E1826;}
	.st8{fill:none;stroke:#204751;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st9{fill:#4DC1C4;}
	.st10{fill:#23AFAB;}
	.st11{fill:#FFFFFF;stroke:#FFFFFF;stroke-width:4;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st12{opacity:0.78;fill:#6D87AC;}
	.st13{opacity:0.39;fill:#6D87AC;}
</style>


<path
   id="XMLID_46_"
   class="st2"
   d="M675.2,218.9c-55.7-83.8-265.5-64.2-283.5-9.4c-7.9,24.2,26.9,39.4,17.7,68  c-10.8,33.6-63.6,27.6-154.9,96.2c-15.5,11.7-110,82.6-109,177.9c0.9,88.9,74.5,175.9,154.3,192.8c81.7,17.3,113.5-50.2,196.4-33.9  C599.3,730.9,611.5,844,673.9,844c120.9,0.1,246.7-427,160.9-482.5c-33.9-21.9-87.9,22.6-125.8-9.4  C672.4,321.3,706.7,266.2,675.2,218.9z" />
<path
   id="XMLID_47_"
   class="st2"
   d="M431.4,763.7c-13.6,14.9-5,39.9,12.7,53.9c14.6,11.6,39.2,18.7,56.6,10.2  c21.7-10.5,23.9-41.1,4.3-59.8C485,748.8,446.8,746.7,431.4,763.7z" />
<path
   id="XMLID_48_"
   class="st2"
   d="M293,229.7c-17.1,4.7-21.6,52.6-10,62c31.5,25.4,53.6,0.7,40-34  C316.8,241.8,303.7,226.7,293,229.7z" />
<path
   id="XMLID_50_"
   class="st2"
   d="M734.2,265.8c-17.1,1.9-21.6,21-10,24.8c31.5,10.1,53.6,0.3,40-13.6  C758,270.7,744.9,264.7,734.2,265.8z" />
<path
   id="XMLID_15_"
   class="st3"
   d="M367.9,670.1c0,0-8.1,9.2,34.7,51.9c0-16.5-5-35.2,17.8-42.4C393,675.9,367.9,670.1,367.9,670.1  z" />
<path
   id="XMLID_107_"
   class="st1"
   d="M367.9,670.1c0,0-8.1,9.2,34.7,51.9c0-16.5-5-35.2,17.8-42.4  C393,675.9,367.9,670.1,367.9,670.1z" />
<path
   id="XMLID_106_"
   class="st1"
   d="M567.4,657.6c0,0,32.6,11.4,47.6,11.4s27-5.7,27-5.7l-15-5.7c22,0,38.5-16.6,38.5-16.6l-17.5-4  c21.5-3.7,31-26,31-26c-4,0-32.6-4.6-32.6-11.3C620.6,625.5,580.5,652.3,567.4,657.6z" />
<path
   id="XMLID_183_"
   class="st4"
   d="M646.4,599.7c-25.8,25.8-65.9,52.6-79,57.9c0,0,22.9,8,39.1,10.6c2.9-3.6,5.6-6.8,7.3-8.5  c5.2-5.2,26.9-8.3,26.9-14.5s-5.3-9.3,7.4-16.4s11.4-12.9,11.4-12.9l1.4-8C653.5,605.8,646.4,603,646.4,599.7z" />
<path
   id="XMLID_41_"
   class="st5"
   d="M591.9,645.2c0,0,9.7,10.9,16.7,10.9" />
<path
   id="XMLID_42_"
   class="st5"
   d="M632.9,645.2c-12.7,0-19.1-14.4-19.1-14.4" />
<path
   id="XMLID_43_"
   class="st5"
   d="M632.9,620c0,0,3.7,0,15.7,0" />
<path
   id="XMLID_19_"
   class="st3"
   d="M567.4,657.6c0,0,32.6,11.4,47.6,11.4s27-5.7,27-5.7l-15-5.7c22,0,38.5-16.6,38.5-16.6l-17.5-4  c21.5-3.7,31-26,31-26c-4,0-32.6-4.6-32.6-11.3C620.6,625.5,580.5,652.3,567.4,657.6z" />
<ellipse
   id="XMLID_68_"
   class="st6"
   cx="301.1"
   cy="403.5"
   rx="33"
   ry="36" />
<ellipse
   id="XMLID_129_"
   class="st7"
   cx="290.4"
   cy="406.8"
   rx="11.5"
   ry="11.8" />
<ellipse
   id="XMLID_122_"
   class="st6"
   cx="287.6"
   cy="402.8"
   rx="5.1"
   ry="5.2" />
<ellipse
   id="XMLID_71_"
   class="st6"
   cx="293.3"
   cy="413"
   rx="1.9"
   ry="1.9" />
<ellipse
   id="XMLID_28_"
   class="st8"
   cx="301.1"
   cy="403.5"
   rx="33"
   ry="36" />
<path
   id="XMLID_70_"
   class="st1"
   d="M471.6,357.7c0,0,30-55.6,106.5-55.6c-10.5,10.5-10.5,21-10.5,21s28.1-10.8,52.5-3  c-16.5,16.5-16.5,25.5-16.5,25.5c16.5-16.5,51.8,4.7,57.9,10.9c-32.8,12-23.3,13.9-23.3,13.9s19.8-0.1,38.7,18.8  c-19.2,5.9-41.2,14.4-37.8,38.9C618.7,409.2,617,392.3,471.6,357.7z" />
<path
   id="XMLID_174_"
   class="st4"
   d="M648.2,402.6l-7.4-12.7c0,0-18-7-26.9-15.9s-13.3-10.3-21.9-18.9c-8.6-8.6-17.9-10.4-29.4-10.4  s-22-11.1-27.6-16.7c-2.8-2.8-18.5-2.8-33.5-2.1c-20.8,15.1-29.8,31.8-29.8,31.8C617,392.3,618.7,409.2,639,428  C637.4,416.5,641.5,408.5,648.2,402.6z" />
<path
   id="XMLID_37_"
   class="st5"
   d="M512.6,361.4c0,0,18.6-24.3,37.1-33.4" />
<path
   id="XMLID_38_"
   class="st5"
   d="M542.5,367.5c0,0,15.9-18.6,49.5-30.5" />
<path
   id="XMLID_39_"
   class="st5"
   d="M574.2,381.7c0,0,19.6-22.7,53.4-24" />
<path
   id="XMLID_40_"
   class="st5"
   d="M600.9,398.1c0,0,28.2-16.4,47.7-16.4" />
<path
   id="XMLID_21_"
   class="st3"
   d="M471.6,357.7c0,0,30-55.6,106.5-55.6c-10.5,10.5-10.5,21-10.5,21s28.1-10.8,52.5-3  c-16.5,16.5-16.5,25.5-16.5,25.5c16.5-16.5,51.8,4.7,57.9,10.9c-32.8,12-23.3,13.9-23.3,13.9s19.8-0.1,38.7,18.8  c-19.2,5.9-41.2,14.4-37.8,38.9C618.7,409.2,617,392.3,471.6,357.7z" />
<path
   id="XMLID_93_"
   class="st1"
   d="M659.7,479.7c0,0,15.3,13.4,39.3,5s84.7-72.4,129.3-61c0,0-23.3,20.7-18,49.3  c5.3,28.7-14,51.3-14,51.3s16,10.7,0,26.7S754,562.7,757,584s18.7,22.3,22,41s-4,60.7,16,70c0,0-21.3,39.3-82-74.7  c-28-53.1-35.3-61.3-53.3-61.3C653.7,543,659.7,479.7,659.7,479.7z" />
<path
   id="XMLID_172_"
   class="st4"
   d="M659.7,479.7c0,0-6,63.3,0,79.3c18,0,25.3,8.2,53.3,61.3c0.1,0.2,0.2,0.4,0.3,0.6  c6.8-17.2,7.3-52,7.3-63.6c0-15.7,18.2-23.8,22-46c2.2-12.8-5.4-32.3-12.3-46.6c-12.9,9.4-23.9,17.4-31.4,20  C675,493.1,659.7,479.7,659.7,479.7z" />
<path
   id="XMLID_110_"
   class="st5"
   d="M676.8,513.4c0,0,24.3,5.6,57.3-10.3s39-33.4,54-33.4" />
<path
   id="XMLID_1_"
   class="st5"
   d="M675.8,533.7c0,0,26.5,0,56.7,0c30.2,0,49.1-9.2,49.1-9.2" />
<path
   id="XMLID_35_"
   class="st5"
   d="M675.8,546.6c0,0,38.9-0.6,66.9,21.4" />
<path
   id="XMLID_36_"
   class="st5"
   d="M671.3,554c0,0,55.4,15.1,86.9,102.1" />
<path
   id="XMLID_20_"
   class="st3"
   d="M659.7,479.7c0,0,15.3,13.4,39.3,5s84.7-72.4,129.3-61c0,0-23.3,20.7-18,49.3  c5.3,28.7-14,51.3-14,51.3s16,10.7,0,26.7S754,562.7,757,584s18.7,22.3,22,41s-4,60.7,16,70c0,0-21.3,39.3-82-74.7  c-28-53.1-35.3-61.3-53.3-61.3C653.7,543,659.7,479.7,659.7,479.7z" />
<path
   id="XMLID_62_"
   class="st9"
   d="M449.8,355.1c-9.3,0-18.5,0.4-27.5,1.2l1.2,0.2c-1.2,0-2.4,0-3.6,0c-9.2,0.9-18.2,2.1-26.9,3.8  v315.7c18.2,3.4,37.2,5.2,56.8,5.2c125.4,0,227-73,227-163S575.2,355.1,449.8,355.1z" />
<path
   id="XMLID_181_"
   class="st10"
   d="M524.8,364.2c72.2,27.3,121.5,81.7,121.5,144.3c0,90-109,167.2-234.4,167.2  c-8.9,0-3.6,2.1-12.2,1.4l-6.7-1.2c18.2,3.4,37.2,5.2,56.8,5.2c125.4,0,227-73,227-163C676.8,446.9,613.3,386.4,524.8,364.2z" />
<path
   id="XMLID_32_"
   class="st8"
   d="M449.8,355.1c-9.3,0-18.5,0.4-27.5,1.2l1.2,0.2c-1.2,0-2.4,0-3.6,0c-9.2,0.9-18.2,2.1-26.9,3.8  v315.7c18.2,3.4,37.2,5.2,56.8,5.2c125.4,0,227-73,227-163S575.2,355.1,449.8,355.1z" />
<path
   id="XMLID_65_"
   class="st9"
   d="M420.4,356.5C420.4,356.5,420.4,356.5,420.4,356.5c-14.1,1.3-27.6,3.5-40.7,6.5  c-57.8,17.8-104.9,75.3-104.9,137l-28.4-3c-12.5-1.3-23.5,8.3-23.5,20.9c0,0.1,0,0.2,0,0.2c0,82.8,86.1,151.3,197.5,161.6  c32-44.1,51.2-100.4,51.2-161.6C471.6,456.8,452.4,400.6,420.4,356.5z" />
<path
   id="XMLID_60_"
   class="st10"
   d="M293.3,476.9c0,0,40.9,17.2,76.9,17.2s47.4-2.7,51.7-16.6c0,0,2.1,9.3-1.6,16.6  s-18.3,29.7-93.8,14.3c-75.4-10.9-47.6-8-47.6-8S277.2,471,293.3,476.9z" />
<path
   id="XMLID_34_"
   class="st10"
   d="M313.6,508.5c0,0,34,16.5,68.6,16.5c20.2,0,36.7-7.2,43.5-11.6c20.5-13.4,6.6-44.1,0-33.7  c-3.7,5.8-9.3,21.4-13,25.2s-55.9,8.5-62.5,8.5C343.6,513.4,313.6,508.5,313.6,508.5z" />
<g
   id="XMLID_177_">
	<path
   id="XMLID_179_"
   class="st10"
   d="M294.8,500c0-61.6,47.1-119.1,104.9-137c7.3-1.7,14.7-3.1,22.2-4.3c-0.5-0.8-1.1-1.5-1.6-2.3   c0,0,0,0-0.1,0c-14,1.3-27.6,3.5-40.7,6.6c-57.1,17.6-103.9,74-104.9,134.8L294.8,500z" />
	<path
   id="XMLID_180_"
   class="st10"
   d="M242.9,518.1c0-0.1,0-0.2,0-0.2c0-9.2,6-16.8,14-19.7l-10.6-1.1   c-12.5-1.3-23.5,8.3-23.5,20.9c0,0.1,0,0.2,0,0.2c0,82.8,86.1,151.3,197.5,161.6c0.5-0.8,1.1-1.5,1.6-2.3   C319.7,661.6,242.9,596.3,242.9,518.1z" />
</g>
<path
   id="XMLID_33_"
   class="st8"
   d="M420.4,356.5C420.4,356.5,420.4,356.5,420.4,356.5c-14.1,1.3-27.6,3.5-40.7,6.5  c-57.8,17.8-104.9,75.3-104.9,137l-28.4-3c-12.5-1.3-23.5,8.3-23.5,20.9c0,0.1,0,0.2,0,0.2c0,82.8,86.1,151.3,197.5,161.6  c32-44.1,51.2-100.4,51.2-161.6C471.6,456.8,452.4,400.6,420.4,356.5z" />
<path
   id="XMLID_24_"
   class="st11"
   d="M353.7,511.3c-6.3-0.6-12.1-1.3-17.3-2c-0.3-1.7-0.4-3.4-0.4-5.1c0-12.7,7.3-23.5,17.3-27.2  c-3,4.7-4.8,10.6-4.8,16.9C348.5,500.4,350.4,506.5,353.7,511.3z" />
<path
   id="XMLID_23_"
   class="st11"
   d="M288.5,503.1c-8.5-1.1-16.4-2.2-23.4-3.3c-0.3-2.3-0.4-4.6-0.4-7c0.5-17.3,10.7-31.7,24.5-36.5  c-4.3,6.3-7,14.2-7.2,22.9C281.8,488.1,284.2,496.4,288.5,503.1z" />
<path
   id="XMLID_30_"
   class="st8"
   d="M436,489.7c0-9.3-26-20-26-20" />
<ellipse
   id="XMLID_66_"
   class="st6"
   cx="350.2"
   cy="399.7"
   rx="40"
   ry="42" />
<ellipse
   id="XMLID_29_"
   class="st8"
   cx="350.2"
   cy="399.7"
   rx="40"
   ry="42" />
<ellipse
   id="XMLID_27_"
   class="st7"
   cx="347"
   cy="411.5"
   rx="16"
   ry="16.5" />
<ellipse
   id="XMLID_67_"
   class="st6"
   cx="343.1"
   cy="406"
   rx="7.1"
   ry="7.3" />
<ellipse
   id="XMLID_69_"
   class="st6"
   cx="351.1"
   cy="420.3"
   rx="2.6"
   ry="2.7" />
<path
   id="XMLID_26_"
   class="st11"
   d="M325.6,456.5c0.3-0.3,0.6-0.6,0.9-0.9c-4.2,2-8.3,4.7-11.8,7.9c-13.3,12.1-17.2,29-11.2,42.6  l17.6,2.4C310.3,492.3,311.5,471.2,325.6,456.5z" />
<path
   id="XMLID_25_"
   class="st11"
   d="M378.7,490.2c0.1-0.1,0.2-0.3,0.4-0.4c-1.8,1.1-3.5,2.4-4.9,4c-5.1,5.8-5.1,13.1-0.8,18.6  l8.8,0.1C375.3,506.1,373.5,497.2,378.7,490.2z" />
<path
   id="XMLID_92_"
   class="st1"
   d="M523,532.3c0,0-45.2,39.7-32.7,79.3c0,0,6.7,19.3,28.7,22.7c21.3,3.2,25.7-7,30.7-12  c-20.2-8.1-11.2-34.3-6.7-38.9c-9.3,0-20-7.9-23.8-16.4C515.5,558.5,517.6,550.4,523,532.3z" />
<path
   id="XMLID_160_"
   class="st4"
   d="M490.3,611.7c0,0,6.4,18.5,27.4,22.4c-10.2-24.3-14.5-54.8-16.3-76.1  C491.9,572.8,484.2,592.2,490.3,611.7z" />
<path
   id="XMLID_17_"
   class="st3"
   d="M523,532.3c0,0-45.2,39.7-32.7,79.3c0,0,6.7,19.3,28.7,22.7c21.3,3.2,25.7-7,30.7-12  c-20.2-8.1-11.2-34.3-6.7-38.9c-9.3,0-20-7.9-23.8-16.4C515.5,558.5,517.6,550.4,523,532.3z" />
<path
   id="XMLID_31_"
   class="st8"
   d="M425.7,479.7c0,0,1.2,37.6-52.7,33.4c-60.9-4.8-117.2-15.1-117.2-15.1" />
<circle
   id="XMLID_109_"
   class="st12"
   cx="343.8"
   cy="607.2"
   r="23.6" />
<circle
   id="XMLID_118_"
   class="st12"
   cx="402.6"
   cy="419.2"
   r="7.4" />
<circle
   id="XMLID_125_"
   class="st12"
   cx="421.2"
   cy="391.3"
   r="11.1" />
<circle
   id="XMLID_158_"
   class="st12"
   cx="412.7"
   cy="530.6"
   r="5.6" />
<circle
   id="XMLID_120_"
   class="st13"
   cx="422"
   cy="435.8"
   r="3.7" />
<circle
   id="XMLID_112_"
   class="st12"
   cx="518.9"
   cy="423"
   r="23.6" />
<circle
   id="XMLID_159_"
   class="st13"
   cx="591.9"
   cy="461.4"
   r="29.4" />
<circle
   id="XMLID_156_"
   class="st12"
   cx="645"
   cy="512.4"
   r="12" />
<circle
   id="XMLID_111_"
   class="st12"
   cx="395.3"
   cy="628.8"
   r="8.8" />
<path
   id="XMLID_162_"
   class="st5"
   d="M500,589.7c0,0,10-6.3,14.3-10.7" />
<path
   id="XMLID_164_"
   class="st5"
   d="M500,602.3c0,0,20-4.3,25-9.3" />
<path
   id="XMLID_168_"
   class="st5"
   d="M504.3,611.7c0,0,13.3,5.3,23.3,8.3" />
<path
   id="XMLID_170_"
   class="st5"
   d="M382.2,679.7c0,0,5.9,15.3,13,18.6" />
</svg>